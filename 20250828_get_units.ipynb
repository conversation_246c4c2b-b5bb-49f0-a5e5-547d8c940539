{"cells": [{"cell_type": "markdown", "id": "1894c465", "metadata": {}, "source": ["# Units and Cortex Assignment (Ctx only)\n", "\n", "This simplified notebook:\n", "- Loads units from Kilosort outputs\n", "- Assigns all units to cortex (Ctx)\n", "- Loads balloon on/off events and applies per-animal time filtering\n", "- Saves updates back to each recording's unit_summary.pkl\n", "\n", "Removed: trajectory extraction/processing and region-based statistics."]}, {"cell_type": "markdown", "id": "f0e41593", "metadata": {}, "source": ["## Table of contents\n", "1. Configuration\n", "2. Helpers\n", "3. Step 1 — Extract units and write unit_summary.pkl\n", "4. Step 2 — Assign cortex region (Ctx) to all units\n", "5. Step 3 — Load and filter balloon events (per animal)\n", "6. Verification — Per-animal summary after filtering"]}, {"cell_type": "code", "execution_count": 1, "id": "81b2ecde", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[32;1mnpyx[c4] version 4.1.3 imported.\u001b[0m\n"]}], "source": ["# Imports\n", "from pathlib import Path\n", "from typing import List, Dict, Optional\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# External utility for extracting units from a Kilosort folder\n", "from npx_utils import extract_unit_data\n"]}, {"cell_type": "markdown", "id": "112e1fbf", "metadata": {}, "source": ["## 1) Configuration\n", "Edit dataset paths and IDs for your environment. The time limits control balloon-event filtering per animal."]}, {"cell_type": "code", "execution_count": 2, "id": "ae52465e", "metadata": {}, "outputs": [], "source": ["from typing import Dict, List\n", "from pathlib import Path\n", "\n", "# Time limits (seconds) for filtering balloon events per animal\n", "tmax_dict: Dict[str, float] = {\"b24\": 2000.0, \"b25\": 2000.0, \"b27\":4000, \"b28\": 4000.0}\n", "\n", "# Kilosort dataset folders\n", "dataset_paths: List[Path] = [\n", "    Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b24\\\\b24_p1_r1_g0\\\\b24_p1_r1_g0_imec0\"),\n", "    Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b25\\\\b25_p1_r2_g0\\\\b25_p1_r2_g0_imec0\"),\n", "    Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b27\\\\b27_p1_r1_g0\\\\catgt\"),\n", "    Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b28\\\\b28_p1_r2_g0\\\\catgt\"),\n", "]\n", "\n", "# Recording ID to animal ID mapping\n", "recording_to_animal_id: Dict[str, str] = {\n", "    \"b24_p1_r1_g0\": \"b24\",\n", "    \"b25_p1_r2_g0\": \"b25\",\n", "    \"b27_p1_r1_g0\": \"b27\",\n", "    \"b28_p1_r2_g0\": \"b28\",\n", "}\n", "\n", "# Firing-rate filter (Hz)\n", "MIN_FIRING_RATE: float = 0.05\n"]}, {"cell_type": "markdown", "id": "b5f8c923", "metadata": {}, "source": ["## 2) Step 1 — Extract units and write unit_summary.pkl\n", "This runs extract_unit_data per dataset, applies a minimum firing-rate filter, and saves unit_summary.pkl."]}, {"cell_type": "code", "execution_count": null, "id": "846835b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Step 1] Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0 (rid=b24_p1_r1_g0, animal=b24)\n", "[ 35/35] 100.00% | Unit 41 | ETC: 00:005\n", "✅ Done.\n", "  ✔ Saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\\unit_summary.pkl\n", "[Step 1] Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0 (rid=b25_p1_r2_g0, animal=b25)\n", "[ 14/14] 100.00% | Unit 167 | ETC: 00:00\n", "✅ Done.\n", "  ✔ Saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\\unit_summary.pkl\n", "[Step 1] Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0\\catgt (rid=b27_p1_r1_g0, animal=b27)\n", "[ 54/54] 100.00% | Unit 561 | ETC: 00:00\n", "✅ Done.\n", "  ✔ Saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0\\catgt\\unit_summary.pkl\n", "[Step 1] Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p1_r2_g0\\catgt (rid=b28_p1_r2_g0, animal=b28)\n", "[ 12/30]  40.00% | Unit 331 | ETC: 00:50"]}], "source": ["from npx_utils import extract_unit_data\n", "\n", "for dataset_path in dataset_paths:\n", "    rid = dataset_path.parent.name\n", "    animal_id = recording_to_animal_id.get(rid, \"\")\n", "    print(f\"[Step 1] Processing units: {dataset_path} (rid={rid}, animal={animal_id})\")\n", "    try:\n", "        df = extract_unit_data(datapath=str(dataset_path), animal_id=animal_id, quality=['good', 'mua'])\n", "        df = df[df['mean_firing_rate'] > MIN_FIRING_RATE]\n", "        out_pkl = dataset_path / 'unit_summary.pkl'\n", "        df.to_pickle(out_pkl)\n", "        print(f\"  ✔ Saved: {out_pkl}\")\n", "    except Exception as exc:\n", "        print(f\"  ❌ Failed {dataset_path}: {exc}\")"]}, {"cell_type": "markdown", "id": "8aa0178e", "metadata": {}, "source": ["## 3) Step 2 — Assign cortex region (Ctx) to all units\n", "For each recording, set three columns on the unit DataFrame and persist back to unit_summary.pkl: region_name='Cortex', region_acronym='Ctx', brain_region='Ctx'."]}, {"cell_type": "code", "execution_count": null, "id": "55101a0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  ✔ Updated and saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\\unit_summary.pkl\n", "  ✔ Updated and saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\\unit_summary.pkl\n", "  ✔ Updated and saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0\\catgt\\unit_summary.pkl\n", "  ✔ Updated and saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p1_r2_g0\\catgt\\unit_summary.pkl\n"]}], "source": ["import pandas as pd\n", "\n", "for dataset_path in dataset_paths:\n", "    out_pkl = dataset_path / 'unit_summary.pkl'\n", "    if not out_pkl.exists():\n", "        print(f\"[Step 2] ⚠ Missing {out_pkl} — run Step 1 first.\")\n", "        continue\n", "    try:\n", "        df = pd.read_pickle(out_pkl)\n", "        df['region_name'] = 'Cortex'\n", "        df['region_acronym'] = 'Ctx'\n", "        df['brain_region'] = 'Ctx'\n", "        df.to_pickle(out_pkl)\n", "        print(f\"  ✔ Updated and saved: {out_pkl}\")\n", "    except Exception as exc:\n", "        print(f\"[Step 2] ❌ Failed {dataset_path}: {exc}\")\n"]}, {"cell_type": "markdown", "id": "19b03f4f", "metadata": {}, "source": ["## 4) Step 3 — Load and filter balloon events (per animal)\n", "Reads bal_on_sec.txt and bal_off_sec.txt from the recording folder, filters by tmax_dict per animal, and stores filtered events in unit_summary.pkl."]}, {"cell_type": "code", "execution_count": null, "id": "31d68f03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Step 3] b24_p1_r1_g0 (b24)\n", "  tmax = 2000.0\n", "  Filtered events: 10 -> 4\n", "  ✔ Saved balloon events in unit_summary.pkl\n", "[Step 3] b25_p1_r2_g0 (b25)\n", "  tmax = 2400.0\n", "  Filtered events: 11 -> 6\n", "  ✔ Saved balloon events in unit_summary.pkl\n", "[Step 3] b27_p1_r1_g0 (b27)\n", "  tmax = 4000.0\n", "  Filtered events: 16 -> 5\n", "  ✔ Saved balloon events in unit_summary.pkl\n", "[Step 3] b28_p1_r2_g0 (b28)\n", "  tmax = 4000.0\n", "  Filtered events: 5 -> 4\n", "  ✔ Saved balloon events in unit_summary.pkl\n"]}], "source": ["for dataset_path in dataset_paths:\n", "    rid = dataset_path.parent.name\n", "    animal_id = recording_to_animal_id.get(rid, \"\")\n", "    basepath = dataset_path.parent\n", "    on_path = basepath / 'bal_on_sec.txt'\n", "    off_path = basepath / 'bal_off_sec.txt'\n", "    out_pkl = dataset_path / 'unit_summary.pkl'\n", "\n", "    status = f\"[Step 3] {rid} ({animal_id})\"\n", "\n", "    if not animal_id:\n", "        print(f\"{status} ❌ Animal ID not found. Aborting.\")\n", "        raise SystemExit(1)\n", "\n", "    if not on_path.exists() or not off_path.exists():\n", "        print(f\"{status} ⚠ Missing balloon files\")\n", "        continue\n", "    if not out_pkl.exists():\n", "        print(f\"{status} ⚠ Missing {out_pkl.name} — run Step 1 first.\")\n", "        continue\n", "\n", "    try:\n", "        bal_on = np.atleast_2d(np.loadtxt(on_path, delimiter='\\t'))\n", "        bal_off = np.atleast_2d(np.loadtxt(off_path, delimiter='\\t'))\n", "        before = len(bal_on)\n", "\n", "        tmax = float(tmax_dict.get(animal_id, -1))\n", "        if tmax > 0:\n", "            keep = (bal_on[:,0] < tmax) & (bal_on[:,1] < tmax) & \\\n", "                   (bal_off[:,0] < tmax) & (bal_off[:,1] < tmax)\n", "            bal_on, bal_off = bal_on[keep], bal_off[keep]\n", "\n", "        after = len(bal_on)\n", "\n", "        df = pd.read_pickle(out_pkl)\n", "        df['balloon_on_sec']  = [bal_on.tolist()] * len(df)\n", "        df['balloon_off_sec'] = [bal_off.tolist()] * len(df)\n", "        df.to_pickle(out_pkl)\n", "\n", "        print(f\"{status} ✔ Saved {before}->{after} events in {out_pkl.name} (tmax={tmax})\")\n", "\n", "    except Exception as exc:\n", "        print(f\"{status} ❌ Balloon processing failed: {exc}\")\n"]}, {"cell_type": "markdown", "id": "fdda0249", "metadata": {}, "source": ["## 5) Verification — Per-animal summary after filtering\n", "Displays per-recording counts of units and balloon trials to validate that filtering worked as expected."]}, {"cell_type": "code", "execution_count": null, "id": "5c23ce4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Unit Summary ===\n", "\n", "|    | recording_id   | animal_id   |   units |   good_units |   mua_units |   balloon_on |   balloon_off |\n", "|----|----------------|-------------|---------|--------------|-------------|--------------|---------------|\n", "|  0 | b24_p1_r1_g0   | b24         |      35 |           23 |          12 |            4 |             4 |\n", "|  1 | b25_p1_r2_g0   | b25         |      14 |           14 |           0 |            6 |             6 |\n", "|  2 | b27_p1_r1_g0   | b27         |      54 |           43 |          11 |            5 |             5 |\n", "|  3 | b28_p1_r2_g0   | b28         |      30 |           24 |           6 |            4 |             4 |\n"]}], "source": ["summary_rows = []\n", "for dataset_path in dataset_paths:\n", "    rid = dataset_path.parent.name\n", "    animal_id = recording_to_animal_id.get(rid, \"\")\n", "    out_pkl = dataset_path / \"unit_summary.pkl\"\n", "    if not out_pkl.exists():\n", "        continue\n", "\n", "    try:\n", "        df = pd.read_pickle(out_pkl)\n", "\n", "        summary_rows.append({\n", "            \"recording_id\": rid,\n", "            \"animal_id\": animal_id,\n", "            \"units\": len(df),\n", "            \"good_units\": int((df.get(\"quality\") == \"good\").sum()) if \"quality\" in df else None,\n", "            \"mua_units\": int((df.get(\"quality\") == \"mua\").sum()) if \"quality\" in df else None,\n", "            \"balloon_on\": len(df.get(\"balloon_on_sec\", [[]])[0]) if \"balloon_on_sec\" in df else 0,\n", "            \"balloon_off\": len(df.get(\"balloon_off_sec\", [[]])[0]) if \"balloon_off_sec\" in df else 0,\n", "        })\n", "    except Exception as exc:\n", "        print(f\"  ⚠ Summary failed for {rid}: {exc}\")\n", "\n", "\n", "summary = pd.DataFrame(summary_rows)\n", "\n", "from tabulate import tabulate\n", "\n", "if not summary.empty:\n", "    print(\"\\n=== Unit Summary ===\\n\")\n", "    print(tabulate(summary, headers=\"keys\", tablefmt=\"github\", showindex=True))\n", "else:\n", "    print(\"No summaries available — check dataset_paths.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}