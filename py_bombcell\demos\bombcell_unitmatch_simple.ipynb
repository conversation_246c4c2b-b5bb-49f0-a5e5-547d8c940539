{"cells": [{"cell_type": "markdown", "metadata": {}, "source": "# Complete BombCell + UnitMatch Pipeline Demo\n\nThis notebook demonstrates the complete pipeline for neural spike analysis using BombCell and UnitMatch:\n\n## 🎯 **What This Demo Does**\n\n### Part 1: BombCell Quality Control\n- Analyzes spike sorting data from Kilosort\n- Extracts comprehensive quality metrics for each unit\n- Classifies units as \"GOOD\", \"MUA\", \"NOISE\", or \"NON-SOMA\"  \n- **Creates waveform data specifically formatted for UnitMatch**\n\n### Part 2: UnitMatch Cross-Session Tracking\n- Uses BombCell outputs to track neurons across recording sessions\n- Applies machine learning to match units based on waveform features\n- Provides interactive tools for manual curation\n- Generates probability matrices showing match confidence\n\n## 🔗 **Key Integration Points**\n- BombCell creates `RawWaveforms/` folders that UnitMatch requires\n- Special parameters optimize BombCell for UnitMatch compatibility\n- Quality classifications help filter units for reliable matching\n- Both tools work seamlessly with standard Kilosort outputs\n\n## 📋 **What You Need**\n- Kilosort output directories from 2+ recording sessions if you want to track across days or for 1 recording session if you want to merge units intra-session\n- Access to raw `.bin` files (for waveform extraction) - can be compressed `.cbin` files\n- Meta files (`.meta` for SpikeGLX or `.oebin` for Open Ephys)\n\n## 💡 **Important for Compressed Data**\nIf your data is in compressed format (e.g., `.cbin` files from mtscomp), the pipeline will automatically handle decompression:\n- BombCell's UnitMatch functions use `decompress_data=True` by default\n- Install the mtscomp package: `pip install mtscomp`\n- This enables automatic decompression during waveform extraction\n\n## 🚀 **Getting Started**\nSimply run the cells below to analyze your own data - just update the file paths in Part 1!"}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ ipywidgets available - interactive GUI ready\n", "✅ BombCell and UnitMatch imported successfully\n", "🚀 Ready to analyze neural data!\n"]}], "source": ["%load_ext autoreload\n", "%autoreload \n", "\n", "# 📦 Import Required Libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import BombCell for quality control\n", "import bombcell as bc\n", "\n", "# Import UnitMatch for cross-session tracking (optional - only if running Part 2)\n", "try:\n", "    import UnitMatchPy.bayes_functions as bf\n", "    import UnitMatchPy.utils as util\n", "    import UnitMatchPy.overlord as ov\n", "    import UnitMatchPy.save_utils as su\n", "    import UnitMatchPy.GUI as um_gui\n", "    import UnitMatchPy.assign_unique_id as aid\n", "    import UnitMatchPy.default_params as default_params\n", "    UNITMATCH_AVAILABLE = True\n", "    print(\"✅ BombCell and UnitMatch imported successfully\")\n", "except ImportError as e:\n", "    UNITMATCH_AVAILABLE = False\n", "    print(\"✅ BombCell imported successfully\")\n", "    print(\"⚠️  UnitMatch not available - please install: pip install UnitMatchPy\")\n", "    print(f\"    Error: {e}\")\n", "\n", "print(\"🚀 Ready to analyze neural data!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📁 Part 1: Configure Data Paths for BombCell\n", "\n", "**Edit these paths to point to your actual data:**\n", "\n", "For this demo, we'll analyze data from multiple sessions to demonstrate the complete pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 BombCell + UnitMatch Pipeline Demo\n", "📊 Dataset: calca_302 cross-session tracking\n", "🔬 Sessions to analyze: 2\n", "   Session 1: calca_302_2023-04-19\n", "      📁 KS: kilosort4\n", "   Session 2: calca_302_2023-04-20\n", "      📁 KS: kilosort4\n", "\n", "🔧 Kilosort version: 4\n", "🎯 This demo will run BombCell first, then use outputs for UnitMatch tracking\n"]}], "source": ["# 📁 Configure Data Paths - Using Processing Playground Dataset\n", "\n", "# These are the exact paths from the processing_playground for testing\n", "# calca_302 sessions from 2023-04-19 and 2023-04-20\n", "\n", "session_configs = [\n", "    {\n", "        'name': 'calca_302_2023-04-19',\n", "        'ks_dir': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/kilosort4',\n", "        'raw_file': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/cz_npxl_g0_t0.imec0.ap.bin',\n", "        'meta_file': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/cz_npxl_g0_t0.imec0.ap.meta',\n", "        'save_path': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf'\n", "    },\n", "    {\n", "        'name': 'calca_302_2023-04-20', \n", "        'ks_dir': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/kilosort4',\n", "        'raw_file': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/cz_npxl_g0_t0.imec0.ap.bin',\n", "        'meta_file': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/cz_npxl_g0_t0.imec0.ap.meta',\n", "        'save_path': r'/home/<USER>/cup/<PERSON>/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf'\n", "    }\n", "]\n", "\n", "kilosort_version = 4\n", "\n", "print(\"🎯 BombCell + UnitMatch Pipeline Demo\")\n", "print(\"📊 Dataset: calca_302 cross-session tracking\")\n", "print(f\"🔬 Sessions to analyze: {len(session_configs)}\")\n", "for i, config in enumerate(session_configs):\n", "    print(f\"   Session {i+1}: {config['name']}\")\n", "    print(f\"      📁 KS: {Path(config['ks_dir']).name}\")\n", "    \n", "print(f\"\\n🔧 Kilosort version: {kilosort_version}\")\n", "print(\"🎯 This demo will run BombCell first, then use outputs for UnitMatch tracking\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 Part 1: Run BombCell Quality Control\n", "\n", "**What BombCell does:**\n", "- Analyzes each unit's waveform properties, firing patterns, and spatial characteristics\n", "- Calculates 15+ quality metrics (amplitude, drift, contamination, etc.)\n", "- Classifies units into categories based on quality thresholds\n", "- **Special for UnitMatch**: Extracts 1000 raw spikes per unit (vs. standard 100)\n", "\n", "**Key UnitMatch optimizations:**\n", "- `detrendWaveform=False`: Preserves raw waveform shape for matching\n", "- `nRawSpikesToExtract=1000`: More spikes = better cross-validation\n", "- `saveMultipleRaw=True`: Saves separate waveform sets for validation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Running BombCell analysis on all sessions...\n", "\n", "============================================================\n", "SESSION 1/2\n", "============================================================\n", "🔬 Analyzing session: calca_302_2023-04-19\n", "   📁 Kilosort directory: /home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/kilosort4\n", "   🗂️  Raw file: cz_npxl_g0_t0.imec0.ap.bin\n", "   📄 Meta file: cz_npxl_g0_t0.imec0.ap.meta\n", "   💾 Results will be saved to: /home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf\n", "   ✅ Found existing BombCell results - loading from disk\n", "   📊 Loaded results - Total units: 1\n", "   🎯 UnitMatch waveforms: 657 files saved\n", "\n", "============================================================\n", "SESSION 2/2\n", "============================================================\n", "🔬 Analyzing session: calca_302_2023-04-20\n", "   📁 Kilosort directory: /home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/kilosort4\n", "   🗂️  Raw file: cz_npxl_g0_t0.imec0.ap.bin\n", "   📄 Meta file: cz_npxl_g0_t0.imec0.ap.meta\n", "   💾 Results will be saved to: /home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf\n", "   ✅ Found existing BombCell results - loading from disk\n", "   📊 Loaded results - Total units: 1\n", "   🎯 UnitMatch waveforms: 644 files saved\n", "\n", "🎉 BombCell analysis complete for 2 sessions!\n"]}], "source": ["def run_bombcell_session(session_config):\n", "    \"\"\"<PERSON> BombCell on a single session with UnitMatch parameters\"\"\"\n", "    \n", "    name = session_config['name']\n", "    ks_dir = session_config['ks_dir']\n", "    raw_file = session_config.get('raw_file')\n", "    meta_file = session_config.get('meta_file')\n", "    \n", "    # Create save path in the kilosort directory\n", "    save_path = Path(session_config['save_path'])\n", "    \n", "    print(f\"🔬 Analyzing session: {name}\")\n", "    print(f\"   📁 Kilosort directory: {ks_dir}\")\n", "    print(f\"   🗂️  Raw file: {Path(raw_file).name if raw_file else 'Not specified'}\")\n", "    print(f\"   📄 Meta file: {Path(meta_file).name if meta_file else 'Not specified'}\")\n", "    print(f\"   💾 Results will be saved to: {save_path}\")\n", "    \n", "    # Check if BombCell has already been run\n", "    existing_results = save_path / \"cluster_bc_unitType.tsv\"\n", "    if existing_results.exists():\n", "        print(f\"   ✅ Found existing BombCell results - loading from disk\")\n", "        \n", "        # Load existing results\n", "        try:\n", "            param, quality_metrics, unit_type_string = bc.load_bc_results(str(save_path))\n", "            unit_type = np.array([1 if ut == 'GOOD' else 0 for ut in unit_type_string])\n", "            \n", "            print(f\"   📊 Loaded results - Total units: {len(unit_type)}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"   ⚠️  Error loading existing results: {e}\")\n", "            print(f\"   🔄 Will re-run BombCell analysis...\")\n", "            existing_results = None\n", "    else:\n", "        existing_results = None\n", "    \n", "    # Run BombCell if no existing results or loading failed\n", "    if not existing_results or not existing_results.exists():\n", "        print(\"   🚀 Running BombCell analysis...\")\n", "        quality_metrics, param, unit_type, unit_type_string = bc.run_bombcell_unit_match(\n", "            ks_dir=ks_dir,\n", "            save_path=str(save_path),\n", "            raw_file=raw_file,\n", "            meta_file=meta_file,\n", "            kilosort_version=kilosort_version\n", "        )\n", "        \n", "        print(f\"   ✅ Analysis complete!\")\n", "        print(f\"   📊 Total units: {len(unit_type)}\")\n", "\n", "    \n", "    # Check UnitMatch waveforms\n", "    raw_waveforms_dir = save_path / \"RawWaveforms\"\n", "    if raw_waveforms_dir.exists():\n", "        waveform_files = list(raw_waveforms_dir.glob(\"Unit*_RawSpikes.npy\"))\n", "        print(f\"   🎯 UnitMatch waveforms: {len(waveform_files)} files saved\")\n", "    else:\n", "        print(f\"   ⚠️  No UnitMatch waveforms - check raw file access\")\n", "    \n", "    return {\n", "        'name': name,\n", "        'ks_dir': ks_dir,\n", "        'save_path': str(save_path),\n", "        'quality_metrics': quality_metrics,\n", "        'param': param,\n", "        'unit_type': unit_type,\n", "        'unit_type_string': unit_type_string\n", "    }\n", "\n", "# <PERSON> on all sessions\n", "print(\"🚀 Running BombCell analysis on all sessions...\")\n", "session_results = []\n", "\n", "for i, session_config in enumerate(session_configs):\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"SESSION {i+1}/{len(session_configs)}\")\n", "    print(f\"{'='*60}\")\n", "    result = run_bombcell_session(session_config)\n", "    session_results.append(result)\n", "\n", "print(f\"\\n🎉 BombCell analysis complete for {len(session_results)} sessions!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Part 2: UnitMatch Cross-Session Tracking\n", "\n", "**What UnitMatch does:**\n", "- Compares units across different recording sessions using multiple features\n", "- Uses machine learning (Na<PERSON>) to calculate match probabilities  \n", "- Identifies the same neurons recorded on different days\n", "- Provides tools for manual curation and validation\n", "\n", "**This section requires multiple sessions with BombCell outputs!**"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Setting up UnitMatch for cross-session tracking...\n", "📁 Kilosort directories: 2\n", "   Session 1: kilosort4\n", "   Session 2: kilosort4\n", "📊 BombCell unit classifications:\n", "   Session 1: ✅ cluster_bc_unitType.tsv\n", "   Session 2: ✅ cluster_bc_unitType.tsv\n", "🎯 Raw waveforms for UnitMatch:\n", "   Session 1: ✅ 657 waveform files\n", "   Session 2: ✅ 644 waveform files\n", "✅ UnitMatch paths configured successfully\n"]}], "source": ["# 🔗 Setup UnitMatch Parameters and Paths\n", "\n", "print(\"🎯 Setting up UnitMatch for cross-session tracking...\")\n", "\n", "# Get default UnitMatch parameters\n", "um_param = default_params.get_default_param()\n", "\n", "# Set up paths from our BombCell results\n", "KS_dirs = [result['ks_dir'] for result in session_results]\n", "um_param['KS_dirs'] = KS_dirs\n", "\n", "# Set up BombCell paths - using exact paths from processing_playground\n", "custom_bombcell_paths = [\n", "    r'/home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf/cluster_bc_unitType.tsv',\n", "    r'/home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf/cluster_bc_unitType.tsv'\n", "]\n", "\n", "custom_raw_waveform_paths = [\n", "    r'/home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf/RawWaveforms',\n", "    r'/home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-20/cz_npxl_g0/cz_npxl_g0_imec0/bombcell_testing_jf/RawWaveforms'\n", "]\n", "\n", "print(f\"📁 Kilosort directories: {len(KS_dirs)}\")\n", "for i, ks_dir in enumerate(KS_dirs):\n", "    print(f\"   Session {i+1}: {Path(ks_dir).name}\")\n", "\n", "print(f\"📊 BombCell unit classifications:\")\n", "for i, bc_path in enumerate(custom_bombcell_paths):\n", "    exists = \"✅\" if Path(bc_path).exists() else \"❌\"\n", "    print(f\"   Session {i+1}: {exists} {Path(bc_path).name}\")\n", "    \n", "print(f\"🎯 Raw waveforms for UnitMatch:\")\n", "for i, wv_path in enumerate(custom_raw_waveform_paths):\n", "    exists = \"✅\" if Path(wv_path).exists() else \"❌\"\n", "    n_files = len(list(Path(wv_path).glob(\"Unit*_RawSpikes.npy\"))) if Path(wv_path).exists() else 0\n", "    print(f\"   Session {i+1}: {exists} {n_files} waveform files\")\n", "\n", "# Setup UnitMatch paths - this matches exactly what processing_playground does\n", "try:\n", "    wave_paths, unit_label_paths, channel_pos = util.paths_from_KS(\n", "        KS_dirs, \n", "        custom_raw_waveform_paths=custom_raw_waveform_paths,\n", "        custom_bombcell_paths=custom_bombcell_paths\n", "    )\n", "    \n", "    um_param = util.get_probe_geometry(channel_pos[0], um_param)\n", "    print(\"✅ UnitMatch paths configured successfully\")\n", "    UNITMATCH_READY = True\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error setting up UnitMatch paths: {e}\")\n", "    print(\"   Make sure BombCell has been run and waveforms extracted\")\n", "    UNITMATCH_READY = False"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Running UnitMatch cross-session analysis...\n", "📊 Step 0: Loading unit waveforms...\n", "   ✅ Loaded 305 good units across 2 sessions\n", "   📏 Waveform shape: (305, 61, 384, 2) (units × channels × time)\n", "🔍 Step 1: Extracting waveform features...\n", "   ✅ Extracted amplitude, spatial decay, and waveform features\n", "📐 Steps 2-4: Computing similarity metrics and drift correction...\n", "   ✅ Found 581 candidate unit pairs\n", "   📊 Metrics included: ['amp_score', 'spatial_decay_score', 'centroid_overlord_score', 'centroid_dist', 'waveform_score', 'trajectory_score']\n", "🧮 Step 5: Calculating match probabilities...\n", "Calculating the probability distributions of the metric scores\n", "Calculating the match probabilities\n", "   ✅ Calculated probability matrix: (305, 305)\n", "   📈 Max probability: 1.000\n", "   📊 Mean probability: 0.006\n"]}], "source": ["# 🧠 Run UnitMatch Analysis Pipeline\n", "\n", "\n", "print(\"🚀 Running UnitMatch cross-session analysis...\")\n", "\n", "# Step 0: Load good units and waveform data\n", "print(\"📊 Step 0: Loading unit waveforms...\")\n", "waveform, session_id, session_switch, within_session, good_units, um_param = util.load_good_waveforms(\n", "    wave_paths, unit_label_paths, um_param, good_units_only=True\n", ") \n", "\n", "print(f\"   ✅ Loaded {len(np.concatenate(good_units))} good units across {len(session_results)} sessions\")\n", "print(f\"   📏 Waveform shape: {waveform.shape} (units × channels × time)\")\n", "\n", "# Create cluster info\n", "clus_info = {\n", "    'good_units': good_units, \n", "    'session_switch': session_switch, \n", "    'session_id': session_id,\n", "    'original_ids': np.concatenate(good_units)\n", "}\n", "\n", "# Step 1: Extract waveform parameters\n", "print(\"🔍 Step 1: Extracting waveform features...\")\n", "extracted_wave_properties = ov.extract_parameters(waveform, channel_pos, clus_info, um_param)\n", "print(\"   ✅ Extracted amplitude, spatial decay, and waveform features\")\n", "\n", "# Steps 2-4: Calculate similarity metrics with drift correction\n", "print(\"📐 Steps 2-4: Computing similarity metrics and drift correction...\")\n", "total_score, candidate_pairs, scores_to_include, predictors = ov.extract_metric_scores(\n", "    extracted_wave_properties, session_switch, within_session, um_param, niter=2\n", ")\n", "print(f\"   ✅ Found {np.sum(candidate_pairs)} candidate unit pairs\")\n", "print(f\"   📊 Metrics included: {list(scores_to_include.keys())}\")\n", "\n", "# Step 5: Probability analysis using <PERSON><PERSON>\n", "print(\"🧮 Step 5: Calculating match probabilities...\")\n", "\n", "# Set up priors\n", "prior_match = 1 - (um_param['n_expected_matches'] / um_param['n_units']**2)\n", "priors = np.array([prior_match, 1 - prior_match])\n", "\n", "# Train Naive Bayes classifier\n", "labels = candidate_pairs.astype(int)\n", "cond = np.unique(labels)\n", "parameter_kernels = bf.get_parameter_kernels(scores_to_include, labels, cond, um_param, add_one=1)\n", "\n", "# Calculate match probabilities\n", "probability = bf.apply_naive_bayes(parameter_kernels, priors, predictors, um_param, cond)\n", "output_prob_matrix = probability[:, 1].reshape(um_param['n_units'], um_param['n_units'])\n", "\n", "print(f\"   ✅ Calculated probability matrix: {output_prob_matrix.shape}\")\n", "print(f\"   📈 Max probability: {np.max(output_prob_matrix):.3f}\")\n", "print(f\"   📊 Mean probability: {np.mean(output_prob_matrix):.3f}\")\n", "    \n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 Analyzing UnitMatch results...\n", "The percentage of units matched to themselves is: 79.01639344262294%\n", "The percentage of false -ve's then is: 20.98360655737706% \n", "\n", "The rate of miss-match(es) per expected match 0.15081967213114755\n", "The percentage of false +ve's is 0.1086443079829948% for session 1\n", "The percentage of false +ve's is 0.09155322028500916% for session 2\n", "\n", "This assumes that the spike sorter has made no mistakes\n", "\n", "🎯 Match Summary (threshold = 0.5):\n", "   🔗 Total matches found: 475.0\n", "   📍 Within-session matches: 188.0\n", "   🌉 Cross-session matches: 287.0\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📊 Red dashed lines show session boundaries\n", "🎯 Diagonal represents within-session matches\n", "🌉 Off-diagonal represents cross-session matches\n"]}], "source": ["# 📊 Analyze UnitMatch Results\n", "\n", "\n", "print(\"📈 Analyzing UnitMatch results...\")\n", "\n", "# Evaluate matching performance\n", "match_threshold = um_param.get('match_threshold', 0.75)\n", "util.evaluate_output(output_prob_matrix, um_param, within_session, session_switch, \n", "                    match_threshold=match_threshold)\n", "\n", "# Create binary match matrix\n", "output_threshold = np.zeros_like(output_prob_matrix)\n", "output_threshold[output_prob_matrix > match_threshold] = 1\n", "\n", "# Count matches\n", "total_matches = np.sum(output_threshold)\n", "within_session_matches = np.sum(output_threshold * within_session)\n", "cross_session_matches = total_matches - within_session_matches\n", "\n", "print(f\"\\n🎯 Match Summary (threshold = {match_threshold}):\")\n", "print(f\"   🔗 Total matches found: {total_matches}\")\n", "print(f\"   📍 Within-session matches: {within_session_matches}\")\n", "print(f\"   🌉 Cross-session matches: {cross_session_matches}\")\n", "\n", "# Visualize probability matrix\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Probability matrix\n", "im1 = axes[0].imshow(output_prob_matrix, cmap='viridis', aspect='auto')\n", "axes[0].set_title('Unit Match Probability Matrix')\n", "axes[0].set_xlabel('Unit Index')\n", "axes[0].set_ylabel('Unit Index')\n", "plt.colorbar(im1, ax=axes[0], label='Match Probability')\n", "\n", "# Session boundaries\n", "n_units_cumsum = np.cumsum([0] + [len(units) for units in good_units])\n", "for boundary in n_units_cumsum[1:-1]:\n", "    axes[0].axhline(boundary, color='red', linestyle='--', alpha=0.7)\n", "    axes[0].axvline(boundary, color='red', linestyle='--', alpha=0.7)\n", "\n", "# Binary matches\n", "im2 = axes[1].imshow(output_threshold, cmap='Greys', aspect='auto')\n", "axes[1].set_title(f'Matches Above Threshold ({match_threshold})')\n", "axes[1].set_xlabel('Unit Index')\n", "axes[1].set_ylabel('Unit Index')\n", "\n", "# Session boundaries\n", "for boundary in n_units_cumsum[1:-1]:\n", "    axes[1].axhline(boundary, color='red', linestyle='--', alpha=0.7)\n", "    axes[1].axvline(boundary, color='red', linestyle='--', alpha=0.7)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 Red dashed lines show session boundaries\")\n", "print(\"🎯 Diagonal represents within-session matches\")\n", "print(\"🌉 Off-diagonal represents cross-session matches\")\n", "    \n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎮 Setting up interactive curation tools...\n", "✅ GUI data prepared successfully\n", "\n", "🎯 To launch interactive curation:\n", "   Run: is_match, not_match, matches_GUI = um_gui.run_GUI()\n", "\n", "🔍 The GUI provides:\n", "   - Interactive visualization of unit pairs\n", "   - Side-by-side waveform comparisons\n", "   - Quality metric displays\n", "   - Manual accept/reject controls\n", "   - Real-time match probability updates\n", "\n", "💡 Curation workflow:\n", "   1. GUI shows potential matches above threshold\n", "   2. Review waveform similarity and spatial locations\n", "   3. Accept good matches, reject false positives\n", "   4. Use curated results for final unit tracking\n", "Pre-calculating autocorrelograms for all units...\n", "Processed 50/305 units\n", "Processed 100/305 units\n", "Processed 150/305 units\n", "Processed 200/305 units\n", "Processed 250/305 units\n", "Processed 300/305 units\n", "ACG cache saved to my_acgs.pkl\n"]}], "source": ["# 🎮 Interactive Manual Curation with UnitMatch GUI\n", "\n", "\n", "print(\"🎮 Setting up interactive curation tools...\")\n", "\n", "# Prepare data for GUI - extract all the required variables\n", "amplitude = extracted_wave_properties['amplitude']\n", "spatial_decay = extracted_wave_properties['spatial_decay']\n", "avg_centroid = extracted_wave_properties['avg_centroid']\n", "avg_waveform = extracted_wave_properties['avg_waveform']\n", "avg_waveform_per_tp = extracted_wave_properties['avg_waveform_per_tp']\n", "wave_idx = extracted_wave_properties['good_wave_idxs']\n", "max_site = extracted_wave_properties['max_site']\n", "max_site_mean = extracted_wave_properties['max_site_mean']\n", "\n", "# Process info for GUI\n", "um_gui.process_info_for_GUI(\n", "    output_prob_matrix, match_threshold, scores_to_include, total_score, \n", "    amplitude, spatial_decay, avg_centroid, avg_waveform, avg_waveform_per_tp, \n", "    wave_idx, max_site, max_site_mean, waveform, within_session, \n", "    channel_pos, clus_info, um_param\n", ")\n", "\n", "print(\"✅ GUI data prepared successfully\")\n", "print(\"\\n🎯 To launch interactive curation:\")\n", "print(\"   Run: is_match, not_match, matches_GUI = um_gui.run_GUI()\")\n", "print(\"\\n🔍 The GUI provides:\")\n", "print(\"   - Interactive visualization of unit pairs\")\n", "print(\"   - Side-by-side waveform comparisons\")\n", "print(\"   - Quality metric displays\")\n", "print(\"   - Manual accept/reject controls\")\n", "print(\"   - Real-time match probability updates\")\n", "\n", "print(\"\\n💡 Curation workflow:\")\n", "print(\"   1. GUI shows potential matches above threshold\")\n", "print(\"   2. Review waveform similarity and spatial locations\")\n", "print(\"   3. Accept good matches, reject false positives\")\n", "print(\"   4. Use curated results for final unit tracking\")\n", "\n", "# Before running the GUI\n", "from UnitMatchPy.GUI import precalculate_all_acgs\n", "\n", "# Pre-calculate and save ACGs\n", "acg_cache = precalculate_all_acgs(clus_info, um_param,\n", "save_path='my_acgs.pkl')\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Could not load ACG cache from acg_cache.pkl: [Errno 2] No such file or directory: 'acg_cache.pkl'\n", "Icon path: /home/<USER>/Dropbox/Python/UnitMatch/UnitMatchPy/UnitMatchPy/GUI_icon.png\n", "File exists: True\n"]}], "source": ["is_match, not_match, matches_GUI = um_gui.run_GUI()\n", "# GUI guide: https://github.com/EnnyvanBeest/UnitMatch/blob/main/UnitMatchPy/Demo%20Notebooks/GUI_Reference_Guide.md"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Saving UnitMatch results and generating final outputs...\n", "Number of Liberal Matches: 67\n", "Number of Intermediate Matches: 60\n", "Number of Conservative Matches: 60\n", "📁 Saving results to: /home/<USER>/cup/Chris/data/cta_backwards/calca_302/2023-04-19/cz_npxl_g0/cz_npxl_g0_imec0/UnitMatch_Results\n", "✅ Results saved successfully!\n", "\n", "📊 Final Results Summary:\n", "============================================================\n", "🧠 Dataset: calca_302 (2 sessions)\n", "📊 Total units analyzed: 2\n", "✅ Good units tracked: 305\n", "🔗 Cross-session matches found: 287.0\n", "🏷️  Unique neurons identified: 305\n", "\n", "📁 Output Files Generated:\n", "   📊 output_prob_matrix.npy - Match probability matrix\n", "   📋 match_table.csv - Detailed match information\n", "   🏷️  unique_IDs.npy - Cross-session unit identifiers\n", "   📈 total_score.npy - Combined similarity scores\n", "   🎯 clus_info.json - Unit metadata and session info\n"]}], "source": ["# 💾 Save Results and Generate Final Outputs\n", "\n", "print(\"💾 Saving UnitMatch results and generating final outputs...\")\n", "\n", "# Assign unique IDs to matched units across sessions\n", "UIDs = aid.assign_unique_id(output_prob_matrix, um_param, clus_info)\n", "\n", "# Get final matches above threshold\n", "matches = np.argwhere(output_threshold == 1)\n", "\n", "# Create save directory next to the first session's BombCell results\n", "save_dir = Path(session_results[0]['save_path']).parent / \"UnitMatch_Results\"\n", "save_dir.mkdir(exist_ok=True)\n", "\n", "print(f\"📁 Saving results to: {save_dir}\")\n", "\n", "# Save comprehensive UnitMatch results\n", "su.save_to_output(\n", "    str(save_dir), \n", "    scores_to_include, \n", "    matches,\n", "    output_prob_matrix, \n", "    avg_centroid, \n", "    avg_waveform, \n", "    avg_waveform_per_tp, \n", "    max_site,\n", "    total_score, \n", "    output_threshold, \n", "    clus_info, \n", "    um_param, \n", "    UIDs=UIDs, \n", "    matches_curated=None,  # Set to matches_curated if manual curation was performed\n", "    save_match_table=True\n", ")\n", "\n", "print(\"✅ Results saved successfully!\")\n", "\n", "# Generate summary statistics\n", "n_unique_neurons = len(np.unique(UIDs))\n", "n_total_units = len(np.concatenate(good_units))\n", "n_cross_session_matches = cross_session_matches\n", "\n", "print(f\"\\n📊 Final Results Summary:\")\n", "print(f\"{'='*60}\")\n", "print(f\"🧠 Dataset: calca_302 (2 sessions)\")\n", "print(f\"📊 Total units analyzed: {sum(len(result['unit_type']) for result in session_results)}\")\n", "print(f\"✅ Good units tracked: {n_total_units}\")\n", "print(f\"🔗 Cross-session matches found: {n_cross_session_matches}\")\n", "print(f\"🏷️  Unique neurons identified: {n_unique_neurons}\")\n", "\n", "print(f\"\\n📁 Output Files Generated:\")\n", "print(f\"\\n📁 Output Files Generated:\")\n", "print(f\"   📊 MatchProb.npy - Match probability matrix\")\n", "print(f\"   📋 match_table.csv - Detailed match information\")\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎉 Pipeline Complete!\n", "\n", "\n", "### Key Files Generated\n", "\n", "**BombCell Outputs (per session):**\n", "- `qMetrics/cluster_bc_unitType.tsv` - Unit classifications\n", "- `qMetrics/templates._bc_qMetrics.parquet` - Quality metrics\n", "- `qMetrics/RawWaveforms/` - Raw spike waveforms for UnitMatch\n", "- `qMetrics/summary_plots.png` - Quality control visualizations\n", "\n", "**UnitMatch Outputs:**\n", "- `UnitMatch_Results/output_prob_matrix.npy` - Match probabilities\n", "- `UnitMatch_Results/match_table.csv` - Unit matches and scores\n", "- `UnitMatch_Results/unique_IDs.npy` - Cross-session unit identifiers\n"]}], "metadata": {"kernelspec": {"display_name": "ephys", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}