{"cells": [{"cell_type": "code", "execution_count": 45, "id": "7f585a75", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import os"]}, {"cell_type": "code", "execution_count": 1, "id": "a5014a96", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[32;1mnpyx[c4] version 4.1.3 imported.\u001b[0m\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\trajectory_1.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\trajectory_2.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b14\\results\\trajectory_1.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\trajectory_1.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\trajectory_1.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\trajectory_2.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\trajectory_2.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\trajectory_1.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b18\\results\\trajectory_1.csv\n", "Saved trajectory to: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b25\\results\\trajectory_1.csv\n"]}], "source": ["from npx_utils import extract_probe_trajectory\n", "from pathlib import Path\n", "\n", "mat_paths = [\n", "    \n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\probe_ccf_struct_2.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b14\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\probe_ccf_struct_2.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\probe_ccf_struct_2.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b18\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b25\\results\\probe_ccf_struct_1.mat'    \n", "]\n", "\n", "for fpath in mat_paths:\n", "    df_trajectory = extract_probe_trajectory(str(fpath))\n", "    #print(f\"Extracted trajectory from: {fpath}\")\n", "    df_trajectory.to_csv(Path(fpath).parent / f\"trajectory_{Path(fpath).name[-5]}.csv\", index=False)\n", "    print(f\"Saved trajectory to: {Path(fpath).parent / f'trajectory_{Path(fpath).name[-5]}.csv'}\")\n"]}, {"cell_type": "code", "execution_count": 58, "id": "2ecaa619", "metadata": {}, "outputs": [], "source": ["# === Mapping recording_id to max depth ===\n", "recording_to_max_depth = {\n", "    \"b12_p1_r1_g0\": 3000+400,\n", "    \"b12_p2_r1_g0\": 4300+400,\n", "    \"b13_p1_r2_g0\": None,  # Will be skipped\n", "    \"b14_p1_r1_g0\": 3550,\n", "    \"b15_p1_r1_g0\": 3000-200,\n", "    \"b15_p2_r1_g0\": 3600+400,\n", "    \"b16_p1_r1_g0\": 3000+350,\n", "    \"b16_p2_r1_g0\": 3750,\n", "    \"b17_p1_r1_g0\": 3700+250,\n", "    \"b17_p1_r2_g0\": 3700+250,\n", "    \"b18_p1_r1_g0\": 2330+1000\n", "}"]}, {"cell_type": "code", "execution_count": 61, "id": "0619ee0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processed and saved: b12_p1_r1_g0\n", "Processed and saved: b12_p2_r1_g0\n", "Skipping b13_p1_r2_g0 (missing max depth)\n", "Processed and saved: b14_p1_r1_g0\n", "Processed and saved: b15_p1_r1_g0\n", "Processed and saved: b15_p2_r1_g0\n", "Processed and saved: b16_p1_r1_g0\n", "Processed and saved: b16_p2_r1_g0\n", "Processed and saved: b17_p1_r1_g0\n", "Processed and saved: b17_p1_r2_g0\n", "Processed and saved: b18_p1_r1_g0\n"]}], "source": ["# Fixed distance from probe tip to first contact point\n", "distance_tip_to_first_contact = 0\n", "\n", "# List of all unit_summary.pkl paths\n", "unit_summary_paths = [\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b12\\b12_p1_r1_g0\\b12_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b12\\b12_p2_r1_g0\\b12_p2_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b13\\b13_p1_r2_g0\\b13_p1_r2_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b14\\b14_p1_r1_g0\\b14_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b15\\b15_p1_r1_g0\\b15_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b15\\b15_p2_r1_g0\\b15_p2_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b16\\b16_p1_r1_g0\\b16_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b16\\b16_p2_r1_g0\\b16_p2_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b17\\b17_p1_r1_g0\\b17_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b17\\b17_p1_r2_g0\\b17_p1_r2_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b18\\b18_p1_r1_g0\\b18_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "]\n", "\n", "# Process each file\n", "for fpath in unit_summary_paths:\n", "    recording_id = os.path.basename(os.path.dirname(fpath)).split('_imec')[0]\n", "\n", "    # Get session-specific probe depth\n", "    max_depth = recording_to_max_depth.get(recording_id, None)\n", "    if max_depth is None:\n", "        print(f\"Skipping {recording_id} (missing max depth)\")\n", "        continue\n", "\n", "    try:\n", "        # Load unit summary data\n", "        unit_data = pd.read_pickle(fpath)\n", "\n", "        # Compute depth penetration from tip\n", "        unit_data['depth_penetration_um'] = max_depth - distance_tip_to_first_contact - unit_data['depth']\n", "\n", "        # Save updated DataFrame\n", "        unit_data.to_pickle(fpath)\n", "\n", "        print(f\"Processed and saved: {recording_id}\")\n", "\n", "    except Exception as e:\n", "        print(f\"Failed to process {recording_id}: {e}\")\n"]}, {"cell_type": "code", "execution_count": 62, "id": "c8f9e2ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📁 Processing b12_p1_r1_g0...\n", "  🧠 Units per region:\n", "    Lateral dorsal nucleus of thalamus: 8\n", "    Thalamus: 1\n", "    Dorsal part of the lateral geniculate complex core: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b12_p2_r1_g0...\n", "  🧠 Units per region:\n", "    Ventral posteromedial nucleus of the thalamus: 10\n", "    Lateral dorsal nucleus of thalamus: 8\n", "    Zona incerta: 3\n", "    Ventral posterolateral nucleus of the thalamus: 3\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b13_p1_r2_g0...\n", "  ⚠ Skipped: no max depth\n", "\n", "📁 Processing b14_p1_r1_g0...\n", "  🧠 Units per region:\n", "    Claustrum: 31\n", "    Primary somatosensory area mouth layer 6a: 24\n", "    Gustatory areas layer 6a: 10\n", "    Agranular insular area dorsal part layer 6a: 8\n", "    Endopiriform nucleus dorsal part: 6\n", "    Primary somatosensory area mouth layer 5: 4\n", "    Agranular insular area ventral part layer 6a: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b15_p1_r1_g0...\n", "  🧠 Units per region:\n", "    Lateral dorsal nucleus of thalamus: 7\n", "    Field CA1: 2\n", "    Field CA3: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b15_p2_r1_g0...\n", "  🧠 Units per region:\n", "    Claustrum: 9\n", "    Supplemental somatosensory area layer 6b: 8\n", "    external capsule: 7\n", "    Olfactory areas: 4\n", "    Gustatory areas layer 6b: 3\n", "    Supplemental somatosensory area layer 6a: 3\n", "    Primary somatosensory area mouth layer 6a: 2\n", "    Piriform area: 1\n", "    Agranular insular area posterior part layer 6a: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b16_p1_r1_g0...\n", "  🧠 Units per region:\n", "    Lateral dorsal nucleus of thalamus: 7\n", "    Field CA3: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b16_p2_r1_g0...\n", "  🧠 Units per region:\n", "    Primary motor area Layer 6a: 6\n", "    Claustrum: 5\n", "    Endopiriform nucleus dorsal part: 4\n", "    Olfactory areas: 1\n", "    Primary somatosensory area mouth layer 6a: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b17_p1_r1_g0...\n", "  🧠 Units per region:\n", "    Endopiriform nucleus dorsal part: 18\n", "    Caudoputamen: 5\n", "    Claustrum: 4\n", "    supra-callosal cerebral white matter: 4\n", "    Agranular insular area ventral part layer 6a: 3\n", "    genu of corpus callosum: 2\n", "    Cortical subplate: 1\n", "    external capsule: 1\n", "    Primary somatosensory area mouth layer 6a: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b17_p1_r2_g0...\n", "  🧠 Units per region:\n", "    Endopiriform nucleus dorsal part: 20\n", "    Agranular insular area ventral part layer 6a: 3\n", "    Claustrum: 3\n", "    Cortical subplate: 1\n", "  ✔ Updated and saved\n", "\n", "📁 Processing b18_p1_r1_g0...\n", "  🧠 Units per region:\n", "    Agranular insular area dorsal part layer 6a: 10\n", "    Agranular insular area ventral part layer 6a: 2\n", "    Gustatory areas layer 6a: 1\n", "  ✔ Updated and saved\n", "\n", "📊 Total unit counts per region across all recordings:\n", "  Claustrum: 52\n", "  Endopiriform nucleus dorsal part: 48\n", "  Lateral dorsal nucleus of thalamus: 30\n", "  Primary somatosensory area mouth layer 6a: 28\n", "  Agranular insular area dorsal part layer 6a: 18\n", "  Gustatory areas layer 6a: 11\n", "  Ventral posteromedial nucleus of the thalamus: 10\n", "  Agranular insular area ventral part layer 6a: 9\n", "  Supplemental somatosensory area layer 6b: 8\n", "  external capsule: 8\n", "  Primary motor area Layer 6a: 6\n", "  Olfactory areas: 5\n", "  Caudoputamen: 5\n", "  Primary somatosensory area mouth layer 5: 4\n", "  supra-callosal cerebral white matter: 4\n", "  Zona incerta: 3\n", "  Ventral posterolateral nucleus of the thalamus: 3\n", "  Gustatory areas layer 6b: 3\n", "  Supplemental somatosensory area layer 6a: 3\n", "  Field CA1: 2\n", "  Field CA3: 2\n", "  genu of corpus callosum: 2\n", "  Cortical subplate: 2\n", "  Thalamus: 1\n", "  Dorsal part of the lateral geniculate complex core: 1\n", "  Piriform area: 1\n", "  Agranular insular area posterior part layer 6a: 1\n"]}], "source": ["import pandas as pd\n", "import os\n", "from collections import Counter\n", "\n", "\n", "\n", "# === Mapping recording_id to trajectory file ===\n", "recording_to_track = {\n", "    \"b12_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\trajectory_1.csv\",\n", "    \"b12_p2_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\trajectory_2.csv\",\n", "    \"b14_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b14\\results\\trajectory_1.csv\",\n", "    \"b15_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\trajectory_1.csv\",\n", "    \"b15_p2_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\trajectory_2.csv\",\n", "    \"b16_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\trajectory_1.csv\",\n", "    \"b16_p2_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\trajectory_2.csv\",\n", "    \"b17_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\trajectory_1.csv\",\n", "    \"b17_p1_r2_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\trajectory_1.csv\",\n", "    \"b17_p2_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\trajectory_1.csv\",\n", "    \"b18_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b18\\results\\trajectory_1.csv\"\n", "}\n", "\n", "\n", "# === List of all unit_summary.pkl paths ===\n", "unit_summary_paths = [\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b12\\b12_p1_r1_g0\\b12_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b12\\b12_p2_r1_g0\\b12_p2_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b13\\b13_p1_r2_g0\\b13_p1_r2_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b14\\b14_p1_r1_g0\\b14_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b15\\b15_p1_r1_g0\\b15_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b15\\b15_p2_r1_g0\\b15_p2_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b16\\b16_p1_r1_g0\\b16_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b16\\b16_p2_r1_g0\\b16_p2_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b17\\b17_p1_r1_g0\\b17_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b17\\b17_p1_r2_g0\\b17_p1_r2_g0_imec0\\unit_summary.pkl\",\n", "    r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b18\\b18_p1_r1_g0\\b18_p1_r1_g0_imec0\\unit_summary.pkl\",\n", "]\n", "\n", "# === Fixed distance from tip to first contact ===\n", "distance_tip_to_first_contact = 0\n", "\n", "# === Accumulator for overall region counts ===\n", "overall_region_counts = Counter()\n", "\n", "# === Processing each file ===\n", "for fpath in unit_summary_paths:\n", "    recording_id = os.path.basename(os.path.dirname(fpath)).split('_imec')[0]\n", "    print(f\"\\n📁 Processing {recording_id}...\")\n", "\n", "    max_depth = recording_to_max_depth.get(recording_id)\n", "    if max_depth is None:\n", "        print(f\"  ⚠ Skipped: no max depth\")\n", "        continue\n", "\n", "    track_path = recording_to_track.get(recording_id)\n", "    if track_path is None or not os.path.exists(track_path):\n", "        print(f\"  ⚠ Skipped: missing trajectory file\")\n", "        continue\n", "\n", "    try:\n", "        unit_data = pd.read_pickle(fpath)\n", "        track_data = pd.read_csv(track_path)\n", "\n", "        # Compute penetration depth\n", "        unit_data['depth_penetration_um'] = max_depth - distance_tip_to_first_contact - unit_data['depth']\n", "\n", "        # Lookup region based on depth\n", "        def get_region_info(depth):\n", "            row = track_data[(track_data['depth_from'] <= depth) & (depth < track_data['depth_to'])]\n", "            if not row.empty:\n", "                return row.iloc[0]['region_name'], row.iloc[0]['region_acronym']\n", "            else:\n", "                return pd.NA, pd.NA\n", "\n", "        region_info = unit_data['depth_penetration_um'].apply(get_region_info)\n", "        unit_data['region_name'] = region_info.apply(lambda x: x[0])\n", "        unit_data['region_acronym'] = region_info.apply(lambda x: x[1])\n", "\n", "        # Count regions (ignoring NAs)\n", "        region_counts = unit_data['region_name'].dropna().value_counts()\n", "        print(\"  🧠 Units per region:\")\n", "        for region, count in region_counts.items():\n", "            print(f\"    {region}: {count}\")\n", "            overall_region_counts[region] += count\n", "\n", "        # Save back to file\n", "        unit_data.to_pickle(fpath)\n", "        print(f\"  ✔ Updated and saved\")\n", "\n", "    except Exception as e:\n", "        print(f\"  ❌ Failed to process {recording_id}: {e}\")\n", "\n", "# === Final summary ===\n", "print(\"\\n📊 Total unit counts per region across all recordings:\")\n", "for region, count in overall_region_counts.most_common():\n", "    print(f\"  {region}: {count}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}