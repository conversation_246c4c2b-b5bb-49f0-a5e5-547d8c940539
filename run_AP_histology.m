%% <PERSON>ript to run AP_histology for registering Neuropixels tracks to the Atlas

% Add to path
addpath(genpath('C:\Users\<USER>\Documents\GitHub\AP_histology'))
addpath(genpath('C:\Users\<USER>\Documents\GitHub\npy-matlab'))
addpath(genpath('D:\AP_histology_atlas'))

% Run the AP_histology software
run('C:\Users\<USER>\Documents\GitHub\AP_histology\AP_histology.m')

%% Make into struct, to be easily read by python
paths = {
    'Z:\users\izouridis\projects\bal_npx\tracks\b12\results\probe_ccf.mat'
    'Z:\users\izouridis\projects\bal_npx\tracks\b14\results\probe_ccf.mat'
    'Z:\users\izouridis\projects\bal_npx\tracks\b15\results\probe_ccf.mat'
    'Z:\users\izouridis\projects\bal_npx\tracks\b16\results\probe_ccf.mat'
    'Z:\users\izouridis\projects\bal_npx\tracks\b17\results\probe_ccf.mat'
    'Z:\users\izouridis\projects\bal_npx\tracks\b18\results\probe_ccf.mat'
    'Z:\users\izouridis\projects\bal_npx\tracks\b24\results\probe_ccf.mat'
    'Z:\users\izouridis\projects\bal_npx\tracks\b25\results\probe_ccf.mat'
};

for i = 1:length(paths)
    convert_probe_ccf(paths{i});
end

function convert_probe_ccf(inputPath)
    data = load(inputPath);
    if ~isfield(data, 'probe_ccf')
        error('No probe_ccf in %s', inputPath);
    end
    probe_ccf = data.probe_ccf;

    [folder, ~, ~] = fileparts(inputPath);

    for i = 1:numel(probe_ccf)
        if builtin('isa', probe_ccf(i).trajectory_areas, 'table')
            probe_ccf(i).trajectory_areas = table2struct(probe_ccf(i).trajectory_areas);
        end
        
        % Save each struct element separately
        savePath = fullfile(folder, sprintf('probe_ccf_struct_%d.mat', i));
        probe = probe_ccf(i).trajectory_areas;  % Save only this element as variable 'probe'
        save(savePath, 'probe');
        fprintf('Saved element %d to %s\n', i, savePath);
    end
end
