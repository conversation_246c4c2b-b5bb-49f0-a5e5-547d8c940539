clear; clc;

datasetPaths = {
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b12', 'b12_p1_r1_g0', 'b12_p1_r1_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b12', 'b12_p2_r1_g0', 'b12_p2_r1_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b13', 'b13_p1_r2_g0', 'b13_p1_r2_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b14', 'b14_p1_r1_g0', 'b14_p1_r1_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b15', 'b15_p1_r1_g0', 'b15_p1_r1_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b15', 'b15_p2_r1_g0', 'b15_p2_r1_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b16', 'b16_p1_r1_g0', 'b16_p1_r1_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b16', 'b16_p2_r1_g0', 'b16_p2_r1_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b17', 'b17_p1_r2_g0', 'b17_p1_r2_g0_imec0');
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b18', 'b18_p1_r1_g0', 'b18_p1_r1_g0_imec0');
};


for i = length(datasetPaths)
    runBombcellQualityAssessment(datasetPaths{i}, ...
        'SaveClusterKSLabel', true, ...
        'LaunchGUI', false);
end
