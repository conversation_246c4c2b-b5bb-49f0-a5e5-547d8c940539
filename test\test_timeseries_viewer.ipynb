{"cells": [{"cell_type": "code", "execution_count": 3, "id": "f468b306", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(0, r\"C:\\Users\\<USER>\\Documents\\GitHub\\bal_npx\")\n", "from timeseries_viewer import calculate_firing_rate, viewer as ts_viewer"]}, {"cell_type": "code", "execution_count": 5, "id": "792bbd61", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import importlib.util\n", "mod_path = Path(r\"C:\\Users\\<USER>\\Documents\\GitHub\\bal_npx\\timeseries_viewer.py\")\n", "spec = importlib.util.spec_from_file_location(\"timeseries_viewer\", str(mod_path))\n", "mod = importlib.util.module_from_spec(spec); spec.loader.exec_module(mod)\n", "calculate_firing_rate = mod.calculate_firing_rate; ts_viewer = mod.viewer"]}, {"cell_type": "code", "execution_count": 9, "id": "1e798f1c", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "99d80de3178a40ea95a40dcd8eea1cb6", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(FigureWidget({\n", "    'data': [{'mode': 'lines',\n", "              'name': '<PERSON>',\n", "              'ty…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5c799ea21db74f4c9ea6356e87f515c3", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'mode': 'lines',\n", "              'name': '<PERSON>',\n", "              'type': 'scatter',\n", "              'uid': '945bade8-cef5-479d-afd2-106b9c51070f',\n", "              'x': array([ -5.88644428,  -5.78644428,  -5.68644428, ..., 305.81355572,\n", "                          305.91355572, 306.01355572]),\n", "              'y': array([0., 0., 0., ..., 0., 0., 0.])},\n", "             {'marker': {'color': 'black', 'opacity': 0.6, 'size': 4},\n", "              'mode': 'markers',\n", "              'name': '<PERSON><PERSON>',\n", "              'type': 'scatter',\n", "              'uid': '6dda26a9-b68b-4dec-acb6-81148b734155',\n", "              'x': array([1.10203125e-01, 1.63789469e-01, 1.99265577e-01, ..., 2.99654102e+02,\n", "                          2.99783398e+02, 2.99942573e+02]),\n", "              'y': array([1., 1., 1., ..., 1., 1., 1.]),\n", "              'yaxis': 'y2'}],\n", "    'layout': {'height': 450,\n", "               'legend': {'orientation': 'h'},\n", "               'shapes': [{'fillcolor': 'LightSkyBlue',\n", "                           'layer': 'below',\n", "                           'line': {'width': 0},\n", "                           'opacity': 0.3,\n", "                           'type': 'rect',\n", "                           'x0': 50.0,\n", "                           'x1': 80.0,\n", "                           'xref': 'x',\n", "                           'y0': 0,\n", "                           'y1': 1,\n", "                           'yref': 'y domain'},\n", "                          {'fillcolor': 'LightSkyBlue',\n", "                           'layer': 'below',\n", "                           'line': {'width': 0},\n", "                           'opacity': 0.3,\n", "                           'type': 'rect',\n", "                           'x0': 150.0,\n", "                           'x1': 180.0,\n", "                           'xref': 'x',\n", "                           'y0': 0,\n", "                           'y1': 1,\n", "                           'yref': 'y domain'},\n", "                          {'fillcolor': 'LightSkyBlue',\n", "                           'layer': 'below',\n", "                           'line': {'width': 0},\n", "                           'opacity': 0.3,\n", "                           'type': 'rect',\n", "                           'x0': 220.0,\n", "                           'x1': 250.0,\n", "                           'xref': 'x',\n", "                           'y0': 0,\n", "                           'y1': 1,\n", "                           'yref': 'y domain'}],\n", "               'template': '...',\n", "               'title': {'text': 'Raw Spikes (auto FR + raster)'},\n", "               'xaxis': {'range': [-12.124444280755664, 312.2515557192432], 'title': {'text': 'Time (s)'}},\n", "               'yaxis': {'range': [-1.1192667488570038, 12.311934237427042], 'title': {'text': 'Amplitude'}},\n", "               'yaxis2': {'overlaying': 'y',\n", "                          'range': [0, 1],\n", "                          'showticklabels': <PERSON><PERSON><PERSON>,\n", "                          'side': 'right',\n", "                          'title': {'text': '<PERSON><PERSON>'},\n", "                          'visible': True}}\n", "})"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["%run -i \"C:\\Users\\<USER>\\Documents\\GitHub\\bal_npx\\timeseries_viewer.py\"\n", "ts_viewer = viewer\n", "\n", "# Simulate spike times\n", "np.random.seed(0)\n", "T = 300.0\n", "spike_times = np.sort(np.random.uniform(0, T, 2000))\n", "events = [[50, 80], [150, 180], [220, 250]]\n", "\n", "# Auto-generate time vector with dt=0.1 s, compute gaussian FR (sigma=1.0 s), and overlay raster\n", "ts_viewer(None, None, events=events, title=\"Raw Spikes (auto FR + raster)\",\n", "          spike_times=spike_times, rate_method='gaussian', rate_kwargs={'sigma': 1.0, 'dt': 0.1},\n", "          show_raster=True, enable_keyboard=True, show_cursor_controls=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}