% Add path to SGLXMetaToCoords
addpath(genpath('C:\Users\<USER>\Documents\GitHub\SGLXMetaToCoords'))

% === User Inputs ===
metaFullPath = "Z:\users\izouridis\projects\bal_npx\data\b12\b12_p2_r1_g0\b12_p2_r1_g0_imec0\b12_p2_r1_g0_t0.imec0.ap.meta";
chanMapFile = replace(metaFullPath, '.ap.meta', '_kilosortChanMap.mat');
bProcLF = 0;            % 0 = AP, 1 = LFP
num_shanks = 1;         % Set to 1 or 4 depending on your probe

% === Extract channel map (variables will appear in workspace) ===
SGLXMetaToCoords(metaFullPath, bProcLF);

% Ensure required variables are in workspace
requiredVars = {'chanMap', 'connected', 'xcoords', 'ycoords'};
for v = requiredVars
    if ~exist(v{1}, 'var')
        error('Variable "%s" not found. Did SGLXMetaToCoords run correctly?', v{1});
    end
end

% === Format inputs ===
xcoords = xcoords(:);
ycoords = ycoords(:);
chanMap = chanMap(:);
connected = connected(:);

% === Assign kcoords based on number of shanks ===
kcoords = zeros(size(xcoords));

if num_shanks == 1
    kcoords(:) = 1;

elseif num_shanks == 4
    unique_x = unique(xcoords);
    
    if numel(unique_x) ~= 8
        error('Expected 8 unique x positions (2 per shank), got %d. Check xcoords.', numel(unique_x));
    end
    
    shank_centers = reshape(unique_x, 2, num_shanks)';  % [4 x 2]
    
    for shank_id = 1:num_shanks
        shank_xs = shank_centers(shank_id, :);
        idx = ismember(xcoords, shank_xs);
        kcoords(idx) = shank_id;
    end

else
    error('Only num_shanks == 1 or 4 is supported.');
end

% === Save to Kilosort-compatible chanMap file ===
save(chanMapFile, 'chanMap', 'connected', 'xcoords', 'ycoords', 'kcoords')
fprintf('✅ Saved chanMap with %d shank(s) to:\n%s\n', num_shanks, chanMapFile);
