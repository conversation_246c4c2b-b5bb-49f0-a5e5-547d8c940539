body {
    margin: auto;
    padding-right: 1em;
    padding-left: 1em;
    max-width: 68em; /* 44em */
    border-left: 1px solid black;
    border-right: 1px solid black;
    color: black;
    font-family: Verdana, sans-serif;
    font-size: 100%;
    line-height: 140%;
    color: #333;
}
blockquote {
    border-left: 5px solid #cccccc;
    margin-left: 20px;
    padding: 0 0 0 15px;
}
pre {
    border: 1px dotted gray;
    border-style: dotted;
    background-color: #f5f5f5; /* #ececec */
    display: block;
    color: #f5f5f5;
    padding-top: 0;
    padding-bottom: 0;
/*    padding: 0.5em; */
}
code {
    font-family: monospace;
    font-size: 115%;
    background-color: transparent;
    color: #1d9d1d;
}
h1 a, h2 a, h3 a, h4 a, h5 a {
    text-decoration: none;
    color: #7a5ada;
}
h1, h2, h3, h4, h5 {
    font-family: verdana;
    font-weight: bold;
    border-bottom: none; /* 1px dotted black; */
    color: #7a5ada; }
h1 {
    font-size: 130%;
}
h2 {
    font-size: 110%;
}
h3 {
    font-size: 95%;
}
h4 {
    font-size: 90%;
    font-style: italic;
}
h5 {
    font-size: 90%;
    font-style: italic;
}
h1.title {
    font-size: 200%;
    font-weight: bold;
    padding-top: 0.2em;
    padding-bottom: 0.2em;
    text-align: left;
    border: none;
}
dt code {
    font-weight: bold;
}
dd p {
    margin-top: 0;
}
#footer {
    padding-top: 1em;
    font-size: 70%;
    color: gray;
    text-align: center;
}
table {
  margin-bottom: 2em;
  border-bottom: 1px solid #ddd;
  border-right: 1px solid #ddd;
  border-spacing: 0;
  border-collapse: collapse;
}
table th {
  padding: .2em 1em;
  background-color: #eee;
  border-top: 1px solid #ddd;
  border-left: 1px solid #ddd;
}
table td {
  padding: .2em 1em;
  border-top: 1px solid #ddd;
  border-left: 1px solid #ddd;
  vertical-align: top;
}

