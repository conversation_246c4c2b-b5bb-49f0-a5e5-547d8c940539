{"cells": [{"cell_type": "markdown", "id": "c5594e5e", "metadata": {}, "source": ["this is a script to extract probe trajectory data for each recording"]}, {"cell_type": "code", "execution_count": 2, "id": "d1226ec5", "metadata": {}, "outputs": [], "source": ["from npx_utils import extract_probe_trajectory\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "id": "06db1000", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved trajectory to: C:\\Users\\<USER>\\Desktop\\tracks\\b12\\results\\trajectory_1.csv\n", "Saved trajectory to: C:\\Users\\<USER>\\Desktop\\tracks\\b12\\results\\trajectory_2.csv\n", "Saved trajectory to: C:\\Users\\<USER>\\Desktop\\tracks\\b14\\results\\trajectory_1.csv\n", "Saved trajectory to: C:\\Users\\<USER>\\Desktop\\tracks\\b16\\results\\trajectory_1.csv\n", "Saved trajectory to: C:\\Users\\<USER>\\Desktop\\tracks\\b16\\results\\trajectory_2.csv\n", "Saved trajectory to: C:\\Users\\<USER>\\Desktop\\tracks\\b17\\results\\trajectory_1.csv\n", "Saved trajectory to: C:\\Users\\<USER>\\Desktop\\tracks\\b18\\results\\trajectory_1.csv\n"]}], "source": ["mat_paths = [\n", "    r'C:\\Users\\<USER>\\Desktop\\tracks\\b12\\results\\probe_ccf_struct_1.mat',\n", "    r'C:\\Users\\<USER>\\Desktop\\tracks\\b12\\results\\probe_ccf_struct_2.mat',\n", "    r'C:\\Users\\<USER>\\Desktop\\tracks\\b14\\results\\probe_ccf_struct_1.mat',\n", "    r'C:\\Users\\<USER>\\Desktop\\tracks\\b16\\results\\probe_ccf_struct_1.mat',\n", "    r'C:\\Users\\<USER>\\Desktop\\tracks\\b16\\results\\probe_ccf_struct_2.mat',\n", "    r'C:\\Users\\<USER>\\Desktop\\tracks\\b17\\results\\probe_ccf_struct_1.mat',\n", "    r'C:\\Users\\<USER>\\Desktop\\tracks\\b18\\results\\probe_ccf_struct_1.mat'\n", "]\n", "\n", "for fpath in mat_paths:\n", "    df_trajectory = extract_probe_trajectory(str(fpath))\n", "    #print(f\"Extracted trajectory from: {fpath}\")\n", "    df_trajectory.to_csv(Path(fpath).parent / f\"trajectory_{Path(fpath).name[-5]}.csv\", index=False)\n", "    print(f\"Saved trajectory to: {Path(fpath).parent / f'trajectory_{Path(fpath).name[-5]}.csv'}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}