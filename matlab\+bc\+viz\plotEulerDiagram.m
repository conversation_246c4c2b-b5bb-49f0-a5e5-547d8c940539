function plotEulerDiagram(qMetric, param, qMetricNames,figHandle)

colorMtx = colors(14); % get 12 colorbling-friendly colors
colorMtx = [colorMtx(1:14, :, :), repmat(0.7, 14, 1)]; % make 70% transparent
colorMtx = colorMtx([1:2:end, 2:2:end], :); % shuffle colors to get more distinct pairs


if param.computeDistanceMetrics && ~isnan(param.isoDmin)
    setsw = {find(qMetric.nPeaks > param.maxNPeaks), find(qMetric.nTroughs > param.maxNTroughs), ...
        find(qMetric.waveformBaselineFlatness >= param.maxWvBaselineFraction), ...
        find(qMetric.spatialDecaySlope <= param.minSpatialDecaySlope), ...
        find(qMetric.waveformDuration_peakTrough < param.minWvDuration | qMetric.waveformDuration_peakTrough > param.maxWvDuration), ...
        find(qMetric.isSomatic == 0), find(qMetric.fractionRPVs_estimatedTauR > param.maxRPVviolations), ...
        find(qMetric.percentageSpikesMissing_gaussian > param.maxPercSpikesMissing), ...
        find(qMetric.nSpikes <= param.minNumSpikes), find(qMetric.rawAmplitude <= param.minAmplitude), ...
        find(qMetric.signalToNoiseRatio <= param.minSignalToNoiseRatio), find(qMetric.maxDriftEstimate <= param.maxDrift),...
        find(qMetric.isoD <= param.isoDmin), find(qMetric.Lratio >= param.lratioMax)};
else
    setsw = {find(qMetric.nPeaks > param.maxNPeaks), find(qMetric.nTroughs > param.maxNTroughs), ...
        find(qMetric.waveformBaseline >= param.maxWvBaselineFraction), ...
        find(qMetric.spatialDecaySlope <= param.minSpatialDecaySlope), ...
        find(qMetric.waveformDuration_peakTrough < param.minWvDuration | qMetric.waveformDuration > param.maxWvDuration), ...
        find(qMetric.isSomatic == 0), find(qMetric.fractionRPVs_estimatedTauR > param.maxRPVviolations), ...
        find(qMetric.percentageSpikesMissing_gaussian > param.maxPercSpikesMissing), ...
        find(qMetric.nSpikes <= param.minNumSpikes), find(qMetric.rawAmplitude <= param.minAmplitude),...
        find(qMetric.signalToNoiseRatio <= param.minSignalToNoiseRatio), find(qMetric.maxDriftEstimate <= param.maxDrift)};
end
emptyCell = find(cellfun(@isempty, setsw));
if ~isempty(emptyCell)
    for iEC = 1:length(emptyCell)
        setsw{emptyCell(iEC)} = 0;
    end
end
figure(figHandle)
subplot(1, 10, 2:5)
vennEulerDiagram(setsw(1: ...
    size(qMetricNames{1}, 2)), qMetricNames{1}, ...
    'ColorOrder', colorMtx(1:6, 1:3), ...
    'ShowIntersectionCounts', 1);

subplot(1, 10, 1) % hacky way to get a legend
title('# of units classified as noise/non-somatic with quality metrics');
hold on;

set(gca, 'XColor', 'w', 'YColor', 'w')
hold on;
arrayfun(@(x) plot(NaN, NaN, 'linewidth', 2, 'color', colorMtx(x, :)), 1:size(qMetricNames{1}, 2));
legend(qMetricNames{1})
set(gcf, 'color', 'w')

subplot(1, 10, 7:10)
vennEulerDiagram(setsw(size(qMetricNames{1}, 2)+1:size(qMetricNames{1}, 2)+size(qMetricNames{2}, 2)), qMetricNames{2}, ...
    'ColorOrder', colorMtx(size(qMetricNames{1}, 2)+1:size(qMetricNames{1}, 2)+size(qMetricNames{2}, 2), 1:3), ...
    'ShowIntersectionCounts', 1);

subplot(1, 10, 6) % hacky way to get a legend + title
title('# of units classified as multi-unit with quality metrics');
hold on;

set(gca, 'XColor', 'w', 'YColor', 'w')
hold on;
arrayfun(@(x) plot(NaN, NaN, 'linewidth', 2, 'color', colorMtx(x, :)), size(qMetricNames{1}, 2)+1:size(qMetricNames{1}, 2)+size(qMetricNames{2}, 2));
legend(qMetricNames{2})
set(gcf, 'color', 'w')
end