{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Modulation Analysis: b27, b28, b30 datasets"]}, {"cell_type": "code", "execution_count": 42, "id": "2ad46552", "metadata": {}, "outputs": [], "source": ["# Imports and dataset paths\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "from typing import Tuple, Sequence, Union, Dict, Any, Optional, List"]}, {"cell_type": "markdown", "id": "d414ab77", "metadata": {}, "source": ["## Basic info and sample rows\n", "We print number of units, columns and dtypes, and show a small sample for each dataset."]}, {"cell_type": "code", "execution_count": 43, "id": "72fc0a02", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 15 rows (units) from: ['b30']\n"]}], "source": ["paths = {  \n", "    #'b27': Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b27\\\\b27_p1_r1_g0_p\\\\catgt\\\\unit_summary.pkl\"),\n", "    #'b28': Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b28\\\\b28_p2_r1_g0\\\\catgt\\\\unit_summary.pkl\"),\n", "    'b30': Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b30\\\\b30_p1_r1_g0\\\\catgt\\\\unit_summary.pkl\")\n", "         }\n", "dfs = []\n", "for name, p in paths.items():\n", "    if not p.exists(): print(f\"⚠ Missing: {p}\"); continue\n", "    df = pd.read_pickle(p).copy(); dfs.append(df)\n", "data = pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()\n", "print(f\"Loaded {len(data)} rows (units) from: {[d for d in paths if (paths[d]).exists()]}\")\n", "\n", "data['unit'] = data['animal_id'] + '_' + data['unit'].astype(str)\n", "data.drop(columns=['spike_times_samples', 'peak_channel', 'depth'], inplace=True)"]}, {"cell_type": "code", "execution_count": 44, "id": "468d9daf", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "data[\"balloon_off_sec\"] = data[\"balloon_off_sec\"].apply(\n", "    lambda offs: [[max(e - 200.0, 0.0), max(e, 0.0)] if np.isfinite(e) else [s, e] for s, e in offs]\n", ")"]}, {"cell_type": "markdown", "id": "7c905542", "metadata": {}, "source": ["## Compute modulation per unit and trial\n", "This section applies the imported functions to the loaded DataFrame.\n", "- Observed MI: ON vs OFF-tail per trial using balloon_off_sec_tail (last 60 s of OFF).\n", "- Null MI: per OFF trial, compare whole OFF vs random subwindows inside OFF."]}, {"cell_type": "code", "execution_count": 45, "id": "9601c9e7", "metadata": {}, "outputs": [], "source": ["# Apply functions to DataFrame\n", "\n", "from modulation_functions import (\n", "    modulation_index_single_trial,\n", "    compute_trialwise_mi,\n", "    compute_trialwise_mi_null,\n", ")\n", "\n", "data['modulation_index'] = data.apply(compute_trialwise_mi, axis=1)\n", "\n", "data['modulation_index_null'] = data.apply(\n", "    lambda r: compute_trialwise_mi_null(\n", "        r,\n", "        window_len=40,            # samples\n", "        n_iter=10000,              # how many times to resample the OFF baseline\n", "        mi_formula='difference_over_sum',\n", "        seed=42,                  # set for reproducibility; or None\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "markdown", "id": "mod-flag-intro", "metadata": {}, "source": ["## Modulation flag (per trial)\n", "We classify each trial as enhanced (+1), suppressed (−1), or not modulated (0)\n", "by comparing the observed MI against its per-trial null distribution.\n", "Ties and invalid/null cases return 0.\n"]}, {"cell_type": "code", "execution_count": 46, "id": "mod-flag-import", "metadata": {}, "outputs": [], "source": ["from modulation_functions import modulation_flag, compute_trialwise_modulation_flags\n", "\n", "# Add the -1/0/1 flags per trial\n", "data['modulated_trial'] = data.apply(\n", "    lambda r: compute_trialwise_modulation_flags(r, lower_quantile=0.1, upper_quantile=0.9),\n", "    axis=1\n", ")"]}, {"cell_type": "code", "execution_count": 47, "id": "92482e9d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import TwoSlopeNorm\n", "\n", "def plot_mi_heatmap(\n", "    data,\n", "    *,\n", "    mi_col: str = \"modulation_index\",\n", "    unit_col: str = \"unit\",\n", "    vmax: float | None = None\n", "):\n", "    \"\"\"\n", "    Heatmap of modulation index (trials × units), diverging colormap:\n", "      blue < 0 < red, with 0 in white. NaNs shown in light gray.\n", "\n", "    X-axis: Unit id (unit names from `unit_col`)\n", "    Y-axis: trial1, trial2, ...\n", "    \"\"\"\n", "    # Collect MI lists per unit\n", "    mi_lists = data[mi_col].tolist()\n", "    n_units = len(mi_lists)\n", "    max_trials = max((len(m) for m in mi_lists), default=0)\n", "\n", "    # Build matrix (units x trials), then transpose to (trials x units)\n", "    M = np.full((n_units, max_trials), np.nan, dtype=float)\n", "    for i, mis in enumerate(mi_lists):\n", "        M[i, :len(mis)] = np.asarray(mis, dtype=float)\n", "    M = M.T  # now shape = (max_trials, n_units): rows=trials, cols=units\n", "\n", "    # Symmetric normalization around 0\n", "    if vmax is None:\n", "        finite = M[np.isfinite(M)]\n", "        abs_max = np.max(np.abs(finite)) if finite.size else 1.0\n", "    else:\n", "        abs_max = float(vmax)\n", "    norm = TwoSlopeNorm(vmin=-abs_max, vcenter=0.0, vmax=+abs_max)\n", "\n", "    # Diverging colormap with white center; gray for NaN\n", "    cmap = plt.get_cmap(\"bwr\").copy()\n", "    cmap.set_bad(\"0.85\", alpha=1.0)  # NaNs as light gray\n", "\n", "    # Figure size: wide for many units, tall for many trials\n", "    fig_w = max(6, 0.35 * n_units)\n", "    fig_h = max(4, 0.28 * max_trials)\n", "\n", "    fig, ax = plt.subplots(figsize=(fig_w, fig_h))\n", "    im = ax.imshow(M, aspect=\"auto\", interpolation=\"nearest\", cmap=cmap, norm=norm)\n", "\n", "    # Colorbar\n", "    cbar = fig.colorbar(im, ax=ax)\n", "    cbar.set_label(\"Modulation index\")\n", "\n", "    # Tick labels\n", "    # X: units\n", "    units = data[unit_col].astype(str).tolist() if unit_col in data.columns else [str(i) for i in range(n_units)]\n", "    ax.set_xticks(np.arange(n_units))\n", "    ax.set_xticklabels(units, rotation=90, ha=\"center\", va=\"top\")\n", "\n", "    # Y: trials as trial1, trial2, ...\n", "    ax.set_yticks(np.arange(max_trials))\n", "    ax.set_yticklabels([f\"trial{i+1}\" for i in range(max_trials)])\n", "    ax.invert_yaxis()  # put trial1 at the top\n", "\n", "    # Axis labels & title\n", "    ax.set_xlabel(\"Unit id\")\n", "    ax.set_ylabel(\"(Trial #)\")\n", "\n", "    fig.tight_layout()\n", "    plt.show()\n", "\n", "# usage:\n", "plot_mi_heatmap(data)\n"]}, {"cell_type": "code", "execution_count": 48, "id": "2d7f6fcf", "metadata": {}, "outputs": [], "source": ["def classify_unit(modulated_trial):\n", "    n = len(modulated_trial)\n", "    n_pos = sum(x == 1 for x in modulated_trial)\n", "    n_neg = sum(x == -1 for x in modulated_trial)\n", "\n", "    if n_pos >= n / 2 and n_neg == 0:\n", "        return \"excited\"\n", "    elif n_neg >= n / 2 and n_pos == 0:\n", "        return \"inhibited\"\n", "    else:\n", "        return \"mixed/non-responsive\"\n", "\n", "data['unit_class'] = data['modulated_trial'].apply(classify_unit)\n"]}, {"cell_type": "code", "execution_count": 49, "id": "65e93d2d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {\n", "    'excited': '#1b9e77',\n", "    'inhibited': '#d95f02',\n", "    'mixed/non-responsive': '#808080'\n", "}\n", "\n", "# counts across the whole dataset\n", "counts = data['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "total = counts.sum()\n", "\n", "# labels with n values relative to total\n", "labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]\n", "colors = [class_colors[c] for c in class_order]\n", "\n", "fig, ax = plt.subplots(figsize=(5, 5))\n", "ax.pie(\n", "    counts.values, labels=labels, colors=colors,\n", "    autopct=lambda p: f'{p:.1f}%' if p > 0 else '', startangle=90, counterclock=False,\n", "    wedgeprops=dict(linewidth=1, edgecolor='white')\n", ")\n", "ax.set_title(f'Response classification (total n={total})')\n", "ax.axis('equal')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 50, "id": "bf01c1b5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {'excited': '#1b9e77', 'inhibited': '#d95f02', 'mixed/non-responsive': '#808080'}\n", "subsets = {k: v.copy() for k, v in data.groupby('animal_id')}\n", "\n", "animals = ['b27', 'b28', 'b30']\n", "\n", "fig, axes = plt.subplots(1, 4, figsize=(16, 4))  # 1x4 layout\n", "axes = axes.flatten()\n", "\n", "for ax, animal in zip(axes, animals):\n", "    df_sub = subsets.get(animal, None)\n", "    if df_sub is None or df_sub.empty:\n", "        ax.axis('off')\n", "        ax.set_title(f'{animal} (no data)', pad=2)\n", "        continue\n", "\n", "    counts = df_sub['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "    total = int(counts.sum())\n", "\n", "    # labels for legend\n", "    labels = [f'{cls}\\n(n={counts[cls]}/{total})' for cls in class_order]  # split in multiple lines\n", "    colors = [class_colors[c] for c in class_order]\n", "\n", "    wedges, texts, autotexts = ax.pie(\n", "        counts.values,\n", "        colors=colors,\n", "        autopct=lambda p: f'{p:.1f}%' if p > 0 else '',\n", "        startangle=90,\n", "        counterclock=False,\n", "        wedgeprops=dict(linewidth=1, edgecolor='white'),\n", "        textprops={'fontsize': 8}  # smaller %\n", "    )\n", "\n", "    # legend outside\n", "    ax.legend(\n", "        wedges, labels,\n", "        loc='upper center',\n", "        bbox_to_anchor=(0.5, -0.05),\n", "        fontsize=7,  # smaller font\n", "        ncol=1,\n", "        frameon=False\n", "    )\n", "\n", "    ax.set_title(f'{animal} (n={total} units)', pad=2)\n", "    ax.axis('equal')\n", "\n", "fig.tight_layout()\n", "fig.savefig('unit_classification_by_recording.png', dpi=200, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "ebe6c2be", "metadata": {}, "source": ["## Composite plots per recording with unit-class borders\n", "We generate per-recording composite grids of firing rates with balloon intervals.\n", "Plot boxes are outlined by unit class: red=excited, blue=inhibited, green=non-responsive.\n"]}, {"cell_type": "code", "execution_count": null, "id": "4887e273", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_units_grid_with_shading\n", "\n", "# Build a mapping from unit id -> unit class for border coloring\n", "unit_class_map = dict(zip(data['unit'], data['unit_class']))\n", "\n", "# Create one composite figure per recording (animal_id)\n", "grid_params = dict(\n", "    bin_size=10, t_start=0, t_end=None, n_cols=4,\n", "    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,\n", "    margins=(0.06, 0.92, 0.08, 0.9), dpi=200\n", "    )\n", "    for animal, df_sub in data.groupby('animal_id'):\n", "        out_file = f'{animal}_modulation_units_grid.png'\n", "        suptitle = f'{animal}: FR with balloon intervals'\n", "        _ = plot_units_grid_with_shading(\n", "            df_sub,\n", "            bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],\n", "            n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],\n", "            wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],\n", "            suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],\n", "            unit_class_map=unit_class_map, unit_col_name='unit'\n", "        )"]}, {"cell_type": "code", "execution_count": 60, "id": "eb7c86b8", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "animal_id", "rawType": "object", "type": "string"}, {"name": "inhibited", "rawType": "int64", "type": "integer"}, {"name": "mixed/non-responsive", "rawType": "int64", "type": "integer"}], "ref": "0a6640bb-8588-4797-8c1d-6bb3eb023e0f", "rows": [["b30", "2", "13"]], "shape": {"columns": 2, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>unit_class</th>\n", "      <th>inhibited</th>\n", "      <th>mixed/non-responsive</th>\n", "    </tr>\n", "    <tr>\n", "      <th>animal_id</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>b30</th>\n", "      <td>2</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["unit_class  inhibited  mixed/non-responsive\n", "animal_id                                  \n", "b30                 2                    13"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["# show a table how many excited inhibited and mixed units we have per animal_id, and summarize, with percentages\n", "\n", "data.groupby(['animal_id', 'unit_class']).size().unstack(fill_value=0)\n"]}, {"cell_type": "code", "execution_count": null, "id": "776352c3", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'plot_all_recordings' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[59], line 120\u001b[0m\n\u001b[0;32m    114\u001b[0m recordings \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    115\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb27\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb27\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m    116\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb28\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb28\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m    117\u001b[0m }\n\u001b[0;32m    119\u001b[0m \u001b[38;5;66;03m# Plot all of them\u001b[39;00m\n\u001b[1;32m--> 120\u001b[0m \u001b[43mplot_all_recordings\u001b[49m(recordings)\n", "\u001b[1;31mNameError\u001b[0m: name 'plot_all_recordings' is not defined"]}], "source": ["import numpy as np\n", "import math\n", "from matplotlib import pyplot as plt\n", "\n", "def _get_time_vec(tv):\n", "    if tv is None:\n", "        return lambda i: None\n", "    if isinstance(tv, np.ndarray) and tv.ndim == 1 and tv.dtype != object:\n", "        return lambda i: tv  # shared\n", "    if hasattr(tv, \"iloc\"):  # pandas Series\n", "        return lambda i: tv.iloc[i]\n", "    return lambda i: tv[i]  # list / object array\n", "\n", "def _series_to_list(x):\n", "    return x.tolist() if hasattr(x, \"tolist\") else list(x)\n", "\n", "def plot_recording_waveforms(\n", "    rec_name, rec_data, \n", "    max_cols=8, figsize_per_subplot=(1.5, 1.8),\n", "    save=True, outdir=\".\"\n", "):\n", "    # Accept DataFrame slice or dict\n", "    if hasattr(rec_data, \"__getitem__\") and not isinstance(rec_data, dict):\n", "        if len(rec_data) == 0:\n", "            print(f\"[skip] {rec_name}: no units found.\")\n", "            return\n", "        wf = rec_data[\"waveform_mean\"]\n", "        waveforms = _series_to_list(wf)\n", "        units = _series_to_list(rec_data[\"unit\"]) if \"unit\" in rec_data else None\n", "        tv_col = rec_data[\"waveform_time_vector_ms\"] if \"waveform_time_vector_ms\" in rec_data else None\n", "    else:\n", "        if rec_data is None:\n", "            print(f\"[skip] {rec_name}: data is None.\")\n", "            return\n", "        wf = rec_data.get(\"waveform_mean\", [])\n", "        if isinstance(wf, np.ndarray) and wf.ndim == 2:\n", "            waveforms = [wf[i, :] for i in range(wf.shape[0])]\n", "        else:\n", "            waveforms = list(wf)\n", "        units = rec_data.get(\"unit\", None)\n", "        tv_col = rec_data.get(\"waveform_time_vector_ms\", None)\n", "\n", "    n_units = len(waveforms)\n", "    if n_units == 0:\n", "        print(f\"[skip] {rec_name}: no units found.\")\n", "        return\n", "\n", "    tv_get = _get_time_vec(tv_col)\n", "\n", "    # Grid\n", "    ncols = min(max_cols, max(1, int(math.ceil(math.sqrt(n_units)))))\n", "    nrows = int(math.ceil(n_units / ncols))\n", "\n", "    fig_w = figsize_per_subplot[0] * ncols\n", "    fig_h = figsize_per_subplot[1] * nrows\n", "    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(fig_w, fig_h))\n", "\n", "    if not isinstance(axes, np.ndarray):\n", "        axes = np.array([axes])\n", "    axes = axes.reshape(nrows, ncols)\n", "\n", "    for i, ax in enumerate(axes.flat):\n", "        if i < n_units:\n", "            y = np.asarray(waveforms[i]).ravel()\n", "            x = tv_get(i)\n", "            if x is None:\n", "                x = np.arange(len(y))\n", "            ax.plot(x, y, color=\"black\")\n", "            if units is not None and i < len(units):\n", "                ax.set_title(f\"Unit {units[i]}\", fontsize=7)\n", "            ax.set_xticks([]); ax.set_yticks([])\n", "            ax.axis(\"off\")\n", "        else:\n", "            ax.axis(\"off\")\n", "\n", "    # ---- Add 1 ms scalebar INSIDE the last subplot without changing limits ----\n", "    last_ax = axes.flat[min(n_units, len(axes.flat)) - 1]\n", "    # Save current limits (so the scalebar doesn't shrink the waveform)\n", "    xlim = last_ax.get_xlim()\n", "    ylim = last_ax.get_ylim()\n", "\n", "    xspan = xlim[1] - xlim[0]\n", "    yspan = ylim[1] - ylim[0]\n", "\n", "    # Desired bar length (1 ms in data units)\n", "    bar_len = 1.0  # ms\n", "    # If the axis window is <1 ms, fall back to 50% of the span\n", "    if bar_len > xspan:\n", "        bar_len = 0.5 * xspan\n", "\n", "    # Place near bottom-right with a small margin\n", "    x_margin = 0.05 * xspan\n", "    y_margin = 0.08 * yspan\n", "    x_end = xlim[1] - x_margin\n", "    x_start = x_end - bar_len\n", "    y_bar = ylim[0] + y_margin\n", "\n", "    # Draw the bar + label, then restore limits so autoscale doesn't change\n", "    last_ax.plot([x_start, x_end], [y_bar, y_bar], color=\"black\", lw=2, solid_capstyle=\"butt\", clip_on=False, zorder=5)\n", "    last_ax.text((x_start + x_end) / 2, y_bar, \"1 ms\", ha=\"center\", va=\"bottom\", fontsize=7)\n", "\n", "    last_ax.set_xlim(xlim)\n", "    last_ax.set_ylim(ylim)\n", "    # --------------------------------------------------------------------------\n", "\n", "    fig.suptitle(f\"Recording: {rec_name} — mean waveforms ({n_units} units)\", fontsize=10)\n", "    fig.tight_layout(rect=[0, 0, 1, 0.94])\n", "\n", "    if save:\n", "        plt.savefig(f\"{outdir}/waveforms_{rec_name}.png\", dpi=200)\n", "    plt.show()\n", "\n", "# Build dictionary of the recordings of interest\n", "recordings = {a\n", "    \"b27\": data[data[\"animal_id\"] == \"b27\"],\n", "    \"b28\": data[data[\"animal_id\"] == \"b28\"],\n", "    \"b30\": data[data[\"animal_id\"] == \"b30\"],\n", "}\n", "\n", "# Plot all of them\n", "plot_all_recordings(recordings)\n"]}, {"cell_type": "code", "execution_count": 58, "id": "65acb127", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_units_grid_with_shading\n", "\n", "# Build a mapping from unit id -> unit class for border coloring\n", "unit_class_map = dict(zip(data['unit'], data['unit_class']))\n", "\n", "# Create one composite figure per recording (animal_id)\n", "grid_params = dict(\n", "    bin_size=10, t_start=0, t_end=None, n_cols=4,\n", "    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,\n", "    margins=(0.06, 0.92, 0.08, 0.9), dpi=200\n", ")\n", "for animal, df_sub in data.groupby('animal_id'):\n", "    out_file = f'{animal}_modulation_units_grid.png'\n", "    suptitle = f'{animal}: FR with balloon intervals'\n", "    _ = plot_units_grid_with_shading(\n", "        df_sub,\n", "        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],\n", "        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],\n", "        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],\n", "        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],\n", "        unit_class_map=unit_class_map, unit_col_name='unit'\n", "    )"]}, {"cell_type": "code", "execution_count": 57, "id": "2a3651be", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'plot_all_recordings' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[57], line 11\u001b[0m\n\u001b[0;32m      2\u001b[0m recordings \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m      3\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb24\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb24\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m      4\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb25\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb25\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m      7\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb30\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb30\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m      8\u001b[0m }\n\u001b[0;32m     10\u001b[0m \u001b[38;5;66;03m# Plot all of them\u001b[39;00m\n\u001b[1;32m---> 11\u001b[0m \u001b[43mplot_all_recordings\u001b[49m(recordings)\n", "\u001b[1;31mNameError\u001b[0m: name 'plot_all_recordings' is not defined"]}], "source": ["# Build dictionary of the recordings of interest\n", "recordings = {\n", "    \"b24\": data[data[\"animal_id\"] == \"b24\"],\n", "    \"b25\": data[data[\"animal_id\"] == \"b25\"],\n", "    \"b27\": data[data[\"animal_id\"] == \"b27\"],\n", "    \"b28\": data[data[\"animal_id\"] == \"b28\"],\n", "    \"b30\": data[data[\"animal_id\"] == \"b30\"],\n", "}\n", "\n", "# Plot all of them\n", "plot_all_recordings(recordings)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}