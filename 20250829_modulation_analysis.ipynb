# Imports and dataset paths
from pathlib import Path
import pandas as pd, numpy as np
from typing import Tuple, Sequence, Union, Dict, Any, Optional, List

paths = {  
    #'b27': Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0_p\\catgt\\unit_summary.pkl"),
    #'b28': Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p2_r1_g0\\catgt\\unit_summary.pkl"),
    'b30': Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt\\unit_summary.pkl")
         }
dfs = []
for name, p in paths.items():
    if not p.exists(): print(f"⚠ Missing: {p}"); continue
    df = pd.read_pickle(p).copy(); dfs.append(df)
data = pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()
print(f"Loaded {len(data)} rows (units) from: {[d for d in paths if (paths[d]).exists()]}")

data['unit'] = data['animal_id'] + '_' + data['unit'].astype(str)
data.drop(columns=['spike_times_samples', 'peak_channel', 'depth'], inplace=True)

import numpy as np

data["balloon_off_sec"] = data["balloon_off_sec"].apply(
    lambda offs: [[max(e - 200.0, 0.0), max(e, 0.0)] if np.isfinite(e) else [s, e] for s, e in offs]
)

# Apply functions to DataFrame

from modulation_functions import (
    modulation_index_single_trial,
    compute_trialwise_mi,
    compute_trialwise_mi_null,
)

data['modulation_index'] = data.apply(compute_trialwise_mi, axis=1)

data['modulation_index_null'] = data.apply(
    lambda r: compute_trialwise_mi_null(
        r,
        window_len=40,            # samples
        n_iter=10000,              # how many times to resample the OFF baseline
        mi_formula='difference_over_sum',
        seed=42,                  # set for reproducibility; or None
    ),
    axis=1,
)

from modulation_functions import modulation_flag, compute_trialwise_modulation_flags

# Add the -1/0/1 flags per trial
data['modulated_trial'] = data.apply(
    lambda r: compute_trialwise_modulation_flags(r, lower_quantile=0.1, upper_quantile=0.9),
    axis=1
)

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import TwoSlopeNorm

def plot_mi_heatmap(
    data,
    *,
    mi_col: str = "modulation_index",
    unit_col: str = "unit",
    vmax: float | None = None
):
    """
    Heatmap of modulation index (trials × units), diverging colormap:
      blue < 0 < red, with 0 in white. NaNs shown in light gray.

    X-axis: Unit id (unit names from `unit_col`)
    Y-axis: trial1, trial2, ...
    """
    # Collect MI lists per unit
    mi_lists = data[mi_col].tolist()
    n_units = len(mi_lists)
    max_trials = max((len(m) for m in mi_lists), default=0)

    # Build matrix (units x trials), then transpose to (trials x units)
    M = np.full((n_units, max_trials), np.nan, dtype=float)
    for i, mis in enumerate(mi_lists):
        M[i, :len(mis)] = np.asarray(mis, dtype=float)
    M = M.T  # now shape = (max_trials, n_units): rows=trials, cols=units

    # Symmetric normalization around 0
    if vmax is None:
        finite = M[np.isfinite(M)]
        abs_max = np.max(np.abs(finite)) if finite.size else 1.0
    else:
        abs_max = float(vmax)
    norm = TwoSlopeNorm(vmin=-abs_max, vcenter=0.0, vmax=+abs_max)

    # Diverging colormap with white center; gray for NaN
    cmap = plt.get_cmap("bwr").copy()
    cmap.set_bad("0.85", alpha=1.0)  # NaNs as light gray

    # Figure size: wide for many units, tall for many trials
    fig_w = max(6, 0.35 * n_units)
    fig_h = max(4, 0.28 * max_trials)

    fig, ax = plt.subplots(figsize=(fig_w, fig_h))
    im = ax.imshow(M, aspect="auto", interpolation="nearest", cmap=cmap, norm=norm)

    # Colorbar
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label("Modulation index")

    # Tick labels
    # X: units
    units = data[unit_col].astype(str).tolist() if unit_col in data.columns else [str(i) for i in range(n_units)]
    ax.set_xticks(np.arange(n_units))
    ax.set_xticklabels(units, rotation=90, ha="center", va="top")

    # Y: trials as trial1, trial2, ...
    ax.set_yticks(np.arange(max_trials))
    ax.set_yticklabels([f"trial{i+1}" for i in range(max_trials)])
    ax.invert_yaxis()  # put trial1 at the top

    # Axis labels & title
    ax.set_xlabel("Unit id")
    ax.set_ylabel("(Trial #)")

    fig.tight_layout()
    plt.show()

# usage:
plot_mi_heatmap(data)


def classify_unit(modulated_trial):
    n = len(modulated_trial)
    n_pos = sum(x == 1 for x in modulated_trial)
    n_neg = sum(x == -1 for x in modulated_trial)

    if n_pos >= n / 2 and n_neg == 0:
        return "excited"
    elif n_neg >= n / 2 and n_pos == 0:
        return "inhibited"
    else:
        return "mixed/non-responsive"

data['unit_class'] = data['modulated_trial'].apply(classify_unit)


import matplotlib.pyplot as plt

class_order = ['excited', 'inhibited', 'mixed/non-responsive']
class_colors = {
    'excited': '#1b9e77',
    'inhibited': '#d95f02',
    'mixed/non-responsive': '#808080'
}

# counts across the whole dataset
counts = data['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)
total = counts.sum()

# labels with n values relative to total
labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]
colors = [class_colors[c] for c in class_order]

fig, ax = plt.subplots(figsize=(5, 5))
ax.pie(
    counts.values, labels=labels, colors=colors,
    autopct=lambda p: f'{p:.1f}%' if p > 0 else '', startangle=90, counterclock=False,
    wedgeprops=dict(linewidth=1, edgecolor='white')
)
ax.set_title(f'Response classification (total n={total})')
ax.axis('equal')
plt.show()


import matplotlib.pyplot as plt

class_order = ['excited', 'inhibited', 'mixed/non-responsive']
class_colors = {'excited': '#1b9e77', 'inhibited': '#d95f02', 'mixed/non-responsive': '#808080'}
subsets = {k: v.copy() for k, v in data.groupby('animal_id')}

animals = ['b27', 'b28', 'b30']

fig, axes = plt.subplots(1, 4, figsize=(16, 4))  # 1x4 layout
axes = axes.flatten()

for ax, animal in zip(axes, animals):
    df_sub = subsets.get(animal, None)
    if df_sub is None or df_sub.empty:
        ax.axis('off')
        ax.set_title(f'{animal} (no data)', pad=2)
        continue

    counts = df_sub['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)
    total = int(counts.sum())

    # labels for legend
    labels = [f'{cls}\n(n={counts[cls]}/{total})' for cls in class_order]  # split in multiple lines
    colors = [class_colors[c] for c in class_order]

    wedges, texts, autotexts = ax.pie(
        counts.values,
        colors=colors,
        autopct=lambda p: f'{p:.1f}%' if p > 0 else '',
        startangle=90,
        counterclock=False,
        wedgeprops=dict(linewidth=1, edgecolor='white'),
        textprops={'fontsize': 8}  # smaller %
    )

    # legend outside
    ax.legend(
        wedges, labels,
        loc='upper center',
        bbox_to_anchor=(0.5, -0.05),
        fontsize=7,  # smaller font
        ncol=1,
        frameon=False
    )

    ax.set_title(f'{animal} (n={total} units)', pad=2)
    ax.axis('equal')

fig.tight_layout()
fig.savefig('unit_classification_by_recording.png', dpi=200, bbox_inches='tight')
plt.show()


from plotting_functions import plot_units_grid_with_shading

# Build a mapping from unit id -> unit class for border coloring
unit_class_map = dict(zip(data['unit'], data['unit_class']))

# Create one composite figure per recording (animal_id)
grid_params = dict(
    bin_size=10, t_start=0, t_end=None, n_cols=4,
    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,
    margins=(0.06, 0.92, 0.08, 0.9), dpi=200
    )
    for animal, df_sub in data.groupby('animal_id'):
        out_file = f'{animal}_modulation_units_grid.png'
        suptitle = f'{animal}: FR with balloon intervals'
        _ = plot_units_grid_with_shading(
            df_sub,
            bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],
            n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],
            wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],
            suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],
            unit_class_map=unit_class_map, unit_col_name='unit'
        )

# show a table how many excited inhibited and mixed units we have per animal_id, and summarize, with percentages

data.groupby(['animal_id', 'unit_class']).size().unstack(fill_value=0)


import numpy as np
import math
from matplotlib import pyplot as plt

def _get_time_vec(tv):
    if tv is None:
        return lambda i: None
    if isinstance(tv, np.ndarray) and tv.ndim == 1 and tv.dtype != object:
        return lambda i: tv  # shared
    if hasattr(tv, "iloc"):  # pandas Series
        return lambda i: tv.iloc[i]
    return lambda i: tv[i]  # list / object array

def _series_to_list(x):
    return x.tolist() if hasattr(x, "tolist") else list(x)

def plot_recording_waveforms(
    rec_name, rec_data, 
    max_cols=8, figsize_per_subplot=(1.5, 1.8),
    save=True, outdir="."
):
    # Accept DataFrame slice or dict
    if hasattr(rec_data, "__getitem__") and not isinstance(rec_data, dict):
        if len(rec_data) == 0:
            print(f"[skip] {rec_name}: no units found.")
            return
        wf = rec_data["waveform_mean"]
        waveforms = _series_to_list(wf)
        units = _series_to_list(rec_data["unit"]) if "unit" in rec_data else None
        tv_col = rec_data["waveform_time_vector_ms"] if "waveform_time_vector_ms" in rec_data else None
    else:
        if rec_data is None:
            print(f"[skip] {rec_name}: data is None.")
            return
        wf = rec_data.get("waveform_mean", [])
        if isinstance(wf, np.ndarray) and wf.ndim == 2:
            waveforms = [wf[i, :] for i in range(wf.shape[0])]
        else:
            waveforms = list(wf)
        units = rec_data.get("unit", None)
        tv_col = rec_data.get("waveform_time_vector_ms", None)

    n_units = len(waveforms)
    if n_units == 0:
        print(f"[skip] {rec_name}: no units found.")
        return

    tv_get = _get_time_vec(tv_col)

    # Grid
    ncols = min(max_cols, max(1, int(math.ceil(math.sqrt(n_units)))))
    nrows = int(math.ceil(n_units / ncols))

    fig_w = figsize_per_subplot[0] * ncols
    fig_h = figsize_per_subplot[1] * nrows
    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(fig_w, fig_h))

    if not isinstance(axes, np.ndarray):
        axes = np.array([axes])
    axes = axes.reshape(nrows, ncols)

    for i, ax in enumerate(axes.flat):
        if i < n_units:
            y = np.asarray(waveforms[i]).ravel()
            x = tv_get(i)
            if x is None:
                x = np.arange(len(y))
            ax.plot(x, y, color="black")
            if units is not None and i < len(units):
                ax.set_title(f"Unit {units[i]}", fontsize=7)
            ax.set_xticks([]); ax.set_yticks([])
            ax.axis("off")
        else:
            ax.axis("off")

    # ---- Add 1 ms scalebar INSIDE the last subplot without changing limits ----
    last_ax = axes.flat[min(n_units, len(axes.flat)) - 1]
    # Save current limits (so the scalebar doesn't shrink the waveform)
    xlim = last_ax.get_xlim()
    ylim = last_ax.get_ylim()

    xspan = xlim[1] - xlim[0]
    yspan = ylim[1] - ylim[0]

    # Desired bar length (1 ms in data units)
    bar_len = 1.0  # ms
    # If the axis window is <1 ms, fall back to 50% of the span
    if bar_len > xspan:
        bar_len = 0.5 * xspan

    # Place near bottom-right with a small margin
    x_margin = 0.05 * xspan
    y_margin = 0.08 * yspan
    x_end = xlim[1] - x_margin
    x_start = x_end - bar_len
    y_bar = ylim[0] + y_margin

    # Draw the bar + label, then restore limits so autoscale doesn't change
    last_ax.plot([x_start, x_end], [y_bar, y_bar], color="black", lw=2, solid_capstyle="butt", clip_on=False, zorder=5)
    last_ax.text((x_start + x_end) / 2, y_bar, "1 ms", ha="center", va="bottom", fontsize=7)

    last_ax.set_xlim(xlim)
    last_ax.set_ylim(ylim)
    # --------------------------------------------------------------------------

    fig.suptitle(f"Recording: {rec_name} — mean waveforms ({n_units} units)", fontsize=10)
    fig.tight_layout(rect=[0, 0, 1, 0.94])

    if save:
        plt.savefig(f"{outdir}/waveforms_{rec_name}.png", dpi=200)
    plt.show()

# Build dictionary of the recordings of interest
recordings = {a
    "b27": data[data["animal_id"] == "b27"],
    "b28": data[data["animal_id"] == "b28"],
    "b30": data[data["animal_id"] == "b30"],
}

# Plot all of them
plot_all_recordings(recordings)


from plotting_functions import plot_units_grid_with_shading

# Build a mapping from unit id -> unit class for border coloring
unit_class_map = dict(zip(data['unit'], data['unit_class']))

# Create one composite figure per recording (animal_id)
grid_params = dict(
    bin_size=10, t_start=0, t_end=None, n_cols=4,
    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,
    margins=(0.06, 0.92, 0.08, 0.9), dpi=200
)
for animal, df_sub in data.groupby('animal_id'):
    out_file = f'{animal}_modulation_units_grid.png'
    suptitle = f'{animal}: FR with balloon intervals'
    _ = plot_units_grid_with_shading(
        df_sub,
        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],
        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],
        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],
        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],
        unit_class_map=unit_class_map, unit_col_name='unit'
    )

# Build dictionary of the recordings of interest
recordings = {
    "b24": data[data["animal_id"] == "b24"],
    "b25": data[data["animal_id"] == "b25"],
    "b27": data[data["animal_id"] == "b27"],
    "b28": data[data["animal_id"] == "b28"],
    "b30": data[data["animal_id"] == "b30"],
}

# Plot all of them
plot_all_recordings(recordings)
