% --- Load channel 1 ---
basepath = 'Z:\users\izouridis\projects\bal_npx\data\b16\b16_p2_r1_g0\';
load(fullfile(basepath, 'channel_2.mat'));

% --- Create FieldTrip data structure ---
data = [];
data.label{1} = 'piezo';
data.trial{1} = channel_2;
data.time{1} = time_axis;
data.fsample = 30303;

% --- Preprocess: Demean, detrend, bandpass filter for heart signal (5–15 Hz) ---
cfg = [];
cfg.demean           = 'yes';
cfg.polyremoval      = 'yes';
cfg.polyorder        = 2;

cfg.bpfilter         = 'yes';
cfg.bpfreq           = [5 15];               % Heartbeat frequency band
cfg.bpfilttype       = 'but';
cfg.bpfiltord        = 2;
cfg.bpinstabilityfix = 'reduce';

data_filtered = ft_preprocessing(cfg, data);

% --- Resample to 1000 Hz for convenience ---
resamplefs = 1000;
cfg = [];
cfg.resamplefs = resamplefs;
data_resampled = ft_resampledata(cfg, data_filtered);

% --- Extract signal and time ---
signal = data_resampled.trial{1};
time   = data_resampled.time{1};

% --- Standardize the signal ---
signal_z = zscore(signal);

% --- Peak detection for heartbeats ---
min_heart_rate = 6;   % Hz (~360 bpm)
max_heart_rate = 10;  % Hz (~600 bpm)

min_peak_distance = 1 / max_heart_rate;  % in seconds (0.1s)
max_peak_distance = 1 / min_heart_rate;  % in seconds (0.167s)

[pks, locs] = findpeaks(signal_z, time, ...
    'MinPeakDistance', min_peak_distance, ...s
    'MinPeakProminence', 0.3);  % adjust if needed

% --- Remove peaks that are too far apart (> max expected interval) ---
ibi = diff(locs);                        % inter-beat intervals
valid_idx = ibi <= max_peak_distance;   % keep plausible intervals
locs_valid = locs([true valid_idx]);     % align with ibi_valid

% --- Compute instantaneous heart rate (Hz) ---
ibi_valid = diff(locs_valid);
rate_inst = 1 ./ ibi_valid;                                % Hz
rate_time = locs_valid(1:end-1) + ibi_valid / 2;           % midpoints

% --- Interpolate to full time vector ---
heart_rate = interp1(rate_time, rate_inst, time, 'linear', 'extrap');
heart_rate(time < rate_time(1) | time > rate_time(end)) = NaN;

% --- Plot heart rate ---
plot(time, heart_rate);
xlabel('Time (s)');
ylabel('Heart Rate (Hz)');
title('Instantaneous Heart Rate (5–15 Hz)');
% 
% --- Manual annotation of behavioral states ---
cfg = [];
cfg.artfctdef.bal_on.artifact = [];
cfg.artfctdef.bal_off.artifact = [];
cfg.viewmode = 'vertical';
cfg.blocksize = 100;  % seconds
cfg = ft_databrowser(cfg, data_filtered);

% % --- Optional: Save annotations ---
% bal_on_sec = cfg.artfctdef.bal_on.artifact / resamplefs;
% bal_off_sec = cfg.artfctdef.bal_off.artifact / resamplefs;
% writematrix(bal_on_sec, fullfile(basepath, 'bal_on_sec.txt'), 'Delimiter', '\t');
% writematrix(bal_off_sec, fullfile(basepath, 'bal_off_sec.txt'), 'Delimiter', '\t');
