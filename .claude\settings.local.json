{"permissions": {"allow": ["Bash(grep:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"rawWaveforms|raw_waveforms\" /home/<USER>/Dropbox/Python/bombcell/pyBombCell/bombcell/unit_quality_gui.py)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n -A 5 -B 5 \"rawWv|raw_waveform\" /home/<USER>/Dropbox/Python/bombcell/pyBombCell/bombcell/unit_quality_gui.py)", "Bash(git checkout:*)", "Bash(cp:*)"], "deny": []}}