{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory Analysis: b24 and b25 datasets\n", "\n", "This notebook loads unit data for two specific recordings (b24 and b25), inspects basic structure, and optionally opens the standalone time-series viewer for interactive visualization with balloon event overlays."]}, {"cell_type": "code", "execution_count": 26, "id": "2ad46552", "metadata": {}, "outputs": [], "source": ["# Imports and dataset paths\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "from typing import Tuple, Sequence, Union, Dict, Any, Optional, List"]}, {"cell_type": "markdown", "id": "d414ab77", "metadata": {}, "source": ["## Basic info and sample rows\n", "We print number of units, columns and dtypes, and show a small sample for each dataset."]}, {"cell_type": "code", "execution_count": 27, "id": "72fc0a02", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 49 rows from: ['b24', 'b25']\n"]}], "source": ["paths = {'b24': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\\unit_summary.pkl'),\n", "         'b25': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\\unit_summary.pkl')}\n", "dfs = []\n", "for name, p in paths.items():\n", "    if not p.exists(): print(f\"⚠ Missing: {p}\"); continue\n", "    df = pd.read_pickle(p).copy(); dfs.append(df)\n", "data = pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()\n", "print(f\"Loaded {len(data)} rows from: {[d for d in paths if (paths[d]).exists()]}\")\n", "\n", "data['unit'] = data['animal_id'] + '_' + data['unit'].astype(str)\n", "data.drop(columns=['spike_times_samples', 'peak_channel', 'depth', 'depth_penetration_um'], inplace=True)"]}, {"cell_type": "code", "execution_count": 28, "id": "468d9daf", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "data[\"balloon_off_sec\"] = data[\"balloon_off_sec\"].apply(\n", "    lambda offs: [[max(e - 150.0, 0.0), max(e, 0.0)] if np.isfinite(e) else [s, e] for s, e in offs]\n", ")"]}, {"cell_type": "code", "execution_count": 29, "id": "238ae7dc", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "# mask: units whose name starts with \"b25\"\n", "mask = data[\"unit\"].astype(str).str.startswith(\"b25\")\n", "\n", "def _filter_before_tmax(on_list, off_list, tmax=2000.0):\n", "    kept_on, kept_off = [], []\n", "    for on, off in zip(on_list, off_list):\n", "        on0, on1 = float(on[0]), float(on[1])\n", "        off0, off1 = float(off[0]), float(off[1])\n", "        if (on0 < tmax and on1 < tmax) and (off0 < tmax and off1 < tmax):\n", "            kept_on.append([on0, on1])   # keep list-of-lists format\n", "            kept_off.append([off0, off1])\n", "    return kept_on, kept_off\n", "\n", "filtered_pairs = data.loc[mask].apply(\n", "    lambda r: _filter_before_tmax(r[\"balloon_on_sec\"], r[\"balloon_off_sec\"], tmax=2000.0),\n", "    axis=1\n", ")\n", "\n", "data.loc[mask, \"balloon_on_sec\"]  = filtered_pairs.map(lambda x: x[0])\n", "data.loc[mask, \"balloon_off_sec\"] = filtered_pairs.map(lambda x: x[1])"]}, {"cell_type": "markdown", "id": "7c905542", "metadata": {}, "source": ["## Compute modulation per unit and trial\n", "This section applies the imported functions to the loaded DataFrame.\n", "- Observed MI: ON vs OFF-tail per trial using balloon_off_sec_tail (last 60 s of OFF).\n", "- Null MI: per OFF trial, compare whole OFF vs random subwindows inside OFF."]}, {"cell_type": "code", "execution_count": 30, "id": "9601c9e7", "metadata": {}, "outputs": [], "source": ["# Apply functions to DataFrame\n", "\n", "from modulation_functions import (\n", "    modulation_index_single_trial,\n", "    compute_trialwise_mi,\n", "    compute_trialwise_mi_null,\n", ")\n", "\n", "data['modulation_index'] = data.apply(compute_trialwise_mi, axis=1)\n", "\n", "data['modulation_index_null'] = data.apply(\n", "    lambda r: compute_trialwise_mi_null(\n", "        r,\n", "        window_len=60,             # samples\n", "        n_iter=10000,              # how many times to resample the OFF baseline\n", "        mi_formula='difference_over_sum',\n", "        seed=42,                   # set for reproducibility; or None\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "markdown", "id": "mod-flag-intro", "metadata": {}, "source": ["## Modulation flag (per trial)\n", "We classify each trial as enhanced (+1), suppressed (−1), or not modulated (0)\n", "by comparing the observed MI against its per-trial null distribution.\n", "Ties and invalid/null cases return 0.\n"]}, {"cell_type": "code", "execution_count": 31, "id": "mod-flag-import", "metadata": {}, "outputs": [], "source": ["from modulation_functions import modulation_flag, compute_trialwise_modulation_flags\n", "\n", "# Add the -1/0/1 flags per trial\n", "data['modulated_trial'] = data.apply(\n", "    lambda r: compute_trialwise_modulation_flags(r, lower_quantile=0.05, upper_quantile=0.95),\n", "    axis=1\n", ")"]}, {"cell_type": "code", "execution_count": 32, "id": "92482e9d", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAxwAAAVSCAYAAACMhf0uAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzde1xUZf4H8M9xgEGYGML7IKIiYRd0SpaL1U9q3SW878Zi5O6IFYiFYVQSJWXkwloiKhZd0CSyjUrWXAqDBLqipBNmN8PKwULYMh1QYByG8/uj9awTiMAwzgx83q/Xea3nme95nu9znPY1X895zhFEURRBRERERERkBUNsnQAREREREQ1cLDiIiIiIiMhqWHAQEREREZHVsOAgIiIiIiKrYcFBRERERERWw4KDiIiIiIishgUHERERERFZDQsOIiIiIiKyGhYcRERERERkNSw4iAY5QRCwevXqPh07fvx4xMbG9ms+sbGxGD9+fL/22RNHjx6FIAjYtm2bXffZG08++SQmT56Mjo4Oq45jyXfIUWzbtg2CIODo0aO9PrYv3+ndu3dDoVDgp59+6vV4RET2hgUHkR0492NGEAR8+OGHnT4XRRE+Pj4QBAFz5syxQYb9q76+HqtXr0ZNTY2tUxmwmpqasHbtWqSkpGDIkEv7f/Uff/wxVq9ejVOnTl3ScXvqmWeesVkR2FO33HILJk2ahMzMTFunQkRkMRYcRHbE1dUVr7zySqf29957Dz/88APkcrkNsup/9fX1ePzxx7ssOF544QUcPnz4kufk6+uL1tZW/O1vf7vkY1vD1q1b0d7ejpiYGKuP1drailWrVkn7H3/8MR5//PEBVXD87W9/Q2trK3x9fa2TVBeWLl2K5557Ds3NzZdsTCIia2DBQWRHZs2ahddffx3t7e1m7a+88gqmTZuG0aNH2yizS8fZ2dkmhZUgCHB1dYVMJrvkY1vDiy++iHnz5sHV1dXqY7m6usLJycnq47zxxhsXLGIaGhpQXFzc72OeOXMGACCTyeDq6gpBEPp9jAu59dZbYTAY8Prrr1+yMYmIrIEFB5EdiYmJwYkTJ1BWVia1nT17Fm+88QZuv/32Lo85c+YM7r//fvj4+EAulyMgIADr1q2DKIpmcQaDAffddx9GjBiByy67DPPmzcMPP/zQqb8L3W++evXqi/7Y+uWXX/DAAw8gMDAQCoUCHh4eiIyMxMGDB6WYyspK/O53vwMALFmyRLqV7Ny/OHc1fk/nKAgCEhMTsXPnTlxzzTWQy+W4+uqrsXv37m7zBrpebxEbGwuFQoEff/wRCxYsgEKhwIgRI/DAAw/AZDKZHX/q1CnExsZCqVTC09MTixcvvuCP46+//hpRUVHw8vKCq6srgoKCsGvXLunz//znPxgxYgTCw8PN5njkyBG4u7tj4cKF3c7l+++/x2effYaZM2eatVdWVkIQBFRWVvbr3M9fw7F69Wo8+OCDAIAJEyZIf7/n1j6UlZXhhhtugKenJxQKBQICAvDwww93Ox8AaG5uRnx8PObMmYOWlhazz06dOoWIiAgkJCTg7Nmz3fYzfvx4fPHFF3jvvfek3MLDwwH879bG9957D3fffTdGjhyJsWPHmn12/hqON998E7Nnz4ZKpYJcLoefnx+eeOKJTuenK6+++iqmTZuGyy67DB4eHggMDMTGjRvNYkaOHIkpU6bgzTffvGh/RET2zPr/JEVEPTZ+/HiEhYXhn//8JyIjIwEAJSUl0Ov1uO2227Bp0yazeFEUMW/ePFRUVODOO++EWq3GO++8gwcffBA//vgjsrOzpdi77roLL7/8Mm6//XZMnz4d5eXlmD17dr/m/91332Hnzp34y1/+ggkTJqCxsRHPPfccZsyYgS+//BIqlQpXXnkl0tPT8eijjyI+Ph433ngjAGD69Old9tmbOQLAhx9+iKKiItx999247LLLsGnTJtx6662oq6vDsGHDej0nk8mEiIgIhISEYN26dXj33XeRlZUFPz8/LFu2TMpx/vz5+PDDD5GQkIArr7wS//rXv7B48eJO/X3xxRe4/vrr4e3tjYceegju7u547bXXsGDBAuzYsQN/+tOfMHLkSOTm5uIvf/kLcnJycO+996KjowOxsbG47LLL8Mwzz3Sb88cffwwAuO6663o9397O/bf+/Oc/45tvvsE///lPZGdnY/jw4QCAESNG4IsvvsCcOXMwZcoUpKenQy6X48iRI/joo48umstll12GnTt3IiIiAlFRUXjzzTfh7OyMlpYWzJkzB0ePHkVFRQVcXFy67WfDhg1Yvnw5FAoFHnnkEQDAqFGjzGLuvvtujBgxAo8++qh0haMr27Ztg0KhQHJyMhQKBcrLy/Hoo4+iqakJTz311AWPKysrQ0xMDH7/+99j7dq1AICvvvoKH330EZKSksxip02bhp07d3Y7JyIiuycSkc29+OKLIgDxk08+ETdv3ixedtllYktLiyiKoviXv/xFvOmmm0RRFEVfX19x9uzZ0nE7d+4UAYhr1qwx6y8qKkoUBEE8cuSIKIqiWFNTIwIQ7777brO422+/XQQgPvbYY1Lb4sWLRV9f3045PvbYY+Jv/y/D19dXXLx4sbTf1tYmmkwms5jvv/9elMvlYnp6utT2ySefiADEF198sdM4vx2/p3MURVEEILq4uJi1HTx4UAQg5uTkdBrrt3n+NqfFixeLAMxyF0VRvPbaa8Vp06Z1yvHJJ5+U2trb28Ubb7yxU5+///3vxcDAQLGtrU1q6+joEKdPny76+/ubjRMTEyO6ubmJ33zzjfjUU0+JAMSdO3d2Ow9RFMVVq1aJAMTm5maz9oqKChGAWFFR0W9zF0Wx03foXK7ff/+9WVx2drYIQPzpp58uOocL2bVrl+jk5CQuXLhQbGtrEyMjI0VXV1exsrKyx31cffXV4owZMzq1n/vv8IYbbhDb29u7/Oz8OZ37b/R8S5cuFd3c3Mz+fn/7nU5KShI9PDw6jdGVjIwMEYDY2Nh48YkREdkp3lJFZGeio6PR2tqK4uJiNDc3o7i4+IK3U7399tuQyWS49957zdrvv/9+iKKIkpISKQ5Ap7gVK1b0a+5yuVx6IpLJZMKJEyek22a0Wm2f+uzpHM+ZOXMm/Pz8pP0pU6bAw8MD3333XZ/GB4CEhASz/RtvvNGsv7fffhtOTk5m/+ovk8mwfPlys+N++eUXlJeXIzo6Gs3Nzfj555/x888/48SJE4iIiEBtbS1+/PFHKX7z5s1QKpWIiopCWloa/va3v2H+/PkXzffEiRNwcnKCQqHo65QlF5t7b3h6egL49Vakvj6qd+7cudi6dStee+01XHHFFSgrK0NhYSFmzJjRp/66EhcX16O1PEOHDpX+fO7v88Ybb0RLSwu+/vrrCx7n6emJM2fOmN06eSGXX345AODnn3/uQeZERPaJBQeRnRkxYgRmzpyJV155BUVFRTCZTIiKiuoyVqfTQaVS4bLLLjNrv/LKK6XPz/3vkCFDzH6IA0BAQEC/5t7R0YHs7Gz4+/tDLpdj+PDhGDFiBD777DPo9fo+9dnTOZ4zbty4Tn1cfvnlOHnyZJ/Gd3V1xYgRI7rtT6fTYcyYMZ1+4P/2/B45cgSiKCItLQ0jRoww2x577DEAv67fOMfLywubNm3CZ599BqVS2emWOmvrydx7Y+HChbj++utx1113YdSoUbjtttvw2muv9br4+Nvf/oYbb7wRdXV1mDVrFubNm9enfC5kwoQJPYr74osv8Kc//QlKpRIeHh4YMWIE/vrXvwJAt9/3u+++G1dccQUiIyMxduxY3HHHHRdcZyT+dw3PpVysTkTU37iGg8gO3X777YiLi0NDQwMiIyOlfxm+FC70w6YnC2EzMjKQlpaGO+64A0888QS8vLwwZMgQrFixwuovnzvnQv8yLf5mgbml/fXFuXPwwAMPICIiosuYSZMmme2/8847AICTJ0/ihx9+6NF3YdiwYWhvb0dzc7NZodbbv9v+fmLX0KFD8f7776OiogJvvfUWdu/ejcLCQtx8880oLS3t8XgpKSl4//338cc//hG7du3CP/7xDzz00EP9mufFnDp1CjNmzICHhwfS09Ph5+cHV1dXaLVapKSkdPt9HzlyJGpqavDOO++gpKQEJSUlePHFF6HRaJCfn28We664O7cWhojIEfEKB5Ed+tOf/oQhQ4Zg7969F7ydCvj13RH19fWdntN/7naOc+8M8PX1RUdHB7799luzuK7ed3H55Zd3+XSl315J6Mobb7yBm266CVu2bMFtt92GP/7xj5g5c2an/nrzr7U9naMt+fr64vjx4zh9+rRZ+2/P78SJEwH8+ujfmTNndrmdXyDs3r0beXl5WLlyJUaMGIHFixd3emRyVyZPngzg16dVne/c7Tm//fvoyd9tb3T39ztkyBD8/ve/x/r16/Hll1/i73//O8rLy1FRUdGjvteuXYsnn3wS6enpeOedd7BixQqkpqbi+eef75f8eqqyshInTpzAtm3bkJSUhDlz5mDmzJnSOb4YFxcXzJ07F8888wy+/fZbLF26FC+99BKOHDliFvf9999LVwqJiBwVCw4iO6RQKJCbm4vVq1dj7ty5F4ybNWsWTCYTNm/ebNaenZ0NQRCkJ12d+9/f3pKzYcOGTn36+flBr9fjs88+k9qOHz+Of/3rXxfNWyaTdbqS8Prrr5utSwAAd3d3AJ1/+Halp3O0pVmzZqG9vR25ublSm8lkQk5OjlncyJEjER4ejueeew7Hjx/v1M9PP/0k/fnUqVO46667EBwcjIyMDOTl5UGr1SIjI+Oi+YSFhQEA9u/fb9bu6+sLmUyG999/36z9Yk+96q0L/f3+8ssvnWLVajWAXx/bfDEvvPACHnroIdx3331IS0sDAKxfvx6xsbFYtmwZXnvttR7nZ+lLCc9djTn/+3727NkencsTJ06Y7Q8ZMgRTpkwB0Pk8HDhwQPr7JCJyVLylishOdfVI1d+aO3cubrrpJjzyyCM4evQopk6ditLSUrz55ptYsWKFtGZDrVYjJiYGzzzzDPR6PaZPn449e/Z0+tdUALjtttuQkpKCP/3pT7j33nvR0tKC3NxcXHHFFRdd+D1nzhykp6djyZIlmD59Og4dOoTt27dL/7J/jp+fHzw9PfHss8/isssug7u7O0JCQrq8d76nc7SluXPn4vrrr8dDDz2Eo0eP4qqrrkJRUVGX9/E//fTTuOGGGxAYGIi4uDhMnDgRjY2NqKqqwg8//CC9syQpKQknTpzAu+++C5lMhltuuQV33XUX1qxZg/nz52Pq1KkXzGfixIm45ppr8O677+KOO+6Q2pVKpfSoXUEQ4Ofnh+LiYrN1I/1h2rRpAIBHHnkEt912G5ydnTF37lykp6fj/fffx+zZs+Hr64v//Oc/eOaZZzB27FjccMMN3fbZ3NyMlJQULFmyBFlZWVK7IAjIy8vDqVOncP/992PBggUXfTTutGnTkJubizVr1mDSpEkYOXIkbr755l7Ncfr06bj88suxePFi3HvvvRAEAQUFBT26de+uu+7CL7/8gptvvhljx46FTqdDTk4O1Gq1tDYJ+HU9z2effYZ77rmnV7kREdkd2z0gi4jOOf+xuN357WNxRVEUm5ubxfvuu09UqVSis7Oz6O/vLz711FNiR0eHWVxra6t47733isOGDRPd3d3FuXPniseOHev0SFNRFMXS0lLxmmuuEV1cXMSAgADx5Zdf7vFjce+//35xzJgx4tChQ8Xrr79erKqqEmfMmNHpMaRvvvmmeNVVV4lOTk5mj2Tt6rG8PZ0jAPGee+7p8rydn2dXLvRoWHd3906xXZ2LEydOiH/7299EDw8PUalUin/729/ETz/9tMvH/3777beiRqMRR48eLTo7O4ve3t7inDlzxDfeeEM6NwDErKwss+OamppEX19fcerUqeLZs2e7nc/69etFhULR6dGtP/30k3jrrbeKbm5u4uWXXy4uXbpU/Pzzzy2ae1ffoSeeeEL09vYWhwwZIj1Ods+ePeL8+fNFlUoluri4iCqVSoyJiRG/+eabbudyzqFDhy74KNm2tjbxq6++6lE/DQ0N4uzZs8XLLrtMBCB9N7v777Crx+J+9NFHYmhoqDh06FBRpVKJK1euFN95551Ojx7+7Xf6jTfeEP/4xz+KI0eOFF1cXMRx48aJS5cuFY8fP242Zm5urujm5iY2NTX1aF5ERPZKEMU+rqQkIiK7pdfrMXHiRDz55JO48847bZ0O9cG1116L8PDwTi+3JCJyNCw4iIgGqLVr1+LFF1/El19+Kb0fhRzD7t27ERUVhe+++w4jR460dTpERBZhwUFERERERFbDf/IiIiIiIiKrYcFBRERERNQP3n//fcydOxcqlQqCIGDnzp0XPaayshLXXXcd5HI5Jk2ahG3btnWKefrppzF+/Hi4uroiJCQE1dXV/Z+8FbHgICIiIiLqB2fOnMHUqVPx9NNP9yj++++/x+zZs3HTTTehpqYGK1aswF133YV33nlHiiksLERycjIee+wxaLVaTJ06FREREf3+SHNr4hoOIiIiIqJ+JggC/vWvf2HBggUXjElJScFbb72Fzz//XGq77bbbcOrUKezevRsAEBISgt/97nfSC3A7Ojrg4+OD5cuX46GHHrLqHPoLX/xnJzo6OlBfX4/LLrsMgiDYOh0iIiKyA6Ioorm5GSqVymZPm2tra8PZs2dtMrY9EEWx028zuVwOuVxucd9VVVWYOXOmWVtERARWrFgBADh79iwOHDiA1NRU6fMhQ4Zg5syZqKqqsnj8S4UFh52or6+Hj4+PrdMgIiIiO3Ts2DGMHTv2ko/b1taGCUOHouGSj2w/FAoFTp8+bdb22GOPYfXq1Rb33dDQgFGjRpm1jRo1Ck1NTWhtbcXJkydhMpm6jPn6668tHv9SYcFhJy677LL//ukYAA9bpkJEdqCw0NYZkD245Yc8W6dANtbU1gafRx4573fCpXX27Fk0YPD+OmkC4HP6NI4dOwYPj/+dgf64ujGYsOCwE/+7VOeBwfmfNBGdz83N1hmQPfAYOtTWKZCdsPXt1oP914mHh4dZwdFfRo8ejcbGRrO2xsZGeHh4YOjQoZDJZJDJZF3GjB49ut/zsRY+pYqIiIiIyAbCwsKwZ88es7aysjKEhYUBAFxcXDBt2jSzmI6ODuzZs0eKcQQsOIiIiIiI+sHp06dRU1ODmpoaAL8+9rampgZ1dXUAgNTUVGg0Gik+ISEB3333HVauXImvv/4azzzzDF577TXcd999UkxycjJeeOEF5Ofn46uvvsKyZctw5swZLFmy5JLOzRK8pYqIiIiIuicIv26DjSj+uvXQ/v37cdNNN0n7ycnJAIDFixdj27ZtOH78uFR8AMCECRPw1ltv4b777sPGjRsxduxY5OXlISIiQopZuHAhfvrpJzz66KNoaGiAWq3G7t27Oy0kt2d8D4edaGpqglKpBKDH4L5LkogA4N//tnUGZA/mHMu1dQpkY02trVDefz/0er1V1hBcdPz//j7RCwI8BmHB0SSKUIqizc7/QMFbqoiIiIiIyGoGRMERHh4uvSCFiIiIiIjsx4AoOLpjNBqRkpKCwMBAuLu7Q6VSQaPRoL6+vst4g8EAtVoNQRCkBT898dlnn+HGG2+Eq6srfHx88OSTT/bTDIiIiIiIHNeALzhaWlqg1WqRlpYGrVaLoqIiHD58GPPmzesyfuXKlVCpVL0ao6mpCX/84x/h6+uLAwcO4KmnnsLq1avx/PPP98cUiIiIiGxryJDBu5HFBsxZbG9vR2JiIpRKJYYPH460tDSIogilUomysjJER0cjICAAoaGh2Lx5Mw4cOGD2lAAAKCkpQWlpKdatW9ersbdv346zZ89i69atuPrqq3Hbbbfh3nvvxfr16/tzikREREREDmfAFBz5+flwcnJCdXU1Nm7ciPXr1yMvL6/LWL1eD0EQ4OnpKbU1NjYiLi4OBQUFcOvlK36rqqrwf//3f3BxcZHaIiIicPjwYZw8ebLLYwwGA5qamsw2IiIiIqKBZsAUHD4+PsjOzkZAQAAWLVqE5cuXIzs7u1NcW1sbUlJSEBMTIz3eTBRFxMbGIiEhAUFBQb0eu6GhodOzkM/tNzQ0dHlMZmYmlEqltPn4+PR6XCIiIiIiezdgCo7Q0FAI5z0fOiwsDLW1tTCZTFKb0WhEdHQ0RFFEbu7/nm2ek5OD5uZmpKamXrJ8U1NTodfrpe3YsWOXbGwiIiIioktl0Lxp/FyxodPpUF5ebvbylvLyclRVVUEul5sdExQUhEWLFiE/P7/bvkePHo3GxkaztnP7o0eP7vIYuVzeaTwiIiIiuzRkyOB90/h5/3hNfTNgCo59+/aZ7e/duxf+/v6QyWRSsVFbW4uKigoMGzbMLHbTpk1Ys2aNtF9fX4+IiAgUFhYiJCTkomOHhYXhkUcegdFohLOzMwCgrKwMAQEBuPzyy/thdkREREREjmnA3FJVV1eH5ORkHD58GP/85z+Rk5ODpKQkGI1GREVFYf/+/di+fTtMJhMaGhrQ0NCAs2fPAgDGjRuHa665RtquuOIKAICfnx/Gjh170bFvv/12uLi44M4778QXX3yBwsJCbNy4EcnJyVadMxERERGRvRswVzg0Gg1aW1sRHBwMmUyGpKQkxMfHQ6fTYdeuXQAAtVptdkxFRQXCw8MtHlupVKK0tBT33HMPpk2bhuHDh+PRRx9FfHy8xX0TERERETmyAVFwVFZWSn8+fzE4AIwfPx6iKPaqv74cM2XKFHzwwQe9OoaIiIiIaKAbEAUHEREREVnRYF40ThYbMGs4rCkyMhIKhaLLLSMjw9bpERERERHZLV7h6IG8vDy0trZ2+ZmXl9clzoaIiIiIyHGw4OgBb29vW6dAREREROSQeEsVERERERFZDa9wEBEREVH3uGicLMCCw87oUzLgIZfbOg2ypZtvtnUGZA+Cg22dAdmDvZNtnQHZ2pkzts6AyGK8pYqIiIiIiKyGBQcREREREVkNCw4iIiIiIrIaruEgIiIiou5x0ThZgFc4iIiIiIjIalhwEBERERGR1bDgICIiIiIiqxkQBUd4eDhWrFhh6zSIiIiIiOg3BkTB0R2j0YiUlBQEBgbC3d0dKpUKGo0G9fX1XcYbDAao1WoIgoCampoejXH48GHcdNNNGDVqFFxdXTFx4kSsWrUKRqOxH2dCREREZCNDhgzejSw24J9S1dLSAq1Wi7S0NEydOhUnT55EUlIS5s2bh/3793eKX7lyJVQqFQ4ePNjjMZydnaHRaHDdddfB09MTBw8eRFxcHDo6OpCRkdGf0yEiIiIicigDpuBob29HYmIiCgoK4OzsjGXLliE9PR1KpRJlZWVmsZs3b0ZwcDDq6uowbtw4qb2kpASlpaXYsWMHSkpKejz2xIkTMXHiRGnf19cXlZWV+OCDDyyfGBERERGRAxsw14ny8/Ph5OSE6upqbNy4EevXr0deXl6XsXq9HoIgwNPTU2prbGxEXFwcCgoK4ObmZlEuR44cwe7duzFjxowLxhgMBjQ1NZltREREREQDzYApOHx8fJCdnY2AgAAsWrQIy5cvR3Z2dqe4trY2pKSkICYmBh4eHgAAURQRGxuLhIQEBAUF9TmH6dOnw9XVFf7+/rjxxhuRnp5+wdjMzEwolUpp8/Hx6fO4RERERET2asAUHKGhoRDOewNmWFgYamtrYTKZpDaj0Yjo6GiIoojc3FypPScnB83NzUhNTbUoh8LCQmi1Wrzyyit46623sG7dugvGpqamQq/XS9uxY8csGpuIiIjIamy9cJuLxh3agFnDcTHnig2dTofy8nLp6gYAlJeXo6qqCnK53OyYoKAgLFq0CPn5+T0a49xViquuugomkwnx8fG4//77IZPJOsXK5fJO4xERERERDTQDpuDYt2+f2f7evXvh7+8PmUwmFRu1tbWoqKjAsGHDzGI3bdqENWvWSPv19fWIiIhAYWEhQkJC+pRPR0cHjEYjOjo6uiw4iIiIiIgGgwFTcNTV1SE5ORlLly6FVqtFTk4OsrKyYDQaERUVBa1Wi+LiYphMJjQ0NAAAvLy84OLiYvakKgBQKBQAAD8/P4wdO/aiY2/fvh3Ozs4IDAyEXC7H/v37kZqaioULF8LZ2bn/J0tERERE5CAGTMGh0WjQ2tqK4OBgyGQyJCUlIT4+HjqdDrt27QIAqNVqs2MqKioQHh5u8dhOTk5Yu3YtvvnmG4iiCF9fXyQmJuK+++6zuG8iIiIiIkc2IAqOyspK6c/nLwYHgPHjx0MUxV7119tjFi5ciIULF/ZqDCIiIiKHMWQIcN7DeQaNXv6GpK5x6T0REREREVkNC44eiIyMhEKh6HLLyMiwdXpERERERHZrQNxSZW15eXlobW3t8jMvL69LnA0RERERkeNgwdED3t7etk6BiIiIiMghseAgIiIiou4JwuB863ZHh60zGBAG4TeHiIiIiIguFV7hsDOarx+Gs7OHrdMgGwpys3UGZA9SphttnQLZgU8UN9k6BbKx02iydQpEFuMVDiIiIiIishoWHEREREREZDW8pYqIiIiIujdkyOBcNE79gt8cIiIiIiKyGhYcRERERERkNSw4iIiIiIjIalhwEBERERGR1QyIRePh4eFQq9XYsGGDrVMhIiIiGni4aJwsMOC/OUajESkpKQgMDIS7uztUKhU0Gg3q6+u7jDcYDFCr1RAEATU1NT0eRxRFrFu3DldccQXkcjm8vb3x97//vZ9mQURERETkmAbEFY7utLS0QKvVIi0tDVOnTsXJkyeRlJSEefPmYf/+/Z3iV65cCZVKhYMHD/ZqnKSkJJSWlmLdunUIDAzEL7/8gl9++aW/pkFERERE5JAGTMHR3t6OxMREFBQUwNnZGcuWLUN6ejqUSiXKysrMYjdv3ozg4GDU1dVh3LhxUntJSQlKS0uxY8cOlJSU9Hjsr776Crm5ufj8888REBAAAJgwYUL/TIyIiIiIyIENmFuq8vPz4eTkhOrqamzcuBHr169HXl5el7F6vR6CIMDT01Nqa2xsRFxcHAoKCuDm5tarsf/9739j4sSJKC4uxoQJEzB+/Hjcdddd3V7hMBgMaGpqMtuIiIiIiAaaAXOFw8fHB9nZ2RAEAQEBATh06BCys7MRFxdnFtfW1oaUlBTExMTAw8MDwK/rL2JjY5GQkICgoCAcPXq0V2N/99130Ol0eP311/HSSy/BZDLhvvvuQ1RUFMrLy7s8JjMzE48//nif5kpERER0SXHROFlgwHxzQkNDIQiCtB8WFoba2lqYTCapzWg0Ijo6GqIoIjc3V2rPyclBc3MzUlNT+zR2R0cHDAYDXnrpJdx4440IDw/Hli1bUFFRgcOHD3d5TGpqKvR6vbQdO3asT2MTEREREdmzAVNwXMy5YkOn06GsrEy6ugEA5eXlqKqqglwuh5OTEyZNmgQACAoKwuLFiy/a95gxY+Dk5IQrrrhCarvyyisBAHV1dV0eI5fL4eHhYbYREREREQ00A+aWqn379pnt7927F/7+/pDJZFKxUVtbi4qKCgwbNswsdtOmTVizZo20X19fj4iICBQWFiIkJOSiY19//fVob2/Ht99+Cz8/PwDAN998AwDw9fW1dGpERERERA5rwBQcdXV1SE5OxtKlS6HVapGTk4OsrCwYjUZERUVBq9WiuLgYJpMJDQ0NAAAvLy+4uLiYPakKABQKBQDAz88PY8eOvejYM2fOxHXXXYc77rgDGzZsQEdHB+655x784Q9/MLvqQUREREQ02AyYgkOj0aC1tRXBwcGQyWRISkpCfHw8dDoddu3aBQBQq9Vmx1RUVCA8PNzisYcMGYJ///vfWL58Of7v//4P7u7uiIyMRFZWlsV9ExEREdkcF42TBQZEwVFZWSn9+fzF4AAwfvx4iKLYq/76coxKpcKOHTt6dQwRERER0UDHUpWIiIiIiKyGBUcPREZGQqFQdLllZGTYOj0iIiIiIrs1IG6psra8vDy0trZ2+ZmXl9clzoaIiIiIyHGw4OgBb29vW6dAREREZDtcNE4W4DeHiIiIiIishgUHERERERFZDW+psjMvPXgIHv998SANTobJU22dAtkB0cnZ1imQHXB1tXUGZGvt7bbOgMhyvMJBRERERERWwyscRERERNQ9LhonC/CbQ0REREREVsOCg4iIiIiIrIYFBxERERERWQ0LDiIiIiIishouGiciIiKi7nHROFlgQHxzwsPDsWLFClunQUREREREvzEgCo7uGI1GpKSkIDAwEO7u7lCpVNBoNKivr+8y3mAwQK1WQxAE1NTU9Hic1157DWq1Gm5ubvD19cVTTz3VTzMgIiIiInJcA77gaGlpgVarRVpaGrRaLYqKinD48GHMmzevy/iVK1dCpVL1aoySkhIsWrQICQkJ+Pzzz/HMM88gOzsbmzdv7o8pEBERERE5rAFTcLS3tyMxMRFKpRLDhw9HWloaRFGEUqlEWVkZoqOjERAQgNDQUGzevBkHDhxAXV2dWR8lJSUoLS3FunXrejV2QUEBFixYgISEBEycOBGzZ89Gamoq1q5dC1EU+3OaREREREQOZcAsGs/Pz8edd96J6upq7N+/H/Hx8Rg3bhzi4uI6xer1egiCAE9PT6mtsbERcXFx2LlzJ9zc3Ho1tsFg6HTM0KFD8cMPP0Cn02H8+PF9mRIRERGRfeCicbLAgPnm+Pj4IDs7GwEBAVi0aBGWL1+O7OzsTnFtbW1ISUlBTEwMPDw8AACiKCI2NhYJCQkICgrq9dgREREoKirCnj170NHRgW+++QZZWVkAgOPHj3d5jMFgQFNTk9lGRERERDTQDJiCIzQ0FIIgSPthYWGora2FyWSS2oxGI6KjoyGKInJzc6X2nJwcNDc3IzU1tU9jx8XFITExEXPmzIGLiwtCQ0Nx2223AQCGXOBfAzIzM6FUKqXNx8enT2MTEREREdmzAVNwXMy5YkOn06GsrEy6ugEA5eXlqKqqglwuh5OTEyZNmgQACAoKwuLFiy/atyAIWLt2LU6fPg2dToeGhgYEBwcDACZOnNjlMampqdDr9dJ27NixfpglEREREZF9GTBrOPbt22e2v3fvXvj7+0Mmk0nFRm1tLSoqKjBs2DCz2E2bNmHNmjXSfn19PSIiIlBYWIiQkJAe5yCTyeDt7Q0A+Oc//4mwsDCMGDGiy1i5XA65XN7jvomIiIiIHNGAKTjq6uqQnJyMpUuXQqvVIicnB1lZWTAajYiKioJWq0VxcTFMJhMaGhoAAF5eXnBxccG4cePM+lIoFAAAPz8/jB079qJj//zzz3jjjTcQHh6OtrY2vPjii3j99dfx3nvv9f9EiYiIiC41QRici8b5tNF+MWAKDo1Gg9bWVgQHB0MmkyEpKQnx8fHQ6XTYtWsXAECtVpsdU1FRgfDw8H4ZPz8/Hw888ABEUURYWBgqKyul26qIiIiIiAarAVFwVFZWSn8+fzE4AIwfP77X78Lo7THDhw9HVVVVr8YgIiIiIhoMBuG1MSIiIiIiulRYcPRAZGQkFApFl1tGRoat0yMiIiIislsD4pYqa8vLy0Nra2uXn3l5eV3ibIiIiIguscH6pnEuGu8XLDh64NyjbomIiIiIqHcGYalKRERERESXCgsOIiIiIiKyGt5SZW/+/W+AbyAf1OT/97OtUyB70E/vCCLHFqjnI9cHu6YzZ2ydApHFWHAQERERUfe4aJwsMAi/OUREREREdKmw4CAiIiIiIqthwUFERERERFbDgoOIiIiIiKyGi8aJiIiIqHtcNE4WGITfHCIiIiIiulTsvuAIDw/HihUrbJ0GERERERH1gd0XHN0xGo1ISUlBYGAg3N3doVKpoNFoUF9f32W8wWCAWq2GIAioqanp0RhtbW2IjY1FYGAgnJycsGDBgk4xRUVF+MMf/oARI0bAw8MDYWFheOeddyyYGRERERHRwODQBUdLSwu0Wi3S0tKg1WpRVFSEw4cPY968eV3Gr1y5EiqVqldjmEwmDB06FPfeey9mzpzZZcz777+PP/zhD3j77bdx4MAB3HTTTZg7dy4+/fTTXs+JiIiIiGggcYhF4+3t7UhMTERBQQGcnZ2xbNkypKenQ6lUoqyszCx28+bNCA4ORl1dHcaNGye1l5SUoLS0FDt27EBJSUmPx3Z3d0dubi4A4KOPPsKpU6c6xWzYsMFsPyMjA2+++Sb+/e9/49prr+35RImIiIjsEReNkwUc4puTn58PJycnVFdXY+PGjVi/fj3y8vK6jNXr9RAEAZ6enlJbY2Mj4uLiUFBQADc3N6vn29HRgebmZnh5eVl9LCIiIiIie+YQVzh8fHyQnZ0NQRAQEBCAQ4cOITs7G3FxcWZxbW1tSElJQUxMDDw8PAAAoigiNjYWCQkJCAoKwtGjR62e77p163D69GlER0dfMMZgMMBgMEj7TU1NVs+LiIiIiOhSc4grHKGhoRAEQdoPCwtDbW0tTCaT1GY0GhEdHQ1RFKVboAAgJycHzc3NSE1NvSS5vvLKK3j88cfx2muvYeTIkReMy8zMhFKplDYfH59Lkh8RERER0aXkEAXHxZwrNnQ6HcrKyqSrGwBQXl6OqqoqyOVyODk5YdKkSQCAoKAgLF68uF/zePXVV3HXXXfhtddeu+AC83NSU1Oh1+ul7dixY/2aCxERERGRPXCIW6r27dtntr937174+/tDJpNJxUZtbS0qKiowbNgws9hNmzZhzZo10n59fT0iIiJQWFiIkJCQfsvxn//8J+644w68+uqrmD179kXj5XI55HJ5v41PREREZDVcNE4WcIiCo66uDsnJyVi6dCm0Wi1ycnKQlZUFo9GIqKgoaLVaFBcXw2QyoaGhAQDg5eUFFxcXsydVAYBCoQAA+Pn5YezYsT0a/8svv8TZs2fxyy+/oLm5WXqHh1qtBvDrbVSLFy/Gxo0bERISIuUwdOhQKJXKfjgDRERERESOySEKDo1Gg9bWVgQHB0MmkyEpKQnx8fHQ6XTYtWsXgP/9+D+noqIC4eHh/TL+rFmzoNPppP1zj7oV/1v1Pv/882hvb8c999yDe+65R4pbvHgxtm3b1i85EBERERE5IrsvOCorK6U/n78YHADGjx8v/ejvqb4cc7EnW52fIxERERER/c8gvBmPiIiIiIgulUFfcERGRkKhUHS5ZWRk2Do9IiIiIts7t2h8MG5kMbu/pcra8vLy0Nra2uVnfFM4EREREZFlBn3B4e3tbesUiIiIiIgGLF4nIiIiIiIiq2HBQUREREREVjPob6myO01NAN9APrjt3GnrDMgefPedrTMgezB0qK0zIFu7wDrTS26wLqDmm8b7xSD85hARERER0aXCgoOIiIiIiKyGBQcREREREVkNCw4iIiIiIrIaLhonIiIiou5x0ThZYBB+c4iIiIiI6FJhwUFERERERFZj9wVHeHg4VqxYYes0iIiIiIioD+y+4OiO0WhESkoKAgMD4e7uDpVKBY1Gg/r6+i7jDQYD1Go1BEFATU1Nj8Zoa2tDbGwsAgMD4eTkhAULFnQb/9FHH8HJyQlqtbp3kyEiIiIiGoAcuuBoaWmBVqtFWloatFotioqKcPjwYcybN6/L+JUrV0KlUvVqDJPJhKFDh+Lee+/FzJkzu409deoUNBoNfv/73/dqDCIiIiK7Jgj/Wzg+mDZBsPWZHxAcouBob29HYmIilEolhg8fjrS0NIiiCKVSibKyMkRHRyMgIAChoaHYvHkzDhw4gLq6OrM+SkpKUFpainXr1vVqbHd3d+Tm5iIuLg6jR4/uNjYhIQG33347wsLCej1HIiIiIqKByCEKjvz8fDg5OaG6uhobN27E+vXrkZeX12WsXq+HIAjw9PSU2hobGxEXF4eCggK4ublZJccXX3wR3333HR577DGr9E9ERERE5Igc4j0cPj4+yM7OhiAICAgIwKFDh5CdnY24uDizuLa2NqSkpCAmJgYeHh4AAFEUERsbi4SEBAQFBeHo0aP9nl9tbS0eeughfPDBB3By6tkpNRgMMBgM0n5TU1O/50VEREREZGsOcYUjNDQUwnn30IWFhaG2thYmk0lqMxqNiI6OhiiKyM3NldpzcnLQ3NyM1NRUq+RmMplw++234/HHH8cVV1zR4+MyMzOhVCqlzcfHxyr5ERERERHZkkNc4biYc8WGTqdDeXm5dHUDAMrLy1FVVQW5XG52TFBQEBYtWoT8/HyLxm5ubsb+/fvx6aefIjExEQDQ0dEBURTh5OSE0tJS3HzzzZ2OS01NRXJysrTf1NTEooOIiIjs02B90/hgnLMVOETBsW/fPrP9vXv3wt/fHzKZTCo2amtrUVFRgWHDhpnFbtq0CWvWrJH26+vrERERgcLCQoSEhFicm4eHBw4dOmTW9swzz6C8vBxvvPEGJkyY0OVxcrm8UxFERERERDTQOETBUVdXh+TkZCxduhRarRY5OTnIysqC0WhEVFQUtFotiouLYTKZ0NDQAADw8vKCi4sLxo0bZ9aXQqEAAPj5+WHs2LE9Gv/LL7/E2bNn8csvv6C5uVl6h4darcaQIUNwzTXXmMWPHDkSrq6undqJiIiIiAYbhyg4NBoNWltbERwcDJlMhqSkJMTHx0On02HXrl0A0OlFexUVFQgPD++X8WfNmgWdTiftX3vttQB+XZBOREREREQXJoj81WwXmpqaoFQqoV+2DB681Wpwa2+3dQZkD6ZMsXUGZA+GDrV1BmRjTa2tUMbHQ6/Xm61RvWTjn/t9EhwMjx4+iXMgaWpvh7K62mbnf6AYfN8cIiIiIuodLhonCwz6sxgZGQmFQtHllpGRYev0iIiIiIgc2qC/wpGXl4fW1tYuP/Py8rrE2RARERERDSyDvuDw9va2dQpERERERAPWoL+lioiIiIiIrIcFBxERERF179yi8cG49cHTTz+N8ePHw9XVFSEhIaiurr5gbHh4OARB6LTNnj1biomNje30+S233NKn3Gxh0N9SZXfq6wFnZ1tnQbb035dT0uB2KDTO1imQHQjc+4KtUyBbu8A6U7JfhYWFSE5OxrPPPouQkBBs2LABEREROHz4MEaOHNkpvqioCGfPnpX2T5w4galTp+Ivf/mLWdwtt9yCF198UdqXO9BrFHiFg4iIiIion6xfvx5xcXFYsmQJrrrqKjz77LNwc3PD1q1bu4z38vLC6NGjpa2srAxubm6dCg65XG4Wd/nll1+K6fQLFhxERERERP3g7NmzOHDgAGbOnCm1DRkyBDNnzkRVVVWP+tiyZQtuu+02uLu7m7VXVlZi5MiRCAgIwLJly3DixIl+zd2aeEsVEREREVE3mpqazPblcnmXtzT9/PPPMJlMGDVqlFn7qFGj8PXXX190nOrqanz++efYsmWLWfstt9yCP//5z5gwYQK+/fZbPPzww4iMjERVVRVkMlkfZnRpseAgIiIiou4N8jeN+/j4mDU/9thjWL16db8Pt2XLFgQGBiI4ONis/bbbbpP+HBgYiClTpsDPzw+VlZX4/e9/3+959DcWHERERERE3Th27Bg8PDyk/Qst2B4+fDhkMhkaGxvN2hsbGzF69Ohuxzhz5gxeffVVpKenXzSfiRMnYvjw4Thy5IhDFByDsFQlIiIiIuo5Dw8Ps+1CBYeLiwumTZuGPXv2SG0dHR3Ys2cPwsLCuh3j9ddfh8FgwF//+teL5vPDDz/gxIkTGDNmTO8mYiMsOIiIiIiI+klycjJeeOEF5Ofn46uvvsKyZctw5swZLFmyBACg0WiQmpra6bgtW7ZgwYIFGDZsmFn76dOn8eCDD2Lv3r04evQo9uzZg/nz52PSpEmIiIi4JHOylN3fUhUeHg61Wo0NGzbYOhUiIiIiom4tXLgQP/30Ex599FE0NDRArVZj9+7d0kLyuro6DPnNepjDhw/jww8/RGlpaaf+ZDIZPvvsM+Tn5+PUqVNQqVT44x//iCeeeMJh3sXh0Fc4jEYjUlJSEBgYCHd3d6hUKmg0GtTX13cZbzAYoFarIQgCampqejRGW1sbYmNjERgYCCcnJyxYsKBTTFdvfxQEAVdffbUFsyMiIiKyE7Z+27eDvWk8MTEROp0OBoMB+/btQ0hIiPRZZWUltm3bZhYfEBAAURTxhz/8oVNfQ4cOxTvvvIP//Oc/OHv2LI4ePYrnn3++05Ow7JlDFxwtLS3QarVIS0uDVqtFUVERDh8+jHnz5nUZv3LlSqhUql6NYTKZMHToUNx7771mz1Q+38aNG3H8+HFpO3bsGLy8vDq9sIWIiIiIaLCx+1uqAKC9vR2JiYkoKCiAs7Mzli1bhvT0dCiVSpSVlZnFbt68GcHBwairq8O4ceOk9pKSEpSWlmLHjh0oKSnp8dju7u7Izc0FAHz00Uc4depUpxilUgmlUint79y5EydPnpTu1SMiIiIiGqwc4gpHfn4+nJycUF1djY0bN2L9+vXIy8vrMlav10MQBHh6ekptjY2NiIuLQ0FBAdzc3Kye75YtWzBz5kz4+vpafSwiIiIiInvmEFc4fHx8kJ2dDUEQEBAQgEOHDiE7OxtxcXFmcW1tbUhJSUFMTIz0rGRRFBEbG4uEhAQEBQXh6NGjVs21vr4eJSUleOWVV7qNMxgMMBgM0v5v32BJRERERDQQOMQVjtDQUAiCIO2HhYWhtrYWJpNJajMajYiOjoYoitItUACQk5OD5ubmLh8/Zg35+fnw9PTscnH5+TIzM6VbsZRKZac3WBIRERHZDVsv3HawReNkbkCcxXPFhk6nQ1lZmdmbIMvLy1FVVQW5XA4nJydMmjQJABAUFITFixf3ax6iKGLr1q3429/+BhcXl25jU1NTodfrpe3YsWP9mgsRERERkT1wiFuq9u3bZ7a/d+9e+Pv7QyaTScVGbW0tKioqOr0sZdOmTVizZo20X19fj4iICBQWFpo9oqw/vPfeezhy5AjuvPPOi8bK5XKHeXYyEREREVFfOUTBUVdXh+TkZCxduhRarRY5OTnIysqC0WhEVFQUtFotiouLYTKZ0NDQAADw8vKCi4uL2ZOqAEChUAAA/Pz8MHbs2B6N/+WXX+Ls2bP45Zdf0NzcLL3DQ61Wm8Vt2bIFISEhuOaaayybMBERERHRAOEQBYdGo0FrayuCg4Mhk8mQlJSE+Ph46HQ67Nq1C0DnH/8VFRUIDw/vl/FnzZoFnU4n7V977bUAfr2F6hy9Xo8dO3Zg48aN/TImEREREdFAYPcFR2VlpfTn8xeDA8D48ePNfvT3RF+O6cmTrZRKJVpaWnrVLxEREZFDGKwLqAfjnK2AZ5GIiIiIiKxm0BcckZGRUCgUXW4ZGRm2To+IiIiIyKHZ/S1V1paXl4fW1tYuP/Py8rrE2RARERERDSyDvuDw9va2dQpERERERAPWoC84iIiIiOgiuGicLMCzSEREREREVsMrHHYm6fKX4OLiYes0yIZkMltnQPYgrMbWGZA96AiOs3UKZGOnTzcBSLJ1GkQW4RUOIiIiIiKyGhYcRERERERkNbylioiIiIi6JwiDcwG1INg6gwFhEH5ziIiIiIjoUmHBQUREREREVsOCg4iIiIiIrIYFBxERERERWY3dLxoPDw+HWq3Ghg0bbJ0KERER0eDEN42TBRz6LBqNRqSkpCAwMBDu7u5QqVTQaDSor6/vMt5gMECtVkMQBNTU1PRojLa2NsTGxiIwMBBOTk5YsGBBl3Hbt2/H1KlT4ebmhjFjxuCOO+7AiRMn+jgzIiIiIqKBwaELjpaWFmi1WqSlpUGr1aKoqAiHDx/GvHnzuoxfuXIlVCpVr8YwmUwYOnQo7r33XsycObPLmI8++ggajQZ33nknvvjiC7z++uuorq5GXBzfEEtEREREg5tDFBzt7e1ITEyEUqnE8OHDkZaWBlEUoVQqUVZWhujoaAQEBCA0NBSbN2/GgQMHUFdXZ9ZHSUkJSktLsW7dul6N7e7ujtzcXMTFxWH06NFdxlRVVWH8+PG49957MWHCBNxwww1YunQpqqur+zxnIiIiIqKBwCEKjvz8fDg5OaG6uhobN27E+vXrkZeX12WsXq+HIAjw9PSU2hobGxEXF4eCggK4ubn1e35hYWE4duwY3n77bYiiiMbGRrzxxhuYNWtWv49FRERERORI7H7ROAD4+PggOzsbgiAgICAAhw4dQnZ2dqdbltra2pCSkoKYmBh4eHgAAERRRGxsLBISEhAUFISjR4/2e37XX389tm/fjoULF6KtrQ3t7e2YO3cunn766QseYzAYYDAYpP2mpqZ+z4uIiIioX3DROFnAIc5iaGgohPNeLR8WFoba2lqYTCapzWg0Ijo6GqIoIjc3V2rPyclBc3MzUlNTrZbfl19+iaSkJDz66KM4cOAAdu/ejaNHjyIhIeGCx2RmZkKpVEqbj4+P1fIjIiIiIrIVhyg4LuZcsaHT6VBWViZd3QCA8vJyVFVVQS6Xw8nJCZMmTQIABAUFYfHixf0yfmZmJq6//no8+OCDmDJlCiIiIvDMM89g69atOH78eJfHpKamQq/XS9uxY8f6JRciIiIiInviELdU7du3z2x/79698Pf3h0wmk4qN2tpaVFRUYNiwYWaxmzZtwpo1a6T9+vp6REREoLCwECEhIf2SX0tLC5yczE+lTCYD8OstXV2Ry+WQy+X9Mj4RERERkb1yiIKjrq4OycnJWLp0KbRaLXJycpCVlQWj0YioqChotVoUFxfDZDKhoaEBAODl5QUXFxeMGzfOrC+FQgEA8PPzw9ixY3s0/pdffomzZ8/il19+QXNzs/QOD7VaDQCYO3cu4uLikJubi4iICBw/fhwrVqxAcHBwrx/DS0REREQ0kDhEwaHRaNDa2org4GDIZDIkJSUhPj4eOp0Ou3btAvC/H//nVFRUIDw8vF/GnzVrFnQ6nbR/7bXXAvjf1YvY2Fg0Nzdj8+bNuP/+++Hp6Ymbb74Za9eu7ZfxiYiIiGyKi8bJAoJ4oXt+6JJqamqCUqlEbKweLi4eFz+ABqz/3o1Hg1xYmK0zIHswZYqtMyBbO326CTfcoIRerzdbo3qpnPt9op89Gx7Ozpd8fFtrMhqhfOstm53/gYJlGxERERERWc2gLzgiIyOhUCi63DIyMmydHhERERGRQ3OINRzWlJeXh9bW1i4/8/LyusTZEBERERENLIO+4PD29rZ1CkRERET2jYvGyQI8i0REREREZDUsOIiIiIiIyGoG/S1V9qalBWhvt3UWZEsmk60zIHvwm3eW0iCl1do6A7K1CywzJXIovMJBRERERERWwyscRERERNQ9LhonC/AsEhERERGR1bDgICIiIiIiq2HBQUREREREVsOCg4iIiIiIrIaLxomIiIioe1w0Thaw+7MYHh6OFStW2DoNIiIiIiLqA7svOLpjNBqRkpKCwMBAuLu7Q6VSQaPRoL6+vst4g8EAtVoNQRBQU1PTozEqKysxf/58jBkzBu7u7lCr1di+fXunuNdffx2TJ0+Gq6srAgMD8fbbb1syNSIiIiKiAcGhC46WlhZotVqkpaVBq9WiqKgIhw8fxrx587qMX7lyJVQqVa/G+PjjjzFlyhTs2LEDn332GZYsWQKNRoPi4mKzmJiYGNx555349NNPsWDBAixYsACff/65RfMjIiIiInJ0giiKoq2T6E54eDiuueYaAEBBQQGcnZ2xbNkypKenQxCETvGffPIJgoODodPpMG7cOKm9pKQEycnJ2LFjB66++mp8+umnUKvVfcpp9uzZGDVqFLZu3QoAWLhwIc6cOWNWhISGhkKtVuPZZ5/tUZ9NTU1QKpWIjtbDxcWjT3nRwGAy2ToDsgdLl9o6A7IH331n6wzI1lpbm3DPPUro9Xp4eFz63wfnfp/o//QneDg7X/Lxba3JaITyX/+y2fkfKBziCkd+fj6cnJxQXV2NjRs3Yv369cjLy+syVq/XQxAEeHp6Sm2NjY2Ii4tDQUEB3NzcLM5Hr9fDy8tL2q+qqsLMmTPNYiIiIlBVVWXxWEREREQ2d27R+GDcyGIO8ZQqHx8fZGdnQxAEBAQE4NChQ8jOzkZcXJxZXFtbG1JSUhATEyNVoaIoIjY2FgkJCQgKCsLRo0ctyuW1117DJ598gueee05qa2howKhRo8ziRo0ahYaGhgv2YzAYYDAYpP2mpiaL8iIiIiIiskcOUbaFhoaa3T4VFhaG2tpamM6798RoNCI6OhqiKCI3N1dqz8nJQXNzM1JTUy3Oo6KiAkuWLMELL7yAq6++2qK+MjMzoVQqpc3Hx8fi/IiIiIiI7I1DFBwXc67Y0Ol0KCsrM7vHrry8HFVVVZDL5XBycsKkSZMAAEFBQVi8eHGPx3jvvfcwd+5cZGdnQ6PRmH02evRoNDY2mrU1NjZi9OjRF+wvNTUVer1e2o4dO9bjXIiIiIiIHIVD3FK1b98+s/29e/fC398fMplMKjZqa2tRUVGBYcOGmcVu2rQJa9askfbr6+sRERGBwsJChISE9Gj8yspKzJkzB2vXrkV8fHynz8PCwrBnzx6z94WUlZUhLCzsgn3K5XLI5fIejU9ERERE5KgcouCoq6tDcnIyli5dCq1Wi5ycHGRlZcFoNCIqKgparRbFxcUwmUzSugkvLy+4uLiYPakKABQKBQDAz88PY8eOvejYFRUVmDNnDpKSknDrrbdK/bu4uEgLx5OSkjBjxgxkZWVh9uzZePXVV7F//348//zz/XkaiIiIiGxjsC6gHoxztgKHOIsajQatra0IDg7GPffcg6SkJMTHx+PHH3/Erl278MMPP0CtVmPMmDHS9vHHH/fL2Pn5+WhpaUFmZqZZ/3/+85+lmOnTp+OVV17B888/j6lTp+KNN97Azp07pcf5EhERERENVnb/Ho7Bgu/hoHP4Hg4C+B4O+hXfw0F28x6Ov/xl8L6H4/XX+R4OCznEFQ4iIiIiInJMg77giIyMhEKh6HLLyMiwdXpERERERA7NIRaNW1NeXh5aW1u7/Oz8t4kTERERDVqCMDgXUJ/3Hjjqu0FfcHh7e9s6BSIiIiKiAWsQlqpERERERHSpsOAgIiIiIiKrYcFBRERERERWM+jXcNibF67Khoerq63TIFtSq22dAdmD66bbOgOyAzPcvrZ1CmRjTadP4x5bJwHwTeNkEZ5FIiIiIiKyGhYcRERERERkNSw4iIiIiIjIalhwEBERERGR1XDROBERERF1j4vGyQI8i0REREREZDV2X3CEh4djxYoVtk6DiIiIiIj6wO4Lju4YjUakpKQgMDAQ7u7uUKlU0Gg0qK+v7zLeYDBArVZDEATU1NT0aIzKykrMnz8fY8aMgbu7O9RqNbZv324Ws23bNgiCYLa58l0aRERERESOXXC0tLRAq9UiLS0NWq0WRUVFOHz4MObNm9dl/MqVK6FSqXo1xscff4wpU6Zgx44d+Oyzz7BkyRJoNBoUFxebxXl4eOD48ePSptPp+jwvIiIiIqKBwiEWjbe3tyMxMREFBQVwdnbGsmXLkJ6eDqVSibKyMrPYzZs3Izg4GHV1dRg3bpzUXlJSgtLSUuzYsQMlJSU9Hvvhhx82209KSkJpaSmKioowZ84cqV0QBIwePbqPMyQiIiKyY1w0ThZwiLOYn58PJycnVFdXY+PGjVi/fj3y8vK6jNXr9RAEAZ6enlJbY2Mj4uLiUFBQADc3N4vz0ev18PLyMms7ffo0fH194ePjg/nz5+OLL76weBwiIiIiIkfnEFc4fHx8kJ2dDUEQEBAQgEOHDiE7OxtxcXFmcW1tbUhJSUFMTAw8PDwAAKIoIjY2FgkJCQgKCsLRo0ctyuW1117DJ598gueee05qCwgIwNatWzFlyhTo9XqsW7cO06dPxxdffIGxY8d22Y/BYIDBYJD2m5qaLMqLiIiIiMgeOcQVjtDQUAiCIO2HhYWhtrYWJpNJajMajYiOjoYoisjNzZXac3Jy0NzcjNTUVIvzqKiowJIlS/DCCy/g6quvNstHo9FArVZjxowZKCoqwogRI8yKkt/KzMyEUqmUNh8fH4vzIyIiIiKyNw5RcFzMuWJDp9OhrKxMuroBAOXl5aiqqoJcLoeTkxMmTZoEAAgKCsLixYt7PMZ7772HuXPnIjs7GxqNpttYZ2dnXHvttThy5MgFY1JTU6HX66Xt2LFjPc6FiIiIiMhROMQtVfv27TPb37t3L/z9/SGTyaRio7a2FhUVFRg2bJhZ7KZNm7BmzRppv76+HhERESgsLERISEiPxq+srMScOXOwdu1axMfHXzTeZDLh0KFDmDVr1gVj5HI55HJ5j8YnIiIisikuGicLOETBUVdXh+TkZCxduhRarRY5OTnIysqC0WhEVFQUtFotiouLYTKZ0NDQAADw8vKCi4uL2ZOqAEChUAAA/Pz8Lri+4nwVFRWYM2cOkpKScOutt0r9u7i4SAvH09PTERoaikmTJuHUqVN46qmnoNPpcNddd/XnaSAiIiIicjgOUXBoNBq0trYiODgYMpkMSUlJiI+Ph06nw65duwAAarXa7JiKigqEh4dbPHZ+fj5aWlqQmZmJzMxMqX3GjBmorKwEAJw8eRJxcXFoaGjA5ZdfjmnTpuHjjz/GVVddZfH4RERERESOTBBFUbR1EvTrU6qUSiX0q1fDg28pH9x+UzzTIDV9uq0zIHvw9de2zoBsrOn0aShvvhl6vd5sjeolG//c7xONBh4uLpd8fFtrOnsWypdestn5Hyh4YxoREREREVnNoC84IiMjoVAoutwyMjJsnR4RERGR7Z1bND4YN7KYQ6zhsKa8vDy0trZ2+dlv3yZORERERES9M+gLDm9vb1unQEREREQ0YPE6ERERERERWQ0LDiIiIiIisppBf0sVEREREV3EYF1APRjnbAUsOOzN5ZcDQ4faOguyJZXK1hkQkb04fdrWGZCtnTlj6wyILMayjYiIiIiIrIYFBxERERERWQ0LDiIiIiIishqu4SAiIiKi7nHROFmAZ5GIiIiIiKyGBQcREREREVmN3Rcc4eHhWLFiha3TICIiIiKiPrD7gqM7RqMRKSkpCAwMhLu7O1QqFTQaDerr67uMNxgMUKvVEAQBNTU1PRqjsrIS8+fPx5gxY+Du7g61Wo3t27ebxYSHh0MQhE7b7NmzLZ0iEREREZFDc+hF4y0tLdBqtUhLS8PUqVNx8uRJJCUlYd68edi/f3+n+JUrV0KlUuHgwYM9HuPjjz/GlClTkJKSglGjRqG4uBgajQZKpRJz5swBABQVFeHs2bPSMSdOnMDUqVPxl7/8xfJJEhEREdkaF42TBRyi4Ghvb0diYiIKCgrg7OyMZcuWIT09HUqlEmVlZWaxmzdvRnBwMOrq6jBu3DipvaSkBKWlpdixYwdKSkp6PPbDDz9stp+UlITS0lIUFRVJBYeXl5dZzKuvvgo3NzcWHEREREQ06DlE2Zafnw8nJydUV1dj48aNWL9+PfLy8rqM1ev1EAQBnp6eUltjYyPi4uJQUFAANzc3i/PR6/WdiozzbdmyBbfddhvc3d0tHouIiIiIyJE5xBUOHx8fZGdnQxAEBAQE4NChQ8jOzkZcXJxZXFtbG1JSUhATEwMPDw8AgCiKiI2NRUJCAoKCgnD06FGLcnnttdfwySef4Lnnnuvy8+rqanz++efYsmVLt/0YDAYYDAZpv6mpyaK8iIiIiIjskUNc4QgNDYUgCNJ+WFgYamtrYTKZpDaj0Yjo6GiIoojc3FypPScnB83NzUhNTbU4j4qKCixZsgQvvPACrr766i5jtmzZgsDAQAQHB3fbV2ZmJpRKpbT5+PhYnB8RERERkb1xiILjYs4VGzqdDmVlZdLVDQAoLy9HVVUV5HI5nJycMGnSJABAUFAQFi9e3OMx3nvvPcydOxfZ2dnQaDRdxpw5cwavvvoq7rzzzov2l5qaCr1eL23Hjh3rcS5EREREl5Qg/G/h+GDazvsHb+o7h7ilat++fWb7e/fuhb+/P2QymVRs1NbWoqKiAsOGDTOL3bRpE9asWSPt19fXIyIiAoWFhQgJCenR+JWVlZgzZw7Wrl2L+Pj4C8a9/vrrMBgM+Otf/3rRPuVyOeRyeY/GJyIiIiJyVA5RcNTV1SE5ORlLly6FVqtFTk4OsrKyYDQaERUVBa1Wi+LiYphMJjQ0NAD49clRLi4uZk+qAgCFQgEA8PPzw9ixYy86dkVFBebMmYOkpCTceuutUv8uLi6dFo5v2bIFCxYs6FT0EBERERENVg5RcGg0GrS2tiI4OBgymQxJSUmIj4+HTqfDrl27AABqtdrsmIqKCoSHh1s8dn5+PlpaWpCZmYnMzEypfcaMGaisrJT2Dx8+jA8//BClpaUWj0lERERENFDYfcFx/o/68xeDA8D48eMhimKv+uvtMdu2bcO2bdsuGhcQENDrXIiIiIiIBjq7LziIiIiIyMb4pnGywKA/i5GRkVAoFF1uGRkZtk6PiIiIiMihDforHHl5eWhtbe3ys+7eJk5ERERERBc36AsOb29vW6dARERERDRgDfpbqoiIiIiIyHoG/RUOIiIiIroILhonC/AsEhERERGR1fAKh7355z8BJ/61DGpvv23rDMgOfLvuX7ZOgeyAH5+WSO3tts6AyGK8wkFERERERFbDgoOIiIiIiKyG9+4QERERUfe4aJwswLNIRERERERWw4KDiIiIiIishgUHERERERFZDQsOIiIiIiKyGrtfNB4eHg61Wo0NGzbYOhUiIiKiwYmLxskCDn0WjUYjUlJSEBgYCHd3d6hUKmg0GtTX13cZbzAYoFarIQgCampqejRGZWUl5s+fjzFjxsDd3R1qtRrbt2/vlEd6ejr8/Pzg6uqKqVOnYvfu3ZZOj4iIiIjI4Tl0wdHS0gKtVou0tDRotVoUFRXh8OHDmDdvXpfxK1euhEql6tUYH3/8MaZMmYIdO3bgs88+w5IlS6DRaFBcXCzFrFq1Cs899xxycnLw5ZdfIiEhAX/605/w6aefWjQ/IiIiIiJHZ/e3VAFAe3s7EhMTUVBQAGdnZyxbtgzp6elQKpUoKyszi928eTOCg4NRV1eHcePGSe0lJSUoLS3Fjh07UFJS0uOxH374YbP9pKQklJaWoqioCHPmzAEAFBQU4JFHHsGsWbMAAMuWLcO7776LrKwsvPzyy32dNhERERGRw3OIgiM/Px933nknqqursX//fsTHx2PcuHGIi4vrFKvX6yEIAjw9PaW2xsZGxMXFYefOnXBzc7M4H71ejyuvvFLaNxgMcHV1NYsZOnQoPvzwwwv2YTAYYDAYpP2mpiaL8yIiIiKyCq7hIAs4xFn08fFBdnY2AgICsGjRIixfvhzZ2dmd4tra2pCSkoKYmBh4eHgAAERRRGxsLBISEhAUFGRxLq+99ho++eQTLFmyRGqLiIjA+vXrUVtbi46ODpSVlaGoqAjHjx+/YD+ZmZlQKpXS5uPjY3FuRERERET2xiEKjtDQUAiCIO2HhYWhtrYWJpNJajMajYiOjoYoisjNzZXac3Jy0NzcjNTUVIvzqKiowJIlS/DCCy/g6quvlto3btwIf39/TJ48GS4uLkhMTMSSJUswpJuqODU1FXq9XtqOHTtmcX5ERERERPbGIQqOizlXbOh0OpSVlUlXNwCgvLwcVVVVkMvlcHJywqRJkwAAQUFBWLx4cY/HeO+99zB37lxkZ2dDo9GYfTZixAjs3LkTZ86cgU6nw9dffw2FQoGJEydesD+5XA4PDw+zjYiIiIhooHGINRz79u0z29+7dy/8/f0hk8mkYqO2thYVFRUYNmyYWeymTZuwZs0aab++vh4REREoLCxESEhIj8avrKzEnDlzsHbtWsTHx18wztXVFd7e3jAajdixYweio6N7MUsiIiIiooHHIQqOuro6JCcnY+nSpdBqtcjJyUFWVhaMRiOioqKg1WpRXFwMk8mEhoYGAICXlxdcXFzMnlQFAAqFAgDg5+eHsWPHXnTsiooKzJkzB0lJSbj11lul/l1cXODl5QXg14Loxx9/hFqtxo8//ojVq1ejo6MDK1eu7M/TQERERGQbXDROFnCIgkOj0aC1tRXBwcGQyWRISkpCfHw8dDoddu3aBQBQq9Vmx1RUVCA8PNzisfPz89HS0oLMzExkZmZK7TNmzEBlZSWAXxerr1q1Ct999x0UCgVmzZqFgoICsydlERERERENRnZfcJz7UQ/AbDE4AIwfPx6iKPaqv94es23bNmzbtq3bmBkzZuDLL7/sVR5ERERERIMBrxMREREREZHVDPqCIzIyEgqFosstIyPD1ukRERERETk0u7+lytry8vLQ2tra5WfnFoUTERERDWpcNE4WGPQFh7e3t61TICIiIiIasFi2ERERERGR1bDgICIiIiIiqxn0t1TZm5uN70DW4WHrNMiGqnfaOgOyB+KcLbZOgexA1i1ltk6BbKytrQmoVNo6DSKLsOAgIiIiou5x0ThZgGeRiIiIiKgfPf300xg/fjxcXV0REhKC6urqC8Zu27YNgiCYba6urmYxoiji0UcfxZgxYzB06FDMnDkTtbW11p5Gv2HBQURERETUTwoLC5GcnIzHHnsMWq0WU6dORUREBP7zn/9c8BgPDw8cP35c2nQ6ndnnTz75JDZt2oRnn30W+/btg7u7OyIiItDW1mbt6fQLFhxERERERP1k/fr1iIuLw5IlS3DVVVfh2WefhZubG7Zu3XrBYwRBwOjRo6Vt1KhR0meiKGLDhg1YtWoV5s+fjylTpuCll15CfX09du7ceQlmZDkWHERERERE3WhqajLbDAZDl3Fnz57FgQMHMHPmTKltyJAhmDlzJqqqqi7Y/+nTp+Hr6wsfHx/Mnz8fX3zxhfTZ999/j4aGBrM+lUolQkJCuu3TnrDgICIiIqLuCcL/Fo4Ppk0QAAA+Pj5QKpXSlpmZ2eVp+vnnn2EymcyuUADAqFGj0NDQ0OUxAQEB2Lp1K9588028/PLL6OjowPTp0/HDDz8AgHRcb/q0N3xKFRERERFRN44dOwYPj/+9tkAul/db32FhYQgLC5P2p0+fjiuvvBLPPfccnnjiiX4bx5bs/gpHeHg4VqxYYes0iIiIiGiQ8vDwMNsuVHAMHz4cMpkMjY2NZu2NjY0YPXp0j8ZydnbGtddeiyNHjgCAdJwlfdqa3Rcc3TEajUhJSUFgYCDc3d2hUqmg0WhQX1/fZbzBYIBarYYgCKipqenRGIcPH8ZNN92EUaNGwdXVFRMnTsSqVatgNBqlmKKiIgQFBcHT0xPu7u5Qq9UoKCjojykSERERkYNwcXHBtGnTsGfPHqmto6MDe/bsMbuK0R2TyYRDhw5hzJgxAIAJEyZg9OjRZn02NTVh3759Pe7T1hz6lqqWlhZotVqkpaVh6tSpOHnyJJKSkjBv3jzs37+/U/zKlSuhUqlw8ODBHo/h7OwMjUaD6667Dp6enjh48CDi4uLQ0dGBjIwMAICXlxceeeQRTJ48GS4uLiguLsaSJUswcuRIRERE9Nt8iYiIiMi+JScnY/HixQgKCkJwcDA2bNiAM2fOYMmSJQAAjUYDb29vaR1Ieno6QkNDMWnSJJw6dQpPPfUUdDod7rrrLgC/PsFqxYoVWLNmDfz9/TFhwgSkpaVBpVJhwYIFtppmrzhEwdHe3o7ExEQUFBTA2dkZy5YtQ3p6OpRKJcrKysxiN2/ejODgYNTV1WHcuHFSe0lJCUpLS7Fjxw6UlJT0eOyJEydi4sSJ0r6vry8qKyvxwQcfSG3h4eFmxyQlJSE/Px8ffvghCw4iIiJyfHzTeI8tXLgQP/30Ex599FE0NDRArVZj9+7d0qLvuro6DDmv35MnTyIuLg4NDQ24/PLLMW3aNHz88ce46qqrpJiVK1fizJkziI+Px6lTp3DDDTdg9+7dnV4QaK8couDIz8/HnXfeierqauzfvx/x8fEYN24c4uLiOsXq9XoIggBPT0+prbGxEXFxcdi5cyfc3NwsyuXIkSPYvXs3/vznP3f5uSiKKC8vx+HDh7F27doL9mMwGMweqdbU1GRRXkRERERkHxITE5GYmNjlZ5WVlWb72dnZyM7O7rY/QRCQnp6O9PT0/krxknKIUtXHxwfZ2dkICAjAokWLsHz58i7/Ytra2pCSkoKYmBjpSQKiKCI2NhYJCQkICgrqcw7Tp0+Hq6sr/P39ceONN3b6C9fr9VAoFHBxccHs2bORk5ODP/zhDxfsLzMz0+zxaj4+Pn3OjYiIiIjIXjlEwREaGgrhv89BBn59fFhtbS1MJpPUZjQaER0dDVEUkZubK7Xn5OSgubkZqampFuVQWFgIrVaLV155BW+99RbWrVtn9vlll12GmpoafPLJJ/j73/+O5OTkThXs+VJTU6HX66Xt2LFjFuVHRERERGSPHOKWqos5V2zodDqUl5ebPSe5vLwcVVVVnR5fFhQUhEWLFiE/P79HY5y7AnHVVVfBZDIhPj4e999/P2QyGYBf3yI5adIkAIBarcZXX32FzMzMTus7zpHL5f36DGciIiIiInvkEAXHvn37zPb37t0Lf39/yGQyqdiora1FRUUFhg0bZha7adMmrFmzRtqvr69HREQECgsLERIS0qd8Ojo6YDQa0dHRIRUcXcVc6LX3RERERA6Fi8bJAg5RcNTV1SE5ORlLly6FVqtFTk4OsrKyYDQaERUVBa1Wi+LiYphMJukV715eXnBxcTF7UhUAKBQKAICfnx/Gjh170bG3b98OZ2dnBAYGQi6XY//+/UhNTcXChQvh7OwM4Nf1GEFBQfDz84PBYMDbb7+NgoICs1u7iIiIiIgGI4coODQaDVpbWxEcHAyZTIakpCTEx8dDp9Nh165dAH69jel8FRUVF7ydqTecnJywdu1afPPNNxBFEb6+vkhMTMR9990nxZw5cwZ33303fvjhBwwdOhSTJ0/Gyy+/jIULF1o8PhERERGRIxNEURRtnQT9+lhcpVKJadP0kMk8Ln4ADVjV1bbOgOyBmLfF1imQHcg6daetUyAba2trwqpVSuj1erM1qpfKud8n+lWr4OEg73zoT01tbVCuWWOz8z9Q8MY0IiIiIiKymkFfcERGRkKhUHS5ZWRk2Do9IiIiIts7t2h8MG5kMYdYw2FNeXl5aG1t7fIzLy+vS5wNEREREdHAMugLDm9vb1unQEREREQ0YPE6ERERERERWQ0LDiIiIiIisppBf0uVvTlyBBAEW2dBtnTVVbbOgOyBbiYfh0pAa4GtMyBba2uzdQb/NVgXUA/GOVsBzyIREREREVkNCw4iIiIiIrIaFhxERERERGQ1LDiIiIiIiMhquGiciIiIiLrHReNkAZ5FIiIiIiKyGhYcRERERERkNXZfcISHh2PFihW2ToOIiIiIiPrA7guO7hiNRqSkpCAwMBDu7u5QqVTQaDSor6/vMt5gMECtVkMQBNTU1PRojMOHD+Omm27CqFGj4OrqiokTJ2LVqlUwGo1SzLZt2yAIgtnm6uraH1MkIiIiInJoDr1ovKWlBVqtFmlpaZg6dSpOnjyJpKQkzJs3D/v37+8Uv3LlSqhUKhw8eLDHYzg7O0Oj0eC6666Dp6cnDh48iLi4OHR0dCAjI0OK8/DwwOHDh6V9ga8LJyIiooGCi8bJAg5RcLS3tyMxMREFBQVwdnbGsmXLkJ6eDqVSibKyMrPYzZs3Izg4GHV1dRg3bpzUXlJSgtLSUuzYsQMlJSU9HnvixImYOHGitO/r64vKykp88MEHZnGCIGD06NF9nCERERER0cDkEGVbfn4+nJycUF1djY0bN2L9+vXIy8vrMlav10MQBHh6ekptjY2NiIuLQ0FBAdzc3CzK5ciRI9i9ezdmzJhh1n769Gn4+vrCx8cH8+fPxxdffNFtPwaDAU1NTWYbEREREdFA4xAFh4+PD7KzsxEQEIBFixZh+fLlyM7O7hTX1taGlJQUxMTEwMPDAwAgiiJiY2ORkJCAoKCgPucwffp0uLq6wt/fHzfeeCPS09OlzwICArB161a8+eabePnll9HR0YHp06fjhx9+uGB/mZmZUCqV0ubj49Pn3IiIiIiI7JVDFByhoaFmayLCwsJQW1sLk8kktRmNRkRHR0MUReTm5krtOTk5aG5uRmpqqkU5FBYWQqvV4pVXXsFbb72FdevWmeWj0WigVqsxY8YMFBUVYcSIEXjuuecu2F9qair0er20HTt2zKL8iIiIiIjskUOs4biYc8WGTqdDeXm5dHUDAMrLy1FVVQW5XG52TFBQEBYtWoT8/PwejXHuCsRVV10Fk8mE+Ph43H///ZDJZJ1inZ2dce211+LIkSMX7E8ul3fKiYiIiMgucdE4WcAhCo59+/aZ7e/duxf+/v6QyWRSsVFbW4uKigoMGzbMLHbTpk1Ys2aNtF9fX4+IiAgUFhYiJCSkT/l0dHTAaDSio6Ojy4LDZDLh0KFDmDVrVp/6JyIiIiIaKByi4Kirq0NycjKWLl0KrVaLnJwcZGVlwWg0IioqClqtFsXFxTCZTGhoaAAAeHl5wcXFxexJVQCgUCgAAH5+fhg7duxFx96+fTucnZ0RGBgIuVyO/fv3IzU1FQsXLoSzszMAID09HaGhoZg0aRJOnTqFp556CjqdDnfddVc/nwkiIiIiIsfiEAWHRqNBa2srgoODIZPJkJSUhPj4eOh0OuzatQsAoFarzY6pqKhAeHi4xWM7OTlh7dq1+OabbyCKInx9fZGYmIj77rtPijl58iTi4uLQ0NCAyy+/HNOmTcPHH3+Mq666yuLxiYiIiIgcmSCKomjrJAhoamr67xOr9BAEj4sfQAOWSmXrDMgevP22rTMge1BQYOsMyNba2prw978rodfrzdaoXirnfp/oMzLg4ep6yce3taa2Nigffthm53+gcIgrHERERERkQ1w0ThYY9GcxMjISCoWiyy0jI8PW6RERERERObRBf4UjLy8Pra2tXX7m5eV1ibMhIiIiIhpYBn3B4e3tbesUiIiIiIgGrEF/SxUREREREVnPoL/CQUREREQXIQiDcwG1INg6gwGBBYedqdtTC4//vpyQBqlTp2ydAdkBoyrE1imQHRgzxtYZkK1dYJkpkUMZhKUqERERERFdKiw4iIiIiIjIalhwEBERERGR1XANBxERERF1j28aJwvwLBIRERERkdWw4CAiIiIiIqthwUFERERERFZj9wVHeHg4VqxYYes0iIiIiIioD+y+4OiO0WhESkoKAgMD4e7uDpVKBY1Gg/r6+i7jDQYD1Go1BEFATU1Nj8Y4fPgwbrrpJowaNQqurq6YOHEiVq1aBaPRaJZHeno6/Pz84OrqiqlTp2L37t39MUUiIiIi2zu3aHwwbmQxh35KVUtLC7RaLdLS0jB16lScPHkSSUlJmDdvHvbv398pfuXKlVCpVDh48GCPx3B2doZGo8F1110HT09PHDx4EHFxcejo6EBGRgYAYNWqVXj55ZfxwgsvYPLkyXjnnXfwpz/9CR9//DGuvfbafpsvEREREZGjcYiCo729HYmJiSgoKICzszOWLVuG9PR0KJVKlJWVmcVu3rwZwcHBqKurw7hx46T2kpISlJaWYseOHSgpKenx2BMnTsTEiROlfV9fX1RWVuKDDz6Q2goKCvDII49g1qxZAIBly5bh3XffRVZWFl5++eW+TpuIiIiIyOE5xHWi/Px8ODk5obq6Ghs3bsT69euRl5fXZaxer4cgCPD09JTaGhsbERcXh4KCAri5uVmUy5EjR7B7927MmDFDajMYDHB1dTWLGzp0KD788MML9mMwGNDU1GS2ERERERENNA5RcPj4+CA7OxsBAQFYtGgRli9fjuzs7E5xbW1tSElJQUxMDDw8PAAAoigiNjYWCQkJCAoK6nMO06dPh6urK/z9/XHjjTciPT1d+iwiIgLr169HbW0tOjo6UFZWhqKiIhw/fvyC/WVmZkKpVEqbj49Pn3MjIiIiIrJXDlFwhIaGQhAEaT8sLAy1tbUwmUxSm9FoRHR0NERRRG5urtSek5OD5uZmpKamWpRDYWEhtFotXnnlFbz11ltYt26d9NnGjRvh7++PyZMnw8XFBYmJiViyZAmGdLPQKDU1FXq9XtqOHTtmUX5EREREVmPrhdtcNO7QHGINx8WcKzZ0Oh3Ky8ulqxsAUF5ejqqqKsjlcrNjgoKCsGjRIuTn5/dojHNXIK666iqYTCbEx8fj/vvvh0wmw4gRI7Bz5060tbXhxIkTUKlUeOihh8zWfvyWXC7vlBMRERER0UDjEAXHvn37zPb37t0Lf39/yGQyqdiora1FRUUFhg0bZha7adMmrFmzRtqvr69HREQECgsLERIS0qd8Ojo6YDQa0dHRAZlMJrW7urrC29sbRqMRO3bsQHR0dJ/6JyIiIiIaKByi4Kirq0NycjKWLl0KrVaLnJwcZGVlwWg0IioqClqtFsXFxTCZTGhoaAAAeHl5wcXFxexJVQCgUCgAAH5+fhg7duxFx96+fTucnZ0RGBgIuVyO/fv3IzU1FQsXLoSzszOAXwuiH3/8EWq1Gj/++CNWr16Njo4OrFy5sp/PBBERERGRY3GIgkOj0aC1tRXBwcGQyWRISkpCfHw8dDoddu3aBQBQq9Vmx1RUVCA8PNzisZ2cnLB27Vp88803EEURvr6+SExMxH333SfFtLW1YdWqVfjuu++gUCgwa9YsFBQUmD0pi4iIiIhoMLL7gqOyslL68/mLwQFg/PjxEEWxV/319piFCxdi4cKF3cbMmDEDX375Za/yICIiInIYg3UB9WCcsxXwLBIRERERkdUM+oIjMjISCoWiyy0jI8PW6REREREROTS7v6XK2vLy8tDa2trlZ15eXpc4GyIiIiKigWXQFxze3t62ToGIiIiIaMAa9AUHEREREV0EF42TBXgWiYiIiIjIaniFw84og/wBeNg6DbIhcYzK1imQHWj/tt7WKZAduPNBriUc7JpEEcttnQSRhXiFg4iIiIiIrIYFBxERERERWQ1vqSIiIiKi7nHROFmAZ5GIiIiIiKyGBQcREREREVkNCw4iIiIiIrIaFhxERERERGQ1dr9oPDw8HGq1Ghs2bLB1KkRERESDExeNkwUc+iwajUakpKQgMDAQ7u7uUKlU0Gg0qK/v+oVZBoMBarUagiCgpqamR2McPXoUgiB02vbu3WsW9/rrr2Py5MlwdXVFYGAg3n77bUunR0RERETk8By64GhpaYFWq0VaWhq0Wi2Kiopw+PBhzJs3r8v4lStXQqXq21uc3333XRw/flzapk2bJn328ccfIyYmBnfeeSc+/fRTLFiwAAsWLMDnn3/ep7GIiIiIiAYKhyg42tvbkZiYCKVSieHDhyMtLQ2iKEKpVKKsrAzR0dEICAhAaGgoNm/ejAMHDqCurs6sj5KSEpSWlmLdunV9ymHYsGEYPXq0tDk7O0ufbdy4EbfccgsefPBBXHnllXjiiSdw3XXXYfPmzRbNm4iIiIjI0TlEwZGfnw8nJydUV1dj48aNWL9+PfLy8rqM1ev1EAQBnp6eUltjYyPi4uJQUFAANze3PuUwb948jBw5EjfccAN27dpl9llVVRVmzpxp1hYREYGqqqoL9mcwGNDU1GS2ERERERENNHa/aBwAfHx8kJ2dDUEQEBAQgEOHDiE7OxtxcXFmcW1tbUhJSUFMTAw8PDwAAKIoIjY2FgkJCQgKCsLRo0d7NbZCoUBWVhauv/56DBkyBDt27MCCBQuwc+dO6dathoYGjBo1yuy4UaNGoaGh4YL9ZmZm4vHHH+9VLkREREQ2wUXjZAGHOIuhoaEQBEHaDwsLQ21tLUwmk9RmNBoRHR0NURSRm5srtefk5KC5uRmpqal9Gnv48OFITk5GSEgIfve73+Ef//gH/vrXv+Kpp57q+4QApKamQq/XS9uxY8cs6o+IiIiIyB45RMFxMeeKDZ1Oh7KyMunqBgCUl5ejqqoKcrkcTk5OmDRpEgAgKCgIixcv7tN4ISEhOHLkiLQ/evRoNDY2msU0NjZi9OjRF+xDLpfDw8PDbCMiIiIiGmgcouDYt2+f2f7evXvh7+8PmUwmFRu1tbV49913MWzYMLPYTZs24eDBg6ipqUFNTY30uNrCwkL8/e9/71M+NTU1GDNmjLQfFhaGPXv2mMWUlZUhLCysT/0TEREREQ0UDrGGo66uDsnJyVi6dCm0Wi1ycnKQlZUFo9GIqKgoaLVaFBcXw2QySesmvLy84OLignHjxpn1pVAoAAB+fn4YO3bsRcfOz8+Hi4sLrr32WgBAUVERtm7darZoPSkpCTNmzEBWVhZmz56NV199Ffv378fzzz/fX6eAiIiIiMghOUTBodFo0NraiuDgYMhkMiQlJSE+Ph46nU56YpRarTY7pqKiAuHh4f0y/hNPPAGdTgcnJydMnjwZhYWFiIqKkj6fPn06XnnlFaxatQoPP/ww/P39sXPnTlxzzTX9Mj4RERGRTQnC4FxAfd4aYuo7QRRF0dZJENDU1ASlUglAD4DrOQYzcUzfXk5JA0vrt/W2ToHswFBvL1unQDbWJIpQnjoFvV5vk/We536f6J9/Hh59fLWAI2tqaYEyPt5m53+gGISlKhERERERXSqDvuCIjIyEQqHocsvIyLB1ekREREREDs0h1nBYU15eHlpbW7v8zMuLl7KJiIiIiCwx6AsOb29vW6dAREREZN/4pnGyAM8iERERERFZDQsOIiIiIiKymkF/S5W90a/JgYerq63TIFu6+S1bZ0B24Lx3i9Ig9t3iX2ydAtmYwdAE5CptnQaRRXiFg4iIiIiIrIZXOIiIiIioe1w0ThbgWSQiIiIiIqthwUFERERERFbDgoOIiIiIiKyGBQcREREREVkNF40TERERUfe4aJwsYPdnMTw8HCtWrLB1GkRERERE1Ad2X3B0x2g0IiUlBYGBgXB3d4dKpYJGo0F9fX2X8QaDAWq1GoIgoKampsfjiKKIdevW4YorroBcLoe3tzf+/ve/d+r7kUcega+vL+RyOcaPH4+tW7daMj0iIiIiIofn0LdUtbS0QKvVIi0tDVOnTsXJkyeRlJSEefPmYf/+/Z3iV65cCZVKhYMHD/ZqnKSkJJSWlmLdunUIDAzEL7/8gl9+MX/7a3R0NBobG7FlyxZMmjQJx48fR0dHh0XzIyIiIiJydA5RcLS3tyMxMREFBQVwdnbGsmXLkJ6eDqVSibKyMrPYzZs3Izg4GHV1dRg3bpzUXlJSgtLSUuzYsQMlJSU9Hvurr75Cbm4uPv/8cwQEBAAAJkyYYBaze/duvPfee/juu+/g5eUFABg/fnwfZ0tERERENHA4xC1V+fn5cHJyQnV1NTZu3Ij169cjLy+vy1i9Xg9BEODp6Sm1NTY2Ii4uDgUFBXBzc+vV2P/+978xceJEFBcXY8KECRg/fjzuuususyscu3btQlBQEJ588kl4e3vjiiuuwAMPPIDW1tYL9mswGNDU1GS2EREREdmlc4vGB+NGFnOIKxw+Pj7Izs6GIAgICAjAoUOHkJ2djbi4OLO4trY2pKSkICYmBh4eHgB+XX8RGxuLhIQEBAUF4ejRo70a+7vvvoNOp8Prr7+Ol156CSaTCffddx+ioqJQXl4uxXz44YdwdXXFv/71L/z888+4++67ceLECbz44otd9puZmYnHH3+89yeDiIiIiMiBOETZFhoaCkEQpP2wsDDU1tbCZDJJbUajEdHR0RBFEbm5uVJ7Tk4OmpubkZqa2qexOzo6YDAY8NJLL+HGG29EeHg4tmzZgoqKChw+fFiKEQQB27dvR3BwMGbNmoX169cjPz//glc5UlNTodfrpe3YsWN9yo+IiIiIyJ45RMFxMeeKDZ1Oh7KyMunqBgCUl5ejqqoKcrkcTk5OmDRpEgAgKCgIixcvvmjfY8aMgZOTE6644gqp7corrwQA1NXVSTHe3t5QKpVmMaIo4ocffuiyX7lcDg8PD7ONiIiIiGigcYiCY9++fWb7e/fuhb+/P2QymVRs1NbW4t1338WwYcPMYjdt2oSDBw+ipqYGNTU1ePvttwEAhYWFnR5t25Xrr78e7e3t+Pbbb6W2b775BgDg6+srxdTX1+P06dNmMUOGDMHYsWP7NmkiIiIiogHAIdZw1NXVITk5GUuXLoVWq0VOTg6ysrJgNBoRFRUFrVaL4uJimEwmNDQ0AAC8vLzg4uJi9qQqAFAoFAAAPz+/HhUDM2fOxHXXXYc77rgDGzZsQEdHB+655x784Q9/kK563H777XjiiSewZMkSPP744/j555/x4IMP4o477sDQoUP7+WwQERERXWKDdQH1YJyzFThEwaHRaNDa2org4GDIZDIkJSUhPj4eOp0Ou3btAgCo1WqzYyoqKhAeHm7x2EOGDMG///1vLF++HP/3f/8Hd3d3REZGIisrS4pRKBQoKyvD8uXLERQUhGHDhiE6Ohpr1qyxeHwiIiIiIkdm9wVHZWWl9OfzF4MDv77rQhTFXvXXl2NUKhV27NjRbczkyZM7vROEiIiIiGiw43UiIiIiIiKymkFfcERGRkKhUHS5ZWRk2Do9IiIiIiKHZve3VFlbXl7eBd+V4eXldYmzISIiIrJDXDROFhj0BYe3t7etUyAiIiIiGrBYthERERERkdWw4CAiIiIiIqsZ9LdU2Zvs9uVwbfewdRpkQymKWlunQHZgeWyzrVMge1Bfb+sMyMaaTp/Gb94KQORwWHAQERERUfe4aJwswLNIRERERERWw4KDiIiIiIishgUHERERERFZDQsOIiIiIiKyGi4aJyIiIqLucdE4WYBnkYiIiIiIrMbuC47w8HCsWLHC1mkQEREREVEf2H3B0R2j0YiUlBQEBgbC3d0dKpUKGo0G9Rd4UZLBYIBarYYgCKipqenRGKtXr4YgCJ02d3d3KeaLL77ArbfeivHjx0MQBGzYsKEfZkdERERE5PgcuuBoaWmBVqtFWloatFotioqKcPjwYcybN6/L+JUrV0KlUvVqjAceeADHjx8326666ir85S9/Mctj4sSJ+Mc//oHRo0dbNCciIiIiooHEIRaNt7e3IzExEQUFBXB2dsayZcuQnp4OpVKJsrIys9jNmzcjODgYdXV1GDdunNReUlKC0tJS7NixAyUlJT0eW6FQQKFQSPsHDx7El19+iWeffVZq+93vfoff/e53AICHHnqor9MkIiIisk9cNE4WcIizmJ+fDycnJ1RXV2Pjxo1Yv3498vLyuozV6/UQBAGenp5SW2NjI+Li4lBQUAA3NzeLcsnLy8MVV1yBG2+80aJ+DAYDmpqazDYiIiIiooHGIQoOHx8fZGdnIyAgAIsWLcLy5cuRnZ3dKa6trQ0pKSmIiYmBh4cHAEAURcTGxiIhIQFBQUEW5dHW1obt27fjzjvvtKgfAMjMzIRSqZQ2Hx8fi/skIiIiIrI3DlFwhIaGQhAEaT8sLAy1tbUwmUxSm9FoRHR0NERRRG5urtSek5OD5uZmpKamWpzHv/71LzQ3N2Px4sUW95Wamgq9Xi9tx44ds7hPIiIiIiJ74xBrOC7mXLGh0+lQXl4uXd0AgPLyclRVVUEul5sdExQUhEWLFiE/P7/H4+Tl5WHOnDkYNWqUxTnL5fJOORERERERDTQOUXDs27fPbH/v3r3w9/eHTCaTio3a2lpUVFRg2LBhZrGbNm3CmjVrpP36+npERESgsLAQISEhPc7h+++/R0VFBXbt2mXZZIiIiIgcjSAMzgXU591hQ33nEAVHXV0dkpOTsXTpUmi1WuTk5CArKwtGoxFRUVHQarUoLi6GyWRCQ0MDAMDLywsuLi5mT6oCID1xys/PD2PHju1xDlu3bsWYMWMQGRnZ6bOzZ8/iyy+/lP78448/oqamBgqFApMmTerrtImIiIiIHJ5DFBwajQatra0IDg6GTCZDUlIS4uPjodPppCsOarXa7JiKigqEh4f3y/gdHR3Ytm0bYmNjIZPJOn1eX1+Pa6+9Vtpft24d1q1bhxkzZqCysrJfciAiIiIickR2X3Cc/4P9/MXgADB+/HiIotir/vpyzJAhQ7pd1N2XPomIiIiIBoNBeDMeERERERFdKoO+4IiMjJTeJv7bLSMjw9bpEREREdneuTeND8aNLGb3t1RZW15eHlpbW7v8zMvL6xJnQ0REREQ0sAz6gsPb29vWKRARERERDVi8TkRERERERFbDgoOIiIiIiKxm0N9SZW9Wr7Z1BmRrxcX+tk6B7MDWrbbOgOzBke8CbJ0C2VhLS5OtU/jVYF1APRjnbAU8i0REREREZDUsOIiIiIiIyGpYcBARERERkdWw4CAiIiIiIqthwUFERERE3bP1274d7E3jTz/9NMaPHw9XV1eEhISgurr6grEvvPACbrzxRlx++eW4/PLLMXPmzE7xsbGxEATBbLvlllv6lJstsOAgIiIiIuonhYWFSE5OxmOPPQatVoupU6ciIiIC//nPf7qMr6ysRExMDCoqKlBVVQUfHx/88Y9/xI8//mgWd8stt+D48ePS9s9//vNSTKdfsOAgIiIiIuon69evR1xcHJYsWYKrrroKzz77LNzc3LD1As873759O+6++26o1WpMnjwZeXl56OjowJ49e8zi5HI5Ro8eLW2XX375pZhOv7D7giM8PBwrVqywdRpERERERN06e/YsDhw4gJkzZ0ptQ4YMwcyZM1FVVdWjPlpaWmA0GuHl5WXWXllZiZEjRyIgIADLli3DiRMn+jV3a7L7gqM7RqMRKSkpCAwMhLu7O1QqFTQaDerr67uMNxgMUKvVEAQBNTU1PR7nnXfeQWhoKC677DKMGDECt956K44ePSp9/uGHH+L666/HsGHDMHToUEyePBnZ2dkWzo6IiIiI7EFTU5PZZjAYuoz7+eefYTKZMGrUKLP2UaNGoaGhoUdjpaSkQKVSmRUtt9xyC1566SXs2bMHa9euxXvvvYfIyEiYTKa+T+oScuiCo6WlBVqtFmlpadBqtSgqKsLhw4cxb968LuNXrlwJlUrVqzG+//57zJ8/HzfffDNqamrwzjvv4Oeff8af//xnKcbd3R2JiYl4//338dVXX2HVqlVYtWoVnn/+eYvmR0RERGQXbL1w28aLxn18fKBUKqUtMzPTKqf5H//4B1599VX861//gqurq9R+2223Yd68eQgMDMSCBQtQXFyMTz75BJWVlVbJo7852TqBnmhvb0diYiIKCgrg7OyMZcuWIT09HUqlEmVlZWaxmzdvRnBwMOrq6jBu3DipvaSkBKWlpdixYwdKSkp6PPaBAwdgMpmwZs0aDPnvl+6BBx7A/PnzYTQa4ezsjGuvvRbXXnutdMz48eNRVFSEDz74APHx8RbOnoiIiIhs6dixY/Dw8JD25XJ5l3HDhw+HTCZDY2OjWXtjYyNGjx7d7Rjr1q3DP/7xD7z77ruYMmVKt7ETJ07E8OHDceTIEfz+97/v4SxsxyGucOTn58PJyQnV1dXYuHEj1q9fj7y8vC5j9Xo9BEGAp6en1NbY2Ii4uDgUFBTAzc2tV2NPmzYNQ4YMwYsvvgiTyQS9Xo+CggLMnDkTzs7OXR7z6aef4uOPP8aMGTMu2K/BYOh0eY6IiIiI7I+Hh4fZdqGCw8XFBdOmTTNb8H1uAXhYWNgF+3/yySfxxBNPYPfu3QgKCrpoPj/88ANOnDiBMWPG9H4yNuAQBYePjw+ys7MREBCARYsWYfny5V2ukWhra0NKSgpiYmKkKlQURcTGxiIhIaFHf4G/NWHCBJSWluLhhx+GXC6Hp6cnfvjhB7z22mudYseOHQu5XI6goCDcc889uOuuuy7Yb2ZmptmlOR8fn17nRkRERET2JTk5GS+88ALy8/Px1VdfYdmyZThz5gyWLFkCANBoNEhNTZXi165di7S0NGzduhXjx49HQ0MDGhoacPr0aQDA6dOn8eCDD2Lv3r04evQo9uzZg/nz52PSpEmIiIiwyRx7yyEKjtDQUAiCIO2HhYWhtrbWbKGM0WhEdHQ0RFFEbm6u1J6Tk4Pm5mazv9jeaGhoQFxcHBYvXoxPPvkE7733HlxcXBAVFQVRFM1iP/jgA+zfvx/PPvssNmzY0O3zkVNTU6HX66Xt2LFjfcqPiIiIiOzHwoULsW7dOjz66KNQq9WoqanB7t27pYXkdXV1OH78uBSfm5uLs2fPIioqCmPGjJG2devWAQBkMhk+++wzzJs3D1dccQXuvPNOTJs2DR988MEFr7TYG4dYw3Ex54oNnU6H8vJys3vsysvLUVVV1ekvJCgoCIsWLUJ+fn63fT/99NNQKpV48sknpbaXX34ZPj4+2LdvH0JDQ6X2CRMmAAACAwPR2NiI1atXIyYmpst+5XK5w3xJiIiIaJCz4K3bDq2Pc05MTERiYmKXn/12off5Tz7tytChQ/HOO+/0KQ974RAFx759+8z29+7dC39/f8hkMqnYqK2tRUVFBYYNG2YWu2nTJqxZs0bar6+vR0REBAoLCxESEnLRsVtaWqTF4ufIZDIAv96TdyEdHR0XfGQaEREREdFg4RAFR11dHZKTk7F06VJotVrk5OQgKysLRqMRUVFR0Gq1KC4uhslkkp5x7OXlBRcXF7MnVQGAQqEAAPj5+WHs2LEXHXv27NnIzs5Geno6YmJi0NzcjIcffhi+vr7Sk6mefvppjBs3DpMnTwYAvP/++1i3bh3uvffe/jwNREREREQOxyEKDo1Gg9bWVgQHB0MmkyEpKQnx8fHQ6XTYtWsXAECtVpsdU1FRgfDwcIvHvvnmm/HKK6/gySefxJNPPgk3NzeEhYVh9+7dGDp0KIBfr2akpqbi+++/h5OTE/z8/LB27VosXbrU4vGJiIiIiByZIP525TPZRFNTE5RKJQA9AI+LhdMAdsMNts6A7MHWrbbOgOzBkSO2zoBsraWlCVFRSuj1erM1qpfKud8n+pISeLi7X/Lxba3pzBkoIyNtdv4HCoe4wkFERERENsRF42SBQX8WIyMjoVAoutwyMjJsnR4RERERkUMb9Fc48vLy0Nra2uVnXl5elzgbIiIiIqKBZdAXHN7e3rZOgYiIiIhowBr0t1QREREREZH1DPorHERERER0EVw0ThZgwWFnRo7kd3uw02ptnQHZg6IiW2dA9sDT09YZkK1dYJkpkUPhT1siIiIiIrIaFhxERERERGQ1LDiIiIiIiMhquIaDiIiIiLrHReNkAZ5FIiIiIiKyGhYcRERERERkNSw4iIiIiIjIauy+4AgPD8eKFStsnQYREREREfWB3Rcc3TEajUhJSUFgYCDc3d2hUqmg0WhQX1/fZbzBYIBarYYgCKipqenxOK+99hrUajXc3Nzg6+uLp556yuzz48eP4/bbb8cVV1yBIUOGsEAiIiKigUUQ/rdwfDBtgmDrMz8gOHTB0dLSAq1Wi7S0NGi1WhQVFeHw4cOYN29el/ErV66ESqXq1RglJSVYtGgREhIS8Pnnn+OZZ55BdnY2Nm/eLMUYDAaMGDECq1atwtSpUy2aExERERHRQOIQBUd7ezsSExOhVCoxfPhwpKWlQRRFKJVKlJWVITo6GgEBAQgNDcXmzZtx4MAB1NXVmfVRUlKC0tJSrFu3rldjFxQUYMGCBUhISMDEiRMxe/ZspKamYu3atRBFEQAwfvx4bNy4ERqNBkqlst/mTURERETk6Byi4MjPz4eTkxOqq6uxceNGrF+/Hnl5eV3G6vX6/2fv/sOiLPP9gb+HGRgEZAj8CYIoEmai4LL8qC3pZEtacaz84hLtRCmgG4rSroQrm5KJbsCI2KGT1MaSptvKtkZhksCmiRBOlK0tYcXghrBt5gwGjMMw3z9cn9MEIgyMM8O8X9f1XIfnnvu5788z23Wd+Xg/n+eGSCSCh4eH0NbR0YGkpCSUlpbCxcVlWHNrtVo4OzsbtY0bNw7//Oc/oVKphn0vPxxXo9EYHUREREREY41NJBy+vr5QKBQICgpCQkIC1qxZA4VC0a9fT08PMjIyEB8fD3d3dwCAwWBAYmIiVq1ahbCwsGHPHRMTg7KyMhw9ehR9fX34/PPPkZeXB+BK7YapcnJyIJPJhMPX19fksYiIiIiIrJVNJByRkZEQ/aBoJyoqCs3NzdDr9UKbTqdDXFwcDAYDioqKhPbCwkJ0dnYiMzPTpLmTkpKQmpqK+++/H05OToiMjMQvfvELAIDDCHafzMzMhFqtFo5z586ZPBYRERGRWVm6eNuSB43YmPgWryYbKpUKlZWVwuoGAFRVVaG2thZSqRQSiQSzZs0CAISFheGxxx677tgikQg7duzApUuXoFKp0N7ejvDwcADAzJkzTY5ZKpXC3d3d6CAiIiIiGmsklg5gKOrq6ozOT548icDAQIjFYiHZaG5uRnV1Nby8vIz67tq1C1u3bhXO29raEBMTgwMHDiAiImLIMYjFYvj4+AAAXn/9dURFRWHixIkjuCsiIiIiorHPJhKO1tZWpKenIyUlBUqlEoWFhcjLy4NOp8OyZcugVCpRXl4OvV6P9vZ2AICnpyecnJzg5+dnNJabmxsAICAgANOmTbvu3P/+97/x5z//GdHR0ejp6cEf/vAHvPHGG/jb3/5m1O/qvh6XLl3CN998g8bGRjg5OWHOnDmj8A0QEREREdkmm0g45HI5uru7ER4eDrFYjLS0NCQnJ0OlUuHQoUMAgJCQEKNrqqurER0dPSrzl5SU4Ne//jUMBgOioqJQU1MjPFZ1VWhoqPD3qVOnsG/fPkyfPh0tLS2jEgMRERERkS2y+oSjpqZG+PuHxeDAlf0vru6FMVTDvWbChAmora29br/hxkFERERkM+y1gNoe79kM+C0SEREREZHZ2H3CsXjxYri5uQ14bNu2zdLhERERERHZNKt/pMrciouL0d3dPeBnnp6eNzgaIiIiIqKxxe4TjquvuiUiIiIiotFn9wkHEREREV0Hi8ZpBPgtEhERERGR2XCFw8r861+WjoAsjXtFEgAkJFg6ArIGq1dbOgKyNJ3O0hEQjRxXOIiIiIiIyGyYcBARERERkdnwkSoiIiIiGhyLxmkE+C0SEREREZHZMOEgIiIiIiKzYcJBRERERERmw4SDiIiIiIjMxqJF49HR0QgJCcHOnTstGQYRERERDYZF4zQCVvst6nQ6ZGRkIDg4GK6urvD29oZcLkdbW9uA/bVaLUJCQiASidDY2DikOXp6epCYmIjg4GBIJBIsXbq0X5/z58/jkUcewc033wwHBwesW7du0DH3798PkUg04FhERERERPbGahOOrq4uKJVKZGVlQalUoqysDE1NTYiNjR2w/4YNG+Dt7T2sOfR6PcaNG4e1a9di0aJFA/bRarWYOHEiNm3ahPnz5w86XktLC37961/jjjvuGFYcRERERERjlcUTjt7eXqSmpkImk2HChAnIysqCwWCATCZDZWUl4uLiEBQUhMjISOzevRunTp1Ca2ur0RgVFRU4cuQIcnNzhzW3q6srioqKkJSUhClTpgzYx9/fHwUFBZDL5ZDJZNccS6/XIyEhAVu2bMHMmTOHFQcRERER0Vhl8YSjpKQEEokE9fX1KCgoQH5+PoqLiwfsq1arIRKJ4OHhIbR1dHQgKSkJpaWlcHFxuUFR95ednY1JkyZhxYoVQ+qv1Wqh0WiMDiIiIiKiscbiO437+vpCoVBAJBIhKCgIp0+fhkKhQFJSklG/np4eZGRkID4+Hu7u7gAAg8GAxMRErFq1CmFhYWhpabHAHQDHjx/Hyy+/POTaEQDIycnBli1bzBcUERER0Whh0TiNgMW/xcjISIhEIuE8KioKzc3N0Ov1QptOp0NcXBwMBgOKioqE9sLCQnR2diIzM/OGxvxDnZ2d+OUvf4k9e/ZgwoQJQ74uMzMTarVaOM6dO2fGKImIiIiILMPiKxzXczXZUKlUqKqqElY3AKCqqgq1tbWQSqVG14SFhSEhIQElJSVmj++LL75AS0sLHnjgAaGtr68PACCRSNDU1ISAgIB+10ml0n5xExERERGNNRZPOOrq6ozOT548icDAQIjFYiHZaG5uRnV1Nby8vIz67tq1C1u3bhXO29raEBMTgwMHDiAiIuKGxD979mycPn3aqG3Tpk3o7OxEQUEBfH19b0gcRERERETWyOIJR2trK9LT05GSkgKlUonCwkLk5eVBp9Nh2bJlUCqVKC8vh16vR3t7OwDA09MTTk5O8PPzMxrLzc0NABAQEIBp06YNaf4zZ87g8uXLuHDhAjo7O4U6jJCQEKHP1bZLly7hm2++QWNjI5ycnDBnzhw4Oztj7ty5RmNeLWr/cTsRERERkb2xeMIhl8vR3d2N8PBwiMVipKWlITk5GSqVCocOHQJg/OMfAKqrqxEdHT0q8y9ZsgQqlUo4Dw0NBXClIP3HbQBw6tQp7Nu3D9OnT7dYkToRERHRDcWicRoBiyYcNTU1wt8/LAYHrux/8cMf/UNhyjVDSRqGO+arr746rP5ERERERGMV0zYiIiIiIjKbMZ1wLF68GG5ubgMe27Zts3R4RERERERjnsVrOMypuLgY3d3dA37m6el5g6MhIiIiIrI/Yzrh8PHxsXQIRERERLaPReM0AvwWiYiIiIjIbJhwEBERERGR2YzpR6qIbFFXl6UjIGtwjfIzsjNxcZaOgCytuxt4911LR0E0MlzhICIiIiIis+EKBxERERENTiSyzwJqkcjSEYwJdvhfDhERERER3ShMOIiIiIiIyGyYcBARERERkdkw4SAiIiIiIrNh0TgRERERDY47jdMIWPRbjI6Oxrp16ywZAhERERERmZHVpm06nQ4ZGRkIDg6Gq6srvL29IZfL0dbWNmB/rVaLkJAQiEQiNDY2DmmOnp4eJCYmIjg4GBKJBEuXLu3Xp6ysDPfccw8mTpwId3d3REVF4d0f7cDz/vvv44EHHoC3tzdEIhHefPPNYd4tEREREdHYZLUJR1dXF5RKJbKysqBUKlFWVoampibExsYO2H/Dhg3w9vYe1hx6vR7jxo3D2rVrsWjRogH7vP/++7jnnnvwzjvv4NSpU7jrrrvwwAMP4KOPPhL6fP/995g/fz5eeOGFYc1PRERERDTWWbyGo7e3F6mpqSgtLYWjoyNWr16N7OxsyGQyVFZWGvXdvXs3wsPD0draCj8/P6G9oqICR44cwcGDB1FRUTHkuV1dXVFUVAQA+OCDD3Dx4sV+fXbu3Gl0vm3bNvz1r3/FW2+9hdDQUADA4sWLsXjx4iHPS0RERERkLyyecJSUlGDFihWor69HQ0MDkpOT4efnh6SkpH591Wo1RCIRPDw8hLaOjg4kJSXhzTffhIuLi9nj7evrQ2dnJzw9PUc0jlarhVarFc41Gs1IQyMiIiIyDxaN0whY/Fv09fWFQqFAUFAQEhISsGbNGigUin79enp6kJGRgfj4eLi7uwMADAYDEhMTsWrVKoSFhd2QeHNzc3Hp0iXExcWNaJycnBzIZDLh8PX1HaUIiYiIiIish8UTjsjISIhEIuE8KioKzc3N0Ov1QptOp0NcXBwMBoPwCBQAFBYWorOzE5mZmTck1n379mHLli3405/+hEmTJo1orMzMTKjVauE4d+7cKEVJRERERGQ9LP5I1fVcTTZUKhWqqqqE1Q0AqKqqQm1tLaRSqdE1YWFhSEhIQElJyajFsX//fqxcuRJvvPHGNQvMh0MqlfaLm4iIiIhorLF4wlFXV2d0fvLkSQQGBkIsFgvJRnNzM6qrq+Hl5WXUd9euXdi6datw3tbWhpiYGBw4cAARERGjFuPrr7+OJ554Avv378d99903auMSEREREY11Fk84WltbkZ6ejpSUFCiVShQWFiIvLw86nQ7Lli2DUqlEeXk59Ho92tvbAQCenp5wcnIyelMVALi5uQEAAgICMG3atCHNf+bMGVy+fBkXLlxAZ2ensIdHSEgIgCuPUT322GMoKChARESEEMO4ceMgk8kAAJcuXcLZs2eFMb/66is0NjbC09OzX4xERERENodF4zQCFk845HI5uru7ER4eDrFYjLS0NCQnJ0OlUuHQoUMA/u/H/1XV1dWIjo4elfmXLFkClUolnF991a3BYAAAvPTSS+jt7cWTTz6JJ598Uuj32GOP4dVXXwUANDQ04K677hI+S09P79eHiIiIiMgeWTThqKmpEf7+YTE4APj7+ws/+ofKlGtaWloG/fyHMV5LdHT0sOclIiIiIrIHXCciIiIiIiKzGdMJx+LFi+Hm5jbgsW3bNkuHR0REREQ05lm8hsOciouL0d3dPeBnI90pnIiIiMhusGicRmBMJxw+Pj6WDoGIiIiIyK4xbSMiIiIiIrNhwkFERERERGYzph+pskXqsLvhLuH/LHaNLzQgAPCYa+kIyAo03TTR0iGQhUmllo6AaOT4y5aIiIiIBseicRoBfotERERERGQ2TDiIiIiIiMhsmHAQEREREZHZMOEgIiIiIiKzYdE4EREREQ2OReMEwGAwQCQSDfs6fotERERERAQASExMxPfff9+vvaWlBXfeeadJY1o04YiOjsa6dessGQIREREREf3Hxx9/jHnz5qG2tlZoKykpwfz58zFhwgSTxrTaFQ6dToeMjAwEBwfD1dUV3t7ekMvlaGtrG7C/VqtFSEgIRCIRGhsbhzRHT08PEhMTERwcDIlEgqVLl15z7N/+9reYPn06pFIp/P398corrxjFmp2djYCAADg7O2P+/Pk4fPjwcG+ZiIiIiMii6uvr8dBDDyE6OhobN25EXFwcUlNTkZubi7/85S8mjWm1NRxdXV1QKpXIysrC/Pnz8d133yEtLQ2xsbFoaGjo13/Dhg3w9vbGxx9/POQ59Ho9xo0bh7Vr1+LgwYPX7BcXF4eOjg68/PLLmDVrFs6fP4++vj7h802bNuG1117Dnj17MHv2bLz77rt48MEHceLECYSGhg7vxomIiIiILMTR0RHPP/88XFxc8Oyzz0IikeBvf/sboqKiTB7T4glHb28vUlNTUVpaCkdHR6xevRrZ2dmQyWSorKw06rt7926Eh4ejtbUVfn5+QntFRQWOHDmCgwcPoqKiYshzu7q6oqioCADwwQcf4OLFi/36HD58GH/729/w5ZdfwtPTEwDg7+9v1Ke0tBS//e1vsWTJEgDA6tWr8d577yEvLw+vvfbakOMhIiIiskosGrcbOp0OTz/9NF544QVkZmbi+PHjeOihh/Dyyy8Lv3WHy+LfYklJCSQSCerr61FQUID8/HwUFxcP2FetVkMkEsHDw0No6+joQFJSEkpLS+Hi4jLq8R06dAhhYWH4/e9/Dx8fH9x888349a9/je7ubqGPVquFs7Oz0XXjxo3D8ePHrzmuVquFRqMxOoiIiIiILCksLAyHDh1CTU0NnnvuOdTU1GDdunV46KGH8Ktf/cqkMS2ecPj6+kKhUCAoKAgJCQlYs2YNFApFv349PT3IyMhAfHw83N3dAVx5NVdiYiJWrVqFsLAws8T35Zdf4vjx4/j000/xl7/8BTt37sSf//xnoy88JiYG+fn5aG5uRl9fHyorK1FWVobz589fc9ycnBzIZDLh8PX1NUv8RERERERDFRYWhsbGRkRGRgIARCIRMjIyUFtbi/fff9+kMS2ecERGRhq9zzcqKgrNzc3Q6/VCm06nQ1xcHAwGg/AIFAAUFhais7MTmZmZZouvr68PIpEIe/fuRXh4OJYsWYL8/HyUlJQIqxwFBQUIDAzE7Nmz4eTkhNTUVDz++ONwGGQZLjMzE2q1WjjOnTtntnsgIiIiIhqKl19+Ga6urv3aQ0NDcerUKZPGtHjCcT1Xkw2VSoXKykphdQMAqqqqUFtbC6lUColEglmzZgG4kpk99thjozL/1KlT4ePjA5lMJrTdcsstMBgM+Oc//wkAmDhxIt588018//33UKlU+Mc//gE3NzfMnDnzmuNKpVK4u7sbHUREREREllZaWorbb78d3t7eUKlUAICdO3ea/BZWiyccdXV1RucnT55EYGAgxGKxkGw0Nzfjvffeg5eXl1HfXbt24eOPP0ZjYyMaGxvxzjvvAAAOHDiA5557blTiu/3229HW1oZLly4JbZ9//jkcHBwwbdo0o77Ozs7w8fFBb28vDh48iP/+7/8elRiIiIiILOpq0bg9HnamqKgI6enpWLJkCS5evCg8deTh4YGdO3eaNKbFv8XW1lakp6ejqakJr7/+OgoLC5GWlgadTodly5ahoaEBe/fuhV6vR3t7O9rb23H58mUAgJ+fH+bOnSscN998MwAgICCgXzJwLWfOnEFjYyMuXLgAtVotJC9XPfLII/Dy8sLjjz+OM2fO4P3338dvfvMbPPHEExg3bhyAK0lTWVkZvvzySxw7dgz33nsv+vr6sGHDhtH9soiIiIiIzKiwsBB79uzBb3/7W4jFYqE9LCwMp0+fNmlMi78WVy6Xo7u7G+Hh4RCLxUhLS0NycjJUKhUOHToEAAgJCTG6prq6GtHR0aMy/5IlS4SlIgDCvhkGgwEA4ObmhsrKSqxZswZhYWHw8vJCXFwctm7dKlzT09ODTZs24csvv4SbmxuWLFmC0tJSo7dpERERERFZu6+++mrAfeSkUim+//57k8a0aMJRU1Mj/P3DYnDgyl4XV3/0D5Up17S0tFy3z+zZs/vtCfJDCxcuxJkzZ4Y1LxERERGRtZkxYwYaGxsxffp0o/bDhw/jlltuMWlMi69wEBERERGRdUhPT8eTTz6Jnp4eGAwG1NfX4/XXX0dOTs4198q7njGdcCxevBjHjh0b8LONGzdi48aNNzgiIiIiIhskEtllATV+sHWDvVi5ciXGjRuHTZs2oaurC4888gi8vb1RUFCAX/ziFyaNOaYTjuLiYqMdwX/I09PzBkdDRERERGT9EhISkJCQgK6uLly6dAmTJk0a0XhjOuHw8fGxdAhERERERDbJxcUFLi4uIx5nTCccREREREQ0uNDQUIiG+PiYUqkc9vhMOIiIiIiI7NjSpUuFv3t6evA///M/mDNnDqKiogBc2Zj773//O371q1+ZND4TDiuz7e6jkErdLR0GWVDjTktHQNbgj3+0dARkDX6wDy3ZqZ4eS0fwH3a667a93PMzzzwj/L1y5UqsXbsWzz77bL8+586dM2l8+/gWiYiIiIjout544w3I5fJ+7Y8++igOHjxo0phMOIiIiIiICAAwbtw4fPDBB/3aP/jgAzg7O5s0Jh+pIiIiIiIiAMC6deuwevVqKJVKhIeHAwDq6urwyiuvICsry6QxmXAQEREREREA4Omnn8bMmTNRUFCA1157DQBwyy234A9/+APi4uJMGpMJBxERERENjkXjdiUuLs7k5GIgTDiIiIiIiMjI5cuX8a9//Qt9fX1G7X5+fsMeiwkHEREREREBAJqbm/HEE0/gxIkTRu0GgwEikQh6vX7YY46JhCM6OhohISHYuXOnpUMhIiIiIrJZiYmJkEgkKC8vx9SpU4e8A/lgxvyDaTqdDhkZGQgODoarqyu8vb0hl8vR1tY2YH+tVouQkBCIRCI0DnHHpZ6eHiQmJiI4OBgSicRot0YiIiIiIlvR2NiI//3f/8XixYsREhKC+fPnGx2mGPMJR1dXF5RKJbKysqBUKlFWVoampibExsYO2H/Dhg3w9vYe1hx6vR7jxo3D2rVrsWjRotEIm4iIiMh6XC0at8fDzsyZMwf//ve/R3XMMfMt9vb2IjU1FTKZDBMmTEBWVhYMBgNkMhkqKysRFxeHoKAgREZGYvfu3Th16hRaW1uNxqioqMCRI0eQm5s7rLldXV1RVFSEpKQkTJkyZTRvi4iIiIjohtmxYwc2bNiAmpoafPvtt9BoNEaHKcZEDQcAlJSUYMWKFaivr0dDQwOSk5Ph5+eHpKSkfn3VajVEIhE8PDyEto6ODiQlJeHNN9+Ei4uL2ePVarXQarXCuan/AxIRERERjZarT+vcfffdRu12XzQOAL6+vlAoFBCJRAgKCsLp06ehUCj6JRw9PT3IyMhAfHw83N3dAVz5AhMTE7Fq1SqEhYWhpaXF7PHm5ORgy5YtZp+HiIiIiGioqqurR33MMZNwREZGGlXRR0VFIS8vD3q9HmKxGMCVAvK4uDgYDAYUFRUJfQsLC9HZ2YnMzMwbFm9mZibS09OFc41GA19f3xs2PxERERHRjy1cuHDUxxwzCcf1XE02VCoVqqqqhNUNAKiqqkJtbS2kUqnRNWFhYUhISEBJScmoxyOVSvvNR0RERGSV7LSA2l7u+ZNPPsHcuXPh4OCATz75ZNC+8+bNG/b4YybhqKurMzo/efIkAgMDIRaLhWSjubkZ1dXV8PLyMuq7a9cubN26VThva2tDTEwMDhw4gIiIiBsSPxERERGRJYSEhKC9vR2TJk0StocwGAz9+tl9DUdrayvS09ORkpICpVKJwsJC5OXlQafTYdmyZVAqlSgvL4der0d7ezsAwNPTE05OTv22aHdzcwMABAQEYNq0aUOa/8yZM7h8+TIuXLiAzs5OYQ+PkJCQUbtHIiIiIqLR9tVXX2HixInC36NtzCQccrkc3d3dCA8Ph1gsRlpaGpKTk6FSqXDo0CEA/X/8V1dXIzo6elTmX7JkCVQqlXAeGhoKAANmh0RERERE1mL69OkD/j1axkTCUVNTI/z9w2JwAPD39x/2j35TrrkRb7YiIiIiIrI1YyLhICIiIiIzYtE4jQC/xSFYvHgx3NzcBjy2bdtm6fCIiIiIiKwWVziGoLi4GN3d3QN+5unpeYOjISIiIiKyHUw4hsDHx8fSIRARERER3TCXL1/Gv/71L/T19Rm1//jtrkPBhIOIiIiIiAAAzc3NeOKJJ3DixAmjdoPBwH04iIiIiMhMWDRuNxITEyGRSFBeXo6pU6dCJBKNeEwmHFYmJgb4z76DZKe4VyQBgKz3W0uHQFZg+XIvS4dAFnbpEvDcc5aOguxJY2MjTp06hdmzZ4/amPaXthERERER0YDmzJmDf//736M6JhMOIiIiIiICAOzYsQMbNmxATU0Nvv32W2g0GqPDFCY9UjVz5kx8+OGH8PIyXuq9ePEiFixYgC+//NKkYIiIiIiIyHIWLVoEALj77ruN2m940XhLS8uAk2m1Wnz99demDElERERE1opF43ajurp61MccVsJx6NAh4e93330XMplMONfr9Th69Cj8/f1HLTgiIiIiIrpxFi5cOOpjDivhWLp0KQBAJBLhscceM/rM0dER/v7+yMvLG7XgiIiIiIjoxrp48SJefvllfPbZZwCAW2+9FU888YTRYsNwDGudqK+vD319ffDz8xN2Hrx6aLVaNDU14f777zcpECIiIiIisqyGhgYEBARAoVDgwoULuHDhAvLz8xEQEAClUmnSmCbVcHz11VcmTWYu0dHRCAkJwc6dOy0dChERERGRzVq/fj1iY2OxZ88eSCRXUoXe3l6sXLkS69atw/vvvz/sMYe8wrFr1y709PQIfw92WBOdToeMjAwEBwfD1dUV3t7ekMvlaGtrG7C/VqtFSEgIRCIRGhsbhz3f2bNnMX78eHh4eIwscCIiIiJrcbVo3B4PE7zwwgvw9/eHs7MzIiIiUF9fP2j/N954A7Nnz4azszOCg4PxzjvvGH1uMBjwu9/9DlOnTsW4ceOwaNEiNDc3mxTb9TQ0NCAjI0NINgBAIpFgw4YNaGhoMGnMIa9wKBQKJCQkwNnZGQqF4pr9RCIR1q5da1Iw5tDV1QWlUomsrCzMnz8f3333HdLS0hAbGzvgl7ZhwwZ4e3vj448/HvZcOp0O8fHxuOOOO3DixInRCJ+IiIiIbMiBAweQnp6OF198EREREdi5cydiYmLQ1NSESZMm9et/4sQJxMfHIycnB/fffz/27duHpUuXQqlUYu7cuQCA3//+99i1axdKSkowY8YMZGVlISYmBmfOnIGzs/Ooxu/u7o7W1tZ+O42fO3cO48ePN2lMkcFgMIxGcJYUHR0t/A9SWloKR0dHrF69GtnZ2RCJRP36f/jhhwgPD4dKpYKfn5/QXlFRgfT0dBw8eBC33norPvroI4SEhAw5joyMDLS1teHuu+/GunXrcPHixSFfq9FoIJPJUFWlhpub+5Cvo7Hnn/+0dARkDR6881tLh0BWoPmC1/U70Zh26ZIGCxbIoFar4e5+438fXP19or540SLzW5pGo4HMw2NY339ERAR++tOfYvfu3QCu1ED7+vpizZo1ePrpp/v1X758Ob7//nuUl5cLbZGRkQgJCcGLL74Ig8EAb29vPPXUU/j1r38NAFCr1Zg8eTJeffVV/OIXvxiFO/0/a9euxV/+8hfk5ubitttuAwB88MEH+M1vfoOHH37YpBKGMfNy4ZKSEkgkEtTX16OgoAD5+fkoLi4esK9arYZIJDJ67KmjowNJSUkoLS2Fi4vLsOevqqrCG2+8gRdeeMHUWyAiIiIiG3b58mWcOnVK2DwPABwcHLBo0SLU1tYOeE1tba1RfwCIiYkR+n/11Vdob2836iOTyRAREXHNMUciNzcXDz30EORyOfz9/eHv74/ExEQsW7YMO3bsMGlMk4rG9Xo9Xn31VRw9elR4W9UPVVVVmRTMSPj6+kKhUEAkEiEoKAinT5+GQqFAUlKSUb+enh5kZGQgPj5eyFQNBgMSExOxatUqhIWFoaWlZVhzf/vtt0hMTMRrr7025OxXq9VCq9UK56ZuFU9ERERE5vXj32lSqRRSqbRfv3//+9/Q6/WYPHmyUfvkyZPxj3/8Y8Cx29vbB+zf3t4ufH617Vp9RpOTkxMKCgqQk5ODL774AgAQEBBg0j/IX2VSwpGWloZXX30V9913H+bOnTvgY0s3WmRkpFEcUVFRyMvLg16vh1gsBnClxiIuLg4GgwFFRUVC38LCQnR2diIzM9OkuZOSkvDII4/gzjvvHPI1OTk52LJli0nzEREREd1IBohggOV/791oV+/Z19fXqP2ZZ57B5s2bLRDRjePi4oLg4OBRGcukhGP//v3405/+hCVLloxKEDfC1WRDpVKhqqrKaCWiqqoKtbW1/TLVsLAwJCQkoKSkZNCxq6qqcOjQIeTm5gK4smLS19cHiUSCl156CU888US/azIzM5Geni6cazSafv8xExEREZHlnTt3zui340CrGwAwYcIEiMVidHR0GLV3dHRgypQpA14zZcqUQftf/b8dHR2YOnWqUZ/h1BoP5qGHHsKrr74Kd3d3PPTQQ4P2LSsrG/b4JiUcTk5OmDVrlimXmk1dXZ3R+cmTJxEYGAixWCwkG83NzaiuroaXl3ER3q5du7B161bhvK2tDTExMThw4AAiIiKuO3dtbS30er1w/te//hU7duzAiRMn4OPjM+A111qKIyIiIiLr4u7uPqTH5p2cnPCTn/wER48exdKlSwFcKRo/evQoUlNTB7wmKioKR48exbp164S2yspKREVFAQBmzJiBKVOm4OjRo0KCodFoUFdXh9WrV4/ovq6SyWTCk0Lu7u6j/vSSSQnHU089hYKCAuzevdsqHqcCgNbWVqSnpyMlJQVKpRKFhYXIy8uDTqfDsmXLoFQqUV5eDr1eLzzv5unpCScnJ6M3VQGAm5sbgCvPq02bNu26c99yyy1G5w0NDXBwcBDenEVERERE9iE9PR2PPfYYwsLCEB4ejp07d+L777/H448/DgCQy+Xw8fFBTk4OgCulCgsXLkReXh7uu+8+7N+/Hw0NDXjppZcAXNlyYt26ddi6dSsCAwOF1+J6e3sLSc1I/eEPfxD+fvXVV0dlzB8yKeE4fvw4qqurUVFRgVtvvRWOjo5Gn5uy1DJScrkc3d3dCA8Ph1gsRlpaGpKTk6FSqXDo0CEA6LfsVF1djejo6BseKxERERGNTcuXL8c333yD3/3ud2hvb0dISAgOHz4sFH23trbC4QcbCt52223Yt28fNm3ahI0bNyIwMBBvvvmm0T9cb9iwAd9//z2Sk5Nx8eJF/OxnP8Phw4dHfQ8OAPiv//ovlJWV9dvEWqPRYOnSpSa9HMqkfTiuZmjX8sMsiYaG+3DQVdyHgwDuw0FXcB8OspZ9OC5csMz8lqbRaODpabnv3xIcHBzQ3t7eb5PCf/3rX/Dx8YFOpxv2mCatcDChICIiIiIaOz755BPh7zNnzhi9clev1+Pw4cPXrE2+nmElHDfddNOANRsymQw333wzfv3rX+Oee+4xKRBrtnjxYhw7dmzAzzZu3IiNGzfe4IiIiIiIiEZPSEgIRCIRRCIR/uu//qvf5+PGjUNhYaFJYw8r4bjWVuYXL17EqVOncP/99+PPf/4zHnjgAZOCsVbFxcXo7u4e8DNPT88bHA0RERER0ej66quvYDAYMHPmTNTX12PixInCZ05OTpg0aZKwt91wDSvheOyxxwb9PCQkBDk5OWMu4TB1+YiIiIhoLOjru3LYG3u65+nTpwO48hrf0WZSDce13H///Ub7WRARERERke05c+YMWltbcfnyZaP22NjYYY81qgmHVquFk5PTaA5JREREREQ3yJdffokHH3wQp0+fhkgkwtUX2l6t4/7hZtdDNaoJx8svvzxqW6zbq58sngR3K9lMkSzjp/a0fkvX1NGqtXQIZAUC5zhevxONaZrh715ANCJpaWmYMWMGjh49ihkzZqC+vh7ffvstnnrqKeTm5po05rASjvT09AHb1Wo1lEolPv/8c7z//vsmBUJERERERJZVW1uLqqoqTJgwAQ4ODnBwcMDPfvYz5OTkYO3atfjoo4+GPeawEo5rTeDu7o577rkHZWVlmDFjxrCDICIiIiLrxaJx+6HX6zF+/HgAwIQJE9DW1oagoCBMnz4dTU1NJo05rISjurrapEmIiIiIiMj6zZ07Fx9//DFmzJiBiIgI/P73v4eTkxNeeuklzJw506QxR7WGg4iIiIiIbNemTZvw/fffAwCys7Nx//3344477oCXlxcOHDhg0phMOIiIiIiICAAQExMj/D1r1iz84x//wIULF3DTTTcJb6oaLiYcRERERER0TZ6eniO6ngkHEREREQ2KReNj20MPPTTkvmVlZcMef0wkHNHR0QgJCcHOnTstHQoRERERkU2RyWRmHX9MJByD0el02LRpE9555x18+eWXkMlkWLRoEbZv3w5vb+9+/bVaLSIiIvDxxx/jo48+GtJGhi0tLQO+Dri2thaRkZGjcRtERERERGbxhz/8wazjO5h1dCvQ1dUFpVKJrKwsKJVKlJWVoampCbGxsQP237Bhw4CJyFC89957OH/+vHD85Cc/GUnoREREREQ2b8yscPT29iI1NRWlpaVwdHTE6tWrkZ2dDZlMhsrKSqO+u3fvRnh4OFpbW+Hn5ye0V1RU4MiRIzh48CAqKiqGHYOXlxemTJky4nshIiIiIrKEGTNmDPo2qi+//HLYY46ZhKOkpAQrVqxAfX09GhoakJycDD8/PyQlJfXrq1arIRKJ4OHhIbR1dHQgKSkJb775JlxcXEyKITY2Fj09Pbj55puxYcOGa66iEBEREdkSFo3bj3Xr1hmd63Q6fPTRRzh8+DB+85vfmDTmmEk4fH19oVAoIBKJEBQUhNOnT0OhUPRLOHp6epCRkYH4+Hi4u7sDAAwGAxITE7Fq1SqEhYWhpaVlWHO7ubkhLy8Pt99+OxwcHHDw4EEsXboUb7755jWTDq1WC61WK5xrNJrh3TARERER0ShLS0sbsP2FF15AQ0ODSWOOmRqOyMhIo+WfqKgoNDc3Q6/XC206nQ5xcXEwGAwoKioS2gsLC9HZ2YnMzEyT5p4wYQLS09MRERGBn/70p9i+fTseffRRPP/889e8JicnBzKZTDh8fX1NmpuIiIiIyNwWL16MgwcPmnTtmEk4rudqsqFSqVBZWSmsbgBAVVUVamtrIZVKIZFIMGvWLABAWFgYHnvsMZPmi4iIwNmzZ6/5eWZmJtRqtXCcO3fOpHmIiIiIiMztz3/+s8kbAI6ZR6rq6uqMzk+ePInAwECIxWIh2WhubkZ1dTW8vLyM+u7atQtbt24Vztva2hATE4MDBw4gIiLCpHgaGxsxderUa34ulUohlUpNGpuIiIiIyBxCQ0ONnhoyGAxob2/HN998g//5n/8xacwxk3C0trYiPT0dKSkpUCqVKCwsRF5eHnQ6HZYtWwalUony8nLo9Xq0t7cDuLJNu5OTk9GbqoArNRkAEBAQgGnTpl137pKSEjg5OSE0NBTAlR0YX3nlFRQXF4/yXRIRERHdeCwatx9Lly41OndwcMDEiRMRHR2N2bNnmzTmmEk45HI5uru7ER4eDrFYjLS0NCQnJ0OlUuHQoUMA0G8Tv+rqakRHR4/K/M8++yxUKhUkEglmz56NAwcOYNmyZaMyNhERERHRjfDMM8+M+pgig8FgGPVRadg0Gg1kMhnUUincB3n3MdkBe/znFOqno1V7/U405k2e5mjpEMjCNAYDZHo91Gq1Uf3pDZv/P79P/vlPy8xvaRqNBtOmySz2/d8ow3lbqinfw5hZ4SAiIiIiouHz8PAYdLO/H/rhG2CHignHECxevBjHjh0b8LONGzdi48aNNzgiIiIiIqLRUV1dLfzd0tKCp59+GomJiYiKigIA1NbWoqSkBDk5OSaNz4RjCIqLi9Hd3T3gZ6a+HoyIiIjIVrBofGxbuHCh8Hd2djby8/MRHx8vtMXGxiI4OBgvvfSSSVtGMOEYAh8fH0uHQERERERkdrW1tXjxxRf7tYeFhWHlypUmjWk3G/8REREREdHgfH19sWfPnn7txcXF8PX1NWlMrnAQEREREREAQKFQ4OGHH0ZFRYWwAXZ9fT2am5tx8OBBk8bkCgcREREREQEAlixZgubmZjzwwAO4cOECLly4gAceeACff/45lixZYtKYXOGwMl+e/BfGjx+773mm6/vkE0tHQNbgwQnDf+0gjT3lf9FZOgSysK4uDbBcZukwWDRuZ6ZNm4Zt27aN2nhMOIiIiIiISHDx4kW8/PLL+OyzzwAAt956K5544gnIZKYlv3ykioiIiIiIAAANDQ0ICAiAQqEQHqnKz89HQEAAlEqlSWNyhYOIiIiIiAAA69evR2xsLPbs2QOJ5Eqq0Nvbi5UrV2LdunV4//33hz0mEw4iIiIiIgJwZYXjh8kGAEgkEmzYsAFhYWEmjcmEg4iIiIgGZTDYZwG1wWDpCG48d3d3tLa2Yvbs2Ubt586dw/jx400akzUcREREREQEAFi+fDlWrFiBAwcO4Ny5czh37hz279+PlStXIj4+3qQxrX6FIzo6GiEhIdi5c6elQyEiIiIiGtNyc3MhEokgl8vR29sLAHB0dMTq1auxfft2k8a06RUOnU6HjIwMBAcHw9XVFd7e3pDL5Whraxuwv1arRUhICEQiERobG4c0R09PDxITExEcHAyJRIKlS5f263P+/Hk88sgjuPnmm+Hg4IB169aZflNERERERBbi5OSEgoICfPfdd2hsbERjYyMuXLgAhUIBqVRq0pg2nXB0dXVBqVQiKysLSqUSZWVlaGpqQmxs7ID9N2zYAG9v72HNodfrMW7cOKxduxaLFi0asI9Wq8XEiROxadMmzJ8/f9j3QURERERkTVxcXBAcHIzg4GC4uLiMaCyrf6QKuPIqrtTUVJSWlgpLOtnZ2ZDJZKisrDTqu3v3boSHh6O1tRV+fn5Ce0VFBY4cOYKDBw+ioqJiyHO7urqiqKgIAPDBBx/g4sWL/fr4+/ujoKAAAPDKK6+YcIdERERE1os7jY99TzzxxJD6mfJb1yYSjpKSEqxYsQL19fVoaGhAcnIy/Pz8kJSU1K+vWq2GSCSCh4eH0NbR0YGkpCS8+eabI87QiIiIiIjGmldffRXTp09HaGgoDKP8ei6bSDh8fX2hUCggEokQFBSE06dPQ6FQ9Es4enp6kJGRgfj4eLi7uwMADAYDEhMTsWrVKoSFhaGlpcUCd9CfVquFVqsVzjUajQWjISIiIiJ7tnr1arz++uv46quv8Pjjj+PRRx+Fp6fnqIxtEzUckZGREIlEwnlUVBSam5uh1+uFNp1Oh7i4OBgMBuERKAAoLCxEZ2cnMjMzb2jM15OTkwOZTCYcvr6+lg6JiIiIiOzUCy+8gPPnz2PDhg1466234Ovri7i4OLz77rsjXvGwiYTjeq4mGyqVCpWVlcLqBgBUVVWhtrYWUqkUEokEs2bNAgCEhYXhscces1TIyMzMhFqtFo5z585ZLBYiIiIiIqlUivj4eFRWVuLMmTO49dZb8atf/Qr+/v64dOmSyePaxCNVdXV1RucnT55EYGAgxGKxkGw0NzejuroaXl5eRn137dqFrVu3CudtbW2IiYnBgQMHEBERcUPiH4hUKjX51WJERERENxKLxu2Pg4MDRCIRDAaD0VNFprCJhKO1tRXp6elISUmBUqlEYWEh8vLyoNPpsGzZMiiVSpSXl0Ov16O9vR0A4OnpCScnJ6M3VQGAm5sbACAgIADTpk0b0vxnzpzB5cuXceHCBXR2dgp7eISEhAh9rrZdunQJ33zzDRobG+Hk5IQ5c+aM7OaJiIiIiG4ArVaLsrIyvPLKKzh+/Djuv/9+7N69G/feey8cHEx/MMomEg65XI7u7m6Eh4dDLBYjLS0NycnJUKlUOHToEADjH/8AUF1djejo6FGZf8mSJVCpVMJ5aGgoABg9z3a1DQBOnTqFffv2Yfr06VZTpE5EREREdC2/+tWvsH//fvj6+uKJJ57A66+/jgkTJozK2CLDaL/3ikyi0Wggk8nw0UdqjB/vfv0LaMz65BNLR0DW4MHYkS1f09hQXiG2dAhkYV1dGixfLoNarTaqUb1Rrv4+aW62z98nnZ0aBAZa7vu/kRwcHODn54fQ0FCjlzX9WFlZ2bDHtokVDiIiIiIiMh+5XD5oojESdp9wLF68GMeOHRvws40bN2Ljxo03OCIiIiIi68Ki8bHv1VdfNdvYdp9wFBcXo7u7e8DPRmuzEyIiIiIie2X3CYePj4+lQyAiIiIiGrPGxMZ/RERERERknZhwEBERERGR2dj9I1VERERENDgWjdNIMOGwMjP3b4O7VGrpMMiCAu65x9IhkDXoi7B0BGQF7veqt3QIZGEa5+8tHQLRiPGRKiIiIiIiMhsmHEREREREZDZMOIiIiIiIyGxYw0FEREREg2LROI0EVziIiIiIiMhsmHAQEREREZHZWH3CER0djXXr1lk6DCIiIiIiMoHVJxyD0el0yMjIQHBwMFxdXeHt7Q25XI62trYB+2u1WoSEhEAkEqGxsXFIc/T09CAxMRHBwcGQSCRYunRpvz7Hjx/H7bffDi8vL4wbNw6zZ8+GQqEYwZ0REREREY0NNl003tXVBaVSiaysLMyfPx/fffcd0tLSEBsbi4aGhn79N2zYAG9vb3z88cdDnkOv12PcuHFYu3YtDh48OGAfV1dXpKamYt68eXB1dcXx48eRkpICV1dXJCcnm3x/RERERNaAReM0EjaRcPT29iI1NRWlpaVwdHTE6tWrkZ2dDZlMhsrKSqO+u3fvRnh4OFpbW+Hn5ye0V1RU4MiRIzh48CAqKiqGPLerqyuKiooAAB988AEuXrzYr09oaChCQ0OFc39/f5SVleHYsWNMOIiIiIjIrtnEI1UlJSWQSCSor69HQUEB8vPzUVxcPGBftVoNkUgEDw8Poa2jowNJSUkoLS2Fi4uL2eP96KOPcOLECSxcuNDscxERERERWTObWOHw9fWFQqGASCRCUFAQTp8+DYVCgaSkJKN+PT09yMjIQHx8PNzd3QEABoMBiYmJWLVqFcLCwtDS0mK2OKdNm4ZvvvkGvb292Lx5M1auXHnNvlqtFlqtVjjXaDRmi4uIiIiIyFJsYoUjMjISIpFIOI+KikJzczP0er3QptPpEBcXB4PBIDwCBQCFhYXo7OxEZmam2eM8duwYGhoa8OKLL2Lnzp14/fXXr9k3JycHMplMOHx9fc0eHxERERHRjWYTKxzXczXZUKlUqKqqElY3AKCqqgq1tbWQSqVG14SFhSEhIQElJSWjFseMGTMAAMHBwejo6MDmzZsRHx8/YN/MzEykp6cL5xqNhkkHERERWSUWjdNI2ETCUVdXZ3R+8uRJBAYGQiwWC8lGc3Mzqqur4eXlZdR3165d2Lp1q3De1taGmJgYHDhwABEREWaLua+vz+iRqR+TSqX9kiAiIiIiorHGJhKO1tZWpKenIyUlBUqlEoWFhcjLy4NOp8OyZcugVCpRXl4OvV6P9vZ2AICnpyecnJyM3lQFAG5ubgCAgIAATJs2bUjznzlzBpcvX8aFCxfQ2dkp7OEREhICAHjhhRfg5+eH2bNnAwDef/995ObmYu3ataNw90REREREtssmEg65XI7u7m6Eh4dDLBYjLS0NycnJUKlUOHToEID/+/F/VXV1NaKjo0dl/iVLlkClUgnnV1+BazAYAFxZzcjMzMRXX30FiUSCgIAA7NixAykpKaMyPxERERGRrbL6hKOmpkb4+4fF4MCV/S6u/ugfKlOuud6brdasWYM1a9YMa0wiIiIiIntg9QkHEREREVkWi8ZpJGzitbjmtHjxYri5uQ14bNu2zdLhERERERHZNLtf4SguLkZ3d/eAn3l6et7gaIiIiIiIxha7Tzh8fHwsHQIRERER0Zhl949UERERERGR+dj9CgcRERERDc5gsM8C6mG+2JSugSscRERERERkNlzhsDbLlwP/2Q2d7JSTk6UjICvw3SVHS4dAVuCmKVMsHQJZWmenpSMgGjGucBARERERkdkw4SAiIiIiIrPhI1VERERENCjuNE4jwRUOIiIiIiIyGyYcRERERERkNkw4iIiIiIjIbKw+4YiOjsa6dessHQYREREREZnA6hOOweh0OmRkZCA4OBiurq7w9vaGXC5HW1vbgP21Wi1CQkIgEonQ2Ng4pDl6enqQmJiI4OBgSCQSLF26tF+fmpoaiESifkd7e/sI7o6IiIjIOlwtGrfHg0bOphOOrq4uKJVKZGVlQalUoqysDE1NTYiNjR2w/4YNG+Dt7T2sOfR6PcaNG4e1a9di0aJFg/ZtamrC+fPnhWPSpEnDmouIiIiIaKyxidfi9vb2IjU1FaWlpXB0dMTq1auRnZ0NmUyGyspKo767d+9GeHg4Wltb4efnJ7RXVFTgyJEjOHjwICoqKoY8t6urK4qKigAAH3zwAS5evHjNvpMmTYKHh8ew7o2IiIiIaCyziRWOkpISSCQS1NfXo6CgAPn5+SguLh6wr1qthkgkMvrh39HRgaSkJJSWlsLFxcVscYaEhGDq1Km455578MEHH5htHiIiIiIiW2ETKxy+vr5QKBQQiUQICgrC6dOnoVAokJSUZNSvp6cHGRkZiI+Ph7u7OwDAYDAgMTERq1atQlhYGFpaWkY9vqlTp+LFF19EWFgYtFotiouLER0djbq6OixYsGDAa7RaLbRarXCu0WhGPS4iIiIiIkuziYQjMjISIpFIOI+KikJeXh70ej3EYjGAKwXkcXFxMBgMwiNQAFBYWIjOzk5kZmaaLb6goCAEBQUJ57fddhu++OILKBQKlJaWDnhNTk4OtmzZYraYiIiIiEaLvRZQ2+M9m4NNPFJ1PVeTDZVKhcrKSmF1AwCqqqpQW1sLqVQKiUSCWbNmAQDCwsLw2GOPmS2m8PBwnD179pqfZ2ZmQq1WC8e5c+fMFgsRERERkaXYxApHXV2d0fnJkycRGBgIsVgsJBvNzc2orq6Gl5eXUd9du3Zh69atwnlbWxtiYmJw4MABREREmC3mxsZGTJ069ZqfS6VSSKVSs81PRERERGQNbCLhaG1tRXp6OlJSUqBUKlFYWIi8vDzodDosW7YMSqUS5eXl0Ov1wt4Xnp6ecHJyMnpTFQC4ubkBAAICAjBt2rQhzX/mzBlcvnwZFy5cQGdnp7CHR0hICABg586dmDFjBm699Vb09PSguLgYVVVVOHLkyOh8AURERERENsomEg65XI7u7m6Eh4dDLBYjLS0NycnJUKlUOHToEID/+/F/VXV1NaKjo0dl/iVLlkClUgnnoaGhAK4UpAPA5cuX8dRTT+Hrr7+Gi4sL5s2bh/feew933XXXqMxPRERERGSrRIarv5rJojQaDWQyGdRKJdz/swpDdsrJydIRkBX4zn26pUMgK3DTxa8sHQJZmKazE7L586FWq41qVG/Y/P/5fXLihBpubjd+fku7dEmD226TWez7HyvGRNE4ERERERFZJ7tPOBYvXgw3N7cBj23btlk6PCIiIiIim2YTNRzmVFxcjO7u7gE/8/T0vMHREBERERGNLXafcPj4+Fg6BCIiIiKiMcvuEw4iIiIiGhx3GqeRsPsaDiIiIiIiMh8mHEREREREZDZ8pMraVFcD48ZZOgqypPHjLR0BWYGblntbOgSyBu09lo6ALE2rtXQERCPGFQ4iIiIiIjIbrnAQERER0aBYNE4jwRUOIiIiIiIyGyYcRERERERkNkw4iIiIiIjIbJhwEBERERGR2Vh90Xh0dDRCQkKwc+dOS4dCREREZJdYNE4jYdMrHDqdDhkZGQgODoarqyu8vb0hl8vR1tY2YH+tVouQkBCIRCI0NjYOaY6amhr893//N6ZOnQpXV1eEhIRg79691+y/f/9+iEQiLF261IQ7IiIiIiIaW2w64ejq6oJSqURWVhaUSiXKysrQ1NSE2NjYAftv2LAB3t7D20zrxIkTmDdvHg4ePIhPPvkEjz/+OORyOcrLy/v1bWlpwa9//WvccccdJt0PEREREdFYY/WPVAFAb28vUlNTUVpaCkdHR6xevRrZ2dmQyWSorKw06rt7926Eh4ejtbUVfn5+QntFRQWOHDmCgwcPoqKiYshzb9y40eg8LS0NR44cQVlZGe6//36hXa/XIyEhAVu2bMGxY8dw8eJF026WiIiIiGgMsYkVjpKSEkgkEtTX16OgoAD5+fkoLi4esK9arYZIJIKHh4fQ1tHRgaSkJJSWlsLFxWXE8ajVanh6ehq1ZWdnY9KkSVixYsWIxyciIiIiGitsYoXD19cXCoUCIpEIQUFBOH36NBQKBZKSkoz69fT0ICMjA/Hx8XB3dwcAGAwGJCYmYtWqVQgLC0NLS8uIYvnTn/6EDz/8EP/7v/8rtB0/fhwvv/zykOtCgCv1JFqtVjjXaDQjiouIiIjIXFg0TiNhEysckZGREIlEwnlUVBSam5uh1+uFNp1Oh7i4OBgMBhQVFQnthYWF6OzsRGZm5ojjqK6uxuOPP449e/bg1ltvBQB0dnbil7/8Jfbs2YMJEyYMeaycnBzIZDLh8PX1HXF8RERERETWxiZWOK7narKhUqlQVVUlrG4AQFVVFWprayGVSo2uCQsLQ0JCAkpKSoY0x9/+9jc88MADUCgUkMvlQvsXX3yBlpYWPPDAA0Jb33/SYYlEgqamJgQEBPQbLzMzE+np6cK5RqNh0kFEREREY45NJBx1dXVG5ydPnkRgYCDEYrGQbDQ3N6O6uhpeXl5GfXft2oWtW7cK521tbYiJicGBAwcQERExpPlrampw//33Y8eOHUhOTjb6bPbs2Th9+rRR26ZNm9DZ2YmCgoJrJhFSqbRfEkRERERENNbYRMLR2tqK9PR0pKSkQKlUorCwEHl5edDpdFi2bBmUSiXKy8uh1+vR3t4OAPD09ISTk5PRm6oAwM3NDQAQEBCAadOmXXfu6upq3H///UhLS8PDDz8sjO/k5ARPT084Oztj7ty5RtdcLVj/cTsRERERkb2xiYRDLpeju7sb4eHhEIvFSEtLQ3JyMlQqFQ4dOgQACAkJMbqmuroa0dHRI567pKQEXV1dyMnJQU5OjtC+cOFC1NTUjHh8IiIiImtnMNhnAbXBYOkIxgaRwcCv0hpoNBrIZDKo8/LgPm6cpcMhSxo/3tIRkDVYvtzSEZA1OHvW0hGQhWkuXYIsPBxqtdqoRvWGzf+f3ydHj6rh6nrj57e077/X4O67ZRb7/scKm3hLFRERERER2Sa7TzgWL14MNze3AY9t27ZZOjwiIiIiIptmEzUc5lRcXIzu7u4BP/vxbuJERERERDQ8dp9w+Pj4WDoEIiIiIqvGncZpJOz+kSoiIiIiIjIfJhxERERERGQ2TDiIiIiIiMhs7L6Gw+o4O185yH5xHxYCgJ4eS0dA1sDDw9IRkKWJxZaOgGjEmHAQERER0aBYNE4jwUeqiIiIiIjIbJhwEBERERGR2TDhICIiIiIis2HCQUREREREZsOicSIiIiIaFIvGaSSsfoUjOjoa69ats3QYRERERERkAqtPOAaj0+mQkZGB4OBguLq6wtvbG3K5HG1tbQP212q1CAkJgUgkQmNj45DmqKmpwX//939j6tSpcHV1RUhICPbu3duv386dOxEUFIRx48bB19cX69evRw/fo09EREREds6mE46uri4olUpkZWVBqVSirKwMTU1NiI2NHbD/hg0b4O3tPaw5Tpw4gXnz5uHgwYP45JNP8Pjjj0Mul6O8vFzos2/fPjz99NN45pln8Nlnn+Hll1/GgQMHsHHjxhHdHxERERGRrbOJGo7e3l6kpqaitLQUjo6OWL16NbKzsyGTyVBZWWnUd/fu3QgPD0drayv8/PyE9oqKChw5cgQHDx5ERUXFkOf+cdKQlpaGI0eOoKysDPfffz+AK0nJ7bffjkceeQQA4O/vj/j4eNTV1Zl6y0REREREY4JNrHCUlJRAIpGgvr4eBQUFyM/PR3Fx8YB91Wo1RCIRPDw8hLaOjg4kJSWhtLQULi4uI45HrVbD09NTOL/ttttw6tQp1NfXAwC+/PJLvPPOO1iyZMmI5yIiIiKytKtF4/Z40MjZRMLh6+sLhUKBoKAgJCQkYM2aNVAoFP369fT0ICMjA/Hx8XB3dwcAGAwGJCYmYtWqVQgLCxtxLH/605/w4Ycf4vHHHxfaHnnkEWRnZ+NnP/sZHB0dERAQgOjo6EEfqdJqtdBoNEYHEREREdmHCxcuICEhAe7u7vDw8MCKFStw6dKlQfuvWbNGqBn28/PD2rVroVarjfqJRKJ+x/79+819O4OyiYQjMjISIpFIOI+KikJzczP0er3QptPpEBcXB4PBgKKiIqG9sLAQnZ2dyMzMHHEc1dXVePzxx7Fnzx7ceuutQntNTQ22bduG//mf/xFqSd5++208++yz1xwrJycHMplMOHx9fUccHxERERHZhoSEBPz9739HZWUlysvL8f777yM5Ofma/dva2tDW1obc3Fx8+umnePXVV3H48GGsWLGiX98//OEPOH/+vHAsXbrUjHdyfTZRw3E9V5MNlUqFqqoqYXUDAKqqqlBbWwupVGp0TVhYGBISElBSUjKkOf72t7/hgQcegEKhgFwuN/osKysLv/zlL7Fy5UoAQHBwML7//nskJyfjt7/9LRwc+ud1mZmZSE9PF841Gg2TDiIiIiI78Nlnn+Hw4cP48MMPhSdwCgsLsWTJEuTm5g74kqO5c+fi4MGDwnlAQACee+45PProo+jt7YVE8n8/6z08PDBlyhTz38gQ2cQKx4+Lr0+ePInAwECIxWIh2WhubsZ7770HLy8vo767du3Cxx9/jMbGRjQ2NuKdd94BABw4cADPPffckOavqanBfffdhx07dgyYeXZ1dfVLKsRiMYArj3QNRCqVwt3d3eggIiIiIuvz48fgtVrtiMarra2Fh4eH0eP+ixYtgoODw7BeOqRWq+Hu7m6UbADAk08+iQkTJiA8PByvvPLKNX+P3ig2scLR2tqK9PR0pKSkQKlUorCwEHl5edDpdFi2bBmUSiXKy8uh1+vR3t4OAPD09ISTk5PRm6oAwM3NDcCVrHDatGnXnbu6uhr3338/0tLS8PDDDwvjOzk5CYXjDzzwAPLz8xEaGoqIiAicPXsWWVlZeOCBB4TEg4iIiMhW2WsB9dV7/vFTKM888ww2b95s8rjt7e2YNGmSUZtEIoGnp6fwW/N6/v3vf+PZZ5/t94/h2dnZ+K//+i+4uLjgyJEj+NWvfoVLly5h7dq1Jsc7UjaRcMjlcnR3dyM8PBxisRhpaWlITk6GSqXCoUOHAAAhISFG11RXVyM6OnrEc5eUlKCrqws5OTnIyckR2hcuXIiamhoAwKZNmyASibBp0yZ8/fXXmDhxIh544IEhr6AQERERkfU6d+6c0dMoP35U/6qnn34aO3bsGHSszz77bMTxaDQa3HfffZgzZ06/xCcrK0v4OzQ0FN9//z2ef/55iyYcIoOl11gIwJX/cGQyGdQvvAD3ceMsHQ5ZEh+vIwD4+c8tHQFZg0HeWEP2QdPZCVlQkPDozA2f/z+/T956Sw1XV/v7/0/ff6/BAw/Ihvz9f/PNN/j2228H7TNz5ky89tpreOqpp/Ddd98J7b29vXB2dsYbb7yBBx988JrXd3Z2IiYmBi4uLigvL4ezs/Og87399tu4//770dPTc81EydxsYoWDiIiIiMjaTZw4ERMnTrxuv6ioKFy8eBGnTp3CT37yEwBXXnTU19eHiIiIa16n0WgQExMDqVSKQ4cOXTfZAIDGxkbcdNNNFks2ABspGjenxYsXw83NbcBj27Ztlg6PiIiIiMaYW265Bffeey+SkpJQX1+PDz74AKmpqfjFL34hvKHq66+/xuzZs4WNpTUaDX7+85/j+++/x8svvwyNRoP29na0t7cLW0W89dZbKC4uxqeffoqzZ8+iqKgI27Ztw5o1ayx2rwBXOFBcXIzu7u4BP/vhbuJERERE9srei8bNYe/evUhNTcXdd98NBwcHPPzww9i1a5fwuU6nQ1NTE7q6ugAASqVSeIPVrFmzjMb66quv4O/vD0dHR7zwwgtYv349DAYDZs2ahfz8fCQlJZnvRobA7hMOHx8fS4dARERERHbG09MT+/btu+bn/v7+Rq+zjY6Ovu7rbe+9917ce++9oxbjaLH7R6qIiIiIiMh8mHAQEREREZHZMOEgIiIiIiKzsfsaDqvj5we4ulo6CrKk8HBLR0DWYAivOiQ78J9iUbJjDtbxb8MsGqeRsI7/iomIiIiIaExiwkFERERERGbDhIOIiIiIiMyGCQcREREREZkNi8aJiIiIaFAsGqeR4AoHERERERGZDRMOIiIiIiIyG6tPOKKjo7Fu3TpLh0FERERERCaw+oRjMDqdDhkZGQgODoarqyu8vb0hl8vR1tY2YH+tVouQkBCIRCI0NjYOaY6mpibcddddmDx5MpydnTFz5kxs2rQJOp3OqN8bb7yB2bNnw9nZGcHBwXjnnXdGentERERERDbPphOOrq4uKJVKZGVlQalUoqysDE1NTYiNjR2w/4YNG+Dt7T2sORwdHSGXy3HkyBE0NTVh586d2LNnD5555hmhz4kTJxAfH48VK1bgo48+wtKlS7F06VJ8+umnI7o/IiIiImtgMPxf4bg9HQaDpb/5scEm3lLV29uL1NRUlJaWwtHREatXr0Z2djZkMhkqKyuN+u7evRvh4eFobW2Fn5+f0F5RUYEjR47g4MGDqKioGPLcM2fOxMyZM4Xz6dOno6amBseOHRPaCgoKcO+99+I3v/kNAODZZ59FZWUldu/ejRdffNHU2yYiIiIisnk2scJRUlICiUSC+vp6FBQUID8/H8XFxQP2VavVEIlE8PDwENo6OjqQlJSE0tJSuLi4jCiWs2fP4vDhw1i4cKHQVltbi0WLFhn1i4mJQW1t7TXH0Wq10Gg0RgcRERER0VhjEwmHr68vFAoFgoKCkJCQgDVr1kChUPTr19PTg4yMDMTHx8Pd3R0AYDAYkJiYiFWrViEsLMzkGG677TY4OzsjMDAQd9xxB7Kzs4XP2tvbMXnyZKP+kydPRnt7+zXHy8nJgUwmEw5fX1+TYyMiIiIislY2kXBERkZCJBIJ51FRUWhuboZerxfadDod4uLiYDAYUFRUJLQXFhais7MTmZmZI4rhwIEDUCqV2LdvH95++23k5uaOaLzMzEyo1WrhOHfu3IjGIyIiIiKyRjZRw3E9V5MNlUqFqqoqYXUDAKqqqlBbWwupVGp0TVhYGBISElBSUjKkOa6uQMyZMwd6vR7Jycl46qmnIBaLMWXKFHR0dBj17+jowJQpU645nlQq7RcTERERkTXiTuM0EjaxwlFXV2d0fvLkSQQGBkIsFgvJRnNzM9577z14eXkZ9d21axc+/vhjNDY2orGxUXhd7YEDB/Dcc8+ZFE9fXx90Oh36/vNfYVRUFI4ePWrUp7KyElFRUSaNT0REREQ0VtjECkdrayvS09ORkpICpVKJwsJC5OXlQafTYdmyZVAqlSgvL4derxfqJjw9PeHk5GT0pioAcHNzAwAEBARg2rRp15177969cHR0RHBwMKRSKRoaGpCZmYnly5fD0dERAJCWloaFCxciLy8P9913H/bv34+Ghga89NJLo/xNEBERERHZFptIOORyObq7uxEeHg6xWIy0tDQkJydDpVLh0KFDAICQkBCja6qrqxEdHT3iuSUSCXbs2IHPP/8cBoMB06dPR2pqKtavXy/0ue2227Bv3z5s2rQJGzduRGBgIN58803MnTt3xPMTEREREdkykcHALU2sgUajgUwmg/qtt+Du6mrpcMiSwsMtHQFZA2dnS0dA1uDf/7Z0BGRhms5OyAIDoVarjWpUb9j8//l98qc/qeHicuPnt7SuLg3i4mQW+/7HCptY4SAiIiIiy2HROI2ETRSNm9PixYvh5uY24LFt2zZLh0dEREREZNPsfoWjuLgY3d3dA37m6el5g6MhIiIiIhpb7D7h8PHxsXQIRERERERjlt0/UkVEREREROZj9yscRERERDQ4Fo3TSDDhsDYtLcC4cZaOgiypt9fSEZA1CAuzdARkDdraLB0BWdqlS5aOgGjE+EgVERERERGZDRMOIiIiIiIyGyYcRERERERkNqzhICIiIqJBsWicRoIrHEREREREZDZMOIiIiIiIyGyYcBARERERkdlYfcIRHR2NdevWWToMIiIiIiIygdUnHIPR6XTIyMhAcHAwXF1d4e3tDblcjrZrbJSk1WoREhICkUiExsbGIc3R1NSEu+66C5MnT4azszNmzpyJTZs2QafTCX327NmDO+64AzfddBNuuukmLFq0CPX19aNxi0REREQWd7Vo3B4PGjmbTji6urqgVCqRlZUFpVKJsrIyNDU1ITY2dsD+GzZsgLe397DmcHR0hFwux5EjR9DU1ISdO3diz549eOaZZ4Q+NTU1iI+PR3V1NWpra+Hr64uf//zn+Prrr0d0f0REREREts4mXovb29uL1NRUlJaWwtHREatXr0Z2djZkMhkqKyuN+u7evRvh4eFobW2Fn5+f0F5RUYEjR47g4MGDqKioGPLcM2fOxMyZM4Xz6dOno6amBseOHRPa9u7da3RNcXExDh48iKNHj0Iulw/3domIiIiIxgybWOEoKSmBRCJBfX09CgoKkJ+fj+Li4gH7qtVqiEQieHh4CG0dHR1ISkpCaWkpXFxcRhTL2bNncfjwYSxcuPCafbq6uqDT6eDp6XnNPlqtFhqNxuggIiIiIhprbCLh8PX1hUKhQFBQEBISErBmzRooFIp+/Xp6epCRkYH4+Hi4u7sDAAwGAxITE7Fq1SqEhYWZHMNtt90GZ2dnBAYG4o477kB2dvY1+2ZkZMDb2xuLFi26Zp+cnBzIZDLh8PX1NTk2IiIiIiJrZRMJR2RkJEQikXAeFRWF5uZm6PV6oU2n0yEuLg4GgwFFRUVCe2FhITo7O5GZmTmiGA4cOAClUol9+/bh7bffRm5u7oD9tm/fjv379+Mvf/kLnJ2drzleZmYm1Gq1cJw7d25E8RERERGZi6ULt1k0bttsoobjeq4mGyqVClVVVcLqBgBUVVWhtrYWUqnU6JqwsDAkJCSgpKRkSHNcXYGYM2cO9Ho9kpOT8dRTT0EsFgt9cnNzsX37drz33nuYN2/eoONJpdJ+MRERERERjTU2kXDU1dUZnZ88eRKBgYEQi8VCstHc3Izq6mp4eXkZ9d21axe2bt0qnLe1tSEmJgYHDhxARESESfH09fVBp9Ohr69PSDh+//vf47nnnsO77747oke3iIiIiIjGEptIOFpbW5Geno6UlBQolUoUFhYiLy8POp0Oy5Ytg1KpRHl5OfR6Pdrb2wEAnp6ecHJyMnpTFQC4ubkBAAICAjBt2rTrzr137144OjoiODgYUqkUDQ0NyMzMxPLly+Ho6AgA2LFjB373u99h37598Pf3F2Jwc3MT5iMiIiIiskc2kXDI5XJ0d3cjPDwcYrEYaWlpSE5OhkqlwqFDhwAAISEhRtdUV1cjOjp6xHNLJBLs2LEDn3/+OQwGA6ZPn47U1FSsX79e6FNUVITLly9j2bJlRtc+88wz2Lx584hjICIiIiKyVVafcNTU1Ah//7AYHAD8/f1hMBiGNd5wr1m+fDmWL18+aJ+WlpZhxUBERERkS+y1gNoe79kcbOItVUREREREZJvsPuFYvHixUGvx42Pbtm2WDo+IiIiIyKZZ/SNV5lZcXIzu7u4BPxtsp3AiIiIiIro+u084fHx8LB0CEREREdGYZfcJBxERERENjkXjNBJ2X8NBRERERETmwxUOa6NWA1qtpaMgS5o1y9IRkDVwdrZ0BERERKOCKxxERERERGQ2TDiIiIiIiMhs+EgVEREREQ2KReM0ElzhICIiIiIis2HCQUREREREZsOEg4iIiIiIzIYJBxERERERmY3VF41HR0cjJCQEO3futHQoRERERHbJYLDPAmqDwdIRjA02vcKh0+mQkZGB4OBguLq6wtvbG3K5HG1tbQP212q1CAkJgUgkQmNj45DmaGpqwl133YXJkyfD2dkZM2fOxKZNm6DT6Qbsv3//fohEIixdutTEuyIiIiIiGjusfoVjMF1dXVAqlcjKysL8+fPx3XffIS0tDbGxsWhoaOjXf8OGDfD29sbHH3885DkcHR0hl8uxYMECeHh44OOPP0ZSUhL6+vqwbds2o74tLS349a9/jTvuuGPE90ZERERENBbYRMLR29uL1NRUlJaWwtHREatXr0Z2djZkMhkqKyuN+u7evRvh4eFobW2Fn5+f0F5RUYEjR47g4MGDqKioGPLcM2fOxMyZM4Xz6dOno6amBseOHTPqp9frkZCQgC1btuDYsWO4ePGiaTdLRERERDSG2MQjVSUlJZBIJKivr0dBQQHy8/NRXFw8YF+1Wg2RSAQPDw+hraOjA0lJSSgtLYWLi8uIYjl79iwOHz6MhQsXGrVnZ2dj0qRJWLFixZDG0Wq10Gg0RgcRERER0VhjEyscvr6+UCgUEIlECAoKwunTp6FQKJCUlGTUr6enBxkZGYiPj4e7uzsAwGAwIDExEatWrUJYWBhaWlpMiuG2226DUqmEVqtFcnIysrOzhc+OHz+Ol19+ech1IQCQk5ODLVu2mBQLERER0Y3EncZpJGxihSMyMhIikUg4j4qKQnNzM/R6vdCm0+kQFxcHg8GAoqIiob2wsBCdnZ3IzMwcUQwHDhyAUqnEvn378PbbbyM3NxcA0NnZiV/+8pfYs2cPJkyYMOTxMjMzoVarhePcuXMjio+IiIiIyBrZxArH9VxNNlQqFaqqqoTVDQCoqqpCbW0tpFKp0TVhYWFISEhASUnJkObw9fUFAMyZMwd6vR7Jycl46qmn8MUXX6ClpQUPPPCA0LfvP+mwRCJBU1MTAgIC+o0nlUr7xURERERENNbYRMJRV1dndH7y5EkEBgZCLBYLyUZzczOqq6vh5eVl1HfXrl3YunWrcN7W1oaYmBgcOHAAERERJsXT19cHnU6Hvr4+zJ49G6dPnzb6fNOmTejs7ERBQYGQqBARERER2SObSDhaW1uRnp6OlJQUKJVKFBYWIi8vDzqdDsuWLYNSqUR5eTn0ej3a29sBAJ6ennBycjJ6UxUAuLm5AQACAgIwbdq06869d+9eODo6Ijg4GFKpFA0NDcjMzMTy5cvh6OgIR0dHzJ071+iaqwXrP24nIiIiIrI3NpFwyOVydHd3Izw8HGKxGGlpaUhOToZKpcKhQ4cAACEhIUbXVFdXIzo6esRzSyQS7NixA59//jkMBgOmT5+O1NRUrF+/fsRjExEREdkCFo3TSFh9wlFTUyP8/cNicADw9/eHYZh7zg/3muXLl2P58uXDmuPVV18dVn8iIiIiorHKJt5SRUREREREtsnuE47FixfDzc1twGPbtm2WDo+IiIiIyKZZ/SNV5lZcXIzu7u4BP/P09LzB0RARERERjS12n3D4+PhYOgQiIiIiq8aicRoJu3+kioiIiIiIzIcJBxERERERmY3dP1JlbbpXroGju7ulwyALGifRWToEsgZcxycAmDfP0hGQpWk0lo6AaMS4wkFERERERGbDFQ4iIiIiGhSLxmkkuMJBRERERERmw4SDiIiIiIjMhgkHERERERGZDRMOIiIiIiIyGxaNExEREdGgWDROI2H1KxzR0dFYt26dpcMgIiIiIiITWH3CMRidToeMjAwEBwfD1dUV3t7ekMvlaGtrG7C/VqtFSEgIRCIRGhsbhz3f2bNnMX78eHh4eBi1//3vf8fDDz8Mf39/iEQi7Ny5c/g3Q0REREQ0Btl0wtHV1QWlUomsrCwolUqUlZWhqakJsbGxA/bfsGEDvL29TZpLp9MhPj4ed9xxx4BxzJw5E9u3b8eUKVNMGp+IiIiIaCyyiYSjt7cXqampkMlkmDBhArKysmAwGCCTyVBZWYm4uDgEBQUhMjISu3fvxqlTp9Da2mo0RkVFBY4cOYLc3FyTYti0aRNmz56NuLi4fp/99Kc/xfPPP49f/OIXkEqlJo1PRERERDQW2UTReElJCVasWIH6+no0NDQgOTkZfn5+SEpK6tdXrVZDJBIZPfbU0dGBpKQkvPnmm3BxcRn2/FVVVXjjjTfQ2NiIsrKykdyKQKvVQqvVCucajWZUxiUiIiIabSwap5GwiRUOX19fKBQKBAUFISEhAWvWrIFCoejXr6enBxkZGYiPj4e7uzsAwGAwIDExEatWrUJYWNiw5/7222+RmJiIV199VRhzNOTk5EAmkwmHr6/vqI1NRERERGQtbCLhiIyMhEgkEs6joqLQ3NwMvV4vtOl0OsTFxcFgMKCoqEhoLywsRGdnJzIzM02aOykpCY888gjuvPNO029gAJmZmVCr1cJx7ty5UR2fiIiIiMga2ETCcT1Xkw2VSoXKykqjlYiqqirU1tZCKpVCIpFg1qxZAICwsDA89thj1x27qqoKubm5kEgkkEgkWLFiBdRqNSQSCV555RWTY5ZKpXB3dzc6iIiIiIjGGpuo4airqzM6P3nyJAIDAyEWi4Vko7m5GdXV1fDy8jLqu2vXLmzdulU4b2trQ0xMDA4cOICIiIjrzl1bW2u0kvLXv/4VO3bswIkTJ+Dj4zPCOyMiIiIiGttsIuFobW1Feno6UlJSoFQqUVhYiLy8POh0OixbtgxKpRLl5eXQ6/Vob28HAHh6esLJyQl+fn5GY7m5uQEAAgICMG3atOvOfcsttxidNzQ0wMHBAXPnzhXaLl++jDNnzgh/f/3112hsbISbm5uwokJERERkq1g0TiNhEwmHXC5Hd3c3wsPDIRaLkZaWhuTkZKhUKhw6dAgAEBISYnRNdXU1oqOjb0h8bW1tCA0NFc5zc3ORm5uLhQsXoqam5obEQERERERkjUQGg8Fg6SDoymtxZTIZ2tvVrOewc+MkOkuHQNaA/6xGACCxiX8XJDPSaDSQeXpCrbbM74Orv0+ef16NcePs7/dJd7cGv/mNzGLf/1gxJorGiYiIiIjIOtl9wrF48WK4ubkNeGzbts3S4RERERER2TS7X6stLi5Gd3f3gJ95enre4GiIiIiIrA+Lxmkk7D7h4KttiYiIiIjMx+4fqSIiIiIiIvNhwkFERERERGZj949UWZtnngGkUktHQZbk4OBo6RDICrz4oqUjIGvAt3BSX5/Y0iEQjRgTDiIiIiIalMFgnwXU3K1udPCRKiIiIiIiMhsmHEREREREZDZMOIiIiIiIyGyYcBARERERkdmwaJyIiIiIBsWdxmkkuMJBRERERERmY/UJR3R0NNatW2fpMIiIiIiIyARWn3AMRqfTISMjA8HBwXB1dYW3tzfkcjna2tqM+vn7+0MkEhkd27dvH9IcTU1NuOuuuzB58mQ4Oztj5syZ2LRpE3Q6nVG/ixcv4sknn8TUqVMhlUpx880345133hm1eyUiIiIiskU2XcPR1dUFpVKJrKwszJ8/H9999x3S0tIQGxuLhoYGo77Z2dlISkoSzsePHz+kORwdHSGXy7FgwQJ4eHjg448/RlJSEvr6+rBt2zYAwOXLl3HPPfdg0qRJ+POf/wwfHx+oVCp4eHiM2r0SEREREdkim0g4ent7kZqaitLSUjg6OmL16tXIzs6GTCZDZWWlUd/du3cjPDwcra2t8PPzE9rHjx+PKVOmDHvumTNnYubMmcL59OnTUVNTg2PHjgltr7zyCi5cuIATJ07A0dERwJVVFSIiIqKxgEXjNBI28UhVSUkJJBIJ6uvrUVBQgPz8fBQXFw/YV61WQyQS9Vtd2L59O7y8vBAaGornn38evb29JsVy9uxZHD58GAsXLhTaDh06hKioKDz55JOYPHky5s6di23btkGv119zHK1WC41GY3QQEREREY01NrHC4evrC4VCAZFIhKCgIJw+fRoKhcLoESkA6OnpQUZGBuLj4+Hu7i60r127FgsWLICnpydOnDiBzMxMnD9/Hvn5+UOO4bbbboNSqYRWq0VycjKys7OFz7788ktUVVUhISEB77zzDs6ePYtf/epX0Ol0eOaZZwYcLycnB1u2bBnmN0FEREREZFtsYoUjMjISIpFIOI+KikJzc7PRCoJOp0NcXBwMBgOKioqMrk9PT0d0dDTmzZuHVatWIS8vD4WFhdBqtUOO4cCBA1Aqldi3bx/efvtt5ObmCp/19fVh0qRJeOmll/CTn/wEy5cvx29/+1u8+OKL1xwvMzMTarVaOM6dOzfkWIiIiIiIbIVNrHBcz9VkQ6VSoaqqymh1YyARERHo7e1FS0sLgoKChjSHr68vAGDOnDnQ6/VITk7GU089BbFYjKlTp8LR0RFisVjof8stt6C9vR2XL1+Gk5NTv/GkUimkUukw7pKIiIiIyPbYRMJRV1dndH7y5EkEBgZCLBYLyUZzczOqq6vh5eV13fEaGxvh4OCASZMmmRRPX18fdDod+vr6IBaLcfvtt2Pfvn3o6+uDg8OVRaPPP/8cU6dOHTDZICIiIrIlLBqnkbCJhKO1tRXp6elISUmBUqlEYWEh8vLyoNPpsGzZMiiVSpSXl0Ov16O9vR0A4OnpCScnJ9TW1qKurg533XUXxo8fj9raWqxfvx6PPvoobrrppuvOvXfvXjg6OiI4OBhSqRQNDQ3IzMzE8uXLhTdSrV69Grt370ZaWhrWrFmD5uZmbNu2DWvXrjXr90JEREREZO1sIuGQy+Xo7u5GeHg4xGIx0tLSkJycDJVKhUOHDgEAQkJCjK6prq5GdHQ0pFIp9u/fj82bN0Or1WLGjBlYv3490tPThzS3RCLBjh078Pnnn8NgMGD69OlITU3F+vXrhT6+vr549913sX79esybNw8+Pj5IS0tDRkbGqH0HRERERES2SGQwGAyWDoIAjUYDmUyGlBQ1pNLBa1BobHOwiVc5kLkN8s4JsiPXKUkkO9DXp8G//y2DWq2+bo2qOVz9fZKdrYazs/39B9nTo8Hvfme573+s4E8bIiIiIiIyG7tPOBYvXgw3N7cBj23btlk6PCIiIiKLu1o0bo8HjZxN1HCYU3FxMbq7uwf8zNPT8wZHQ0REREQ0tth9wuHj42PpEIiIiIiIxiy7f6SKiIiIiIjMhwkHERERERGZjd0/UmVt/vd/LR0BWZqLi6UjIGvQ02PpCMgaSPj/pe2etWxeYK8F1PZ4z+bAFQ4iIiIiIjIbJhxERERERGQ2TDiIiIiIiMhsmHAQEREREZHZsByNiIiIiAbFonEaCa5wEBERERGR2TDhICIiIiIis7H6hCM6Ohrr1q2zdBhERERERGQCq084BqPT6ZCRkYHg4GC4urrC29sbcrkcbW1tRv38/f0hEomMju3btw9pjqamJtx1112YPHkynJ2dMXPmTGzatAk6nc4ojuzsbAQEBMDZ2Rnz58/H4cOHR/VeiYiIiIhskU0XjXd1dUGpVCIrKwvz58/Hd999h7S0NMTGxqKhocGob3Z2NpKSkoTz8ePHD2kOR0dHyOVyLFiwAB4eHvj444+RlJSEvr4+bNu2DQCwadMmvPbaa9izZw9mz56Nd999Fw8++CBOnDiB0NDQ0bthIiIiIgtg0TiNhE0kHL29vUhNTUVpaSkcHR2xevVqZGdnQyaTobKy0qjv7t27ER4ejtbWVvj5+Qnt48ePx5QpU4Y998yZMzFz5kzhfPr06aipqcGxY8eEttLSUvz2t7/FkiVLAACrV6/Ge++9h7y8PLz22mvDnpOIiIiIaKywiUeqSkpKIJFIUF9fj4KCAuTn56O4uHjAvmq1GiKRCB4eHkbt27dvh5eXF0JDQ/H888+jt7fXpFjOnj2Lw4cPY+HChUKbVquFs7OzUb9x48bh+PHj1xxHq9VCo9EYHUREREREY41NrHD4+vpCoVBAJBIhKCgIp0+fhkKhMHpECgB6enqQkZGB+Ph4uLu7C+1r167FggUL4OnpiRMnTiAzMxPnz59Hfn7+kGO47bbboFQqodVqkZycjOzsbOGzmJgY5Ofn484770RAQACOHj2KsrIy6PX6a46Xk5ODLVu2DONbICIiIiKyPTaxwhEZGQmRSCScR0VFobm52egHvU6nQ1xcHAwGA4qKioyuT09PR3R0NObNm4dVq1YhLy8PhYWF0Gq1Q47hwIEDUCqV2LdvH95++23k5uYKnxUUFCAwMBCzZ8+Gk5MTUlNT8fjjj8PB4dpfb2ZmJtRqtXCcO3duyLEQEREREdkKm0g4rudqsqFSqVBZWWm0ujGQiIgI9Pb2oqWlZchz+Pr6Ys6cOYiPj8f27duxefNmIeGZOHEi3nzzTXz//fdQqVT4xz/+ATc3N6Pajx+TSqVwd3c3OoiIiIis0dWicXs8zOXChQtISEiAu7s7PDw8sGLFCly6dGnQa6Kjo/u9eXXVqlVGfVpbW3HffffBxcUFkyZNwm9+8xuTSwlGi008UlVXV2d0fvLkSQQGBkIsFgvJRnNzM6qrq+Hl5XXd8RobG+Hg4IBJkyaZFE9fXx90Oh36+vogFouFdmdnZ/j4+ECn0+HgwYOIi4szaXwiIiIiGtsSEhJw/vx5VFZWQqfT4fHHH0dycjL27ds36HVJSUlGj/a7uLgIf+v1etx3332YMmUKTpw4gfPnz0Mul8PR0VF4u6ol2ETC0draivT0dKSkpECpVKKwsBB5eXnQ6XRYtmwZlEolysvLodfr0d7eDgDw9PSEk5MTamtrUVdXh7vuugvjx49HbW0t1q9fj0cffRQ33XTTdefeu3cvHB0dERwcDKlUioaGBmRmZmL58uVwdHQEcCUh+vrrrxESEoKvv/4amzdvRl9fHzZs2GDW74WIiIiIbM9nn32Gw4cP48MPP0RYWBgAoLCwEEuWLEFubi68vb2vea2Li8s137x65MgRnDlzBu+99x4mT56MkJAQPPvss8jIyMDmzZvh5ORklvu5Hpt4pEoul6O7uxvh4eF48sknkZaWhuTkZHz99dc4dOgQ/vnPfyIkJARTp04VjhMnTgC48ujS/v37sXDhQtx666147rnnsH79erz00ktDmlsikWDHjh0IDw/HvHnzsGXLFqSmphq9JaunpwebNm3CnDlz8OCDD8LHxwfHjx/v96YsIiIiIqLa2lp4eHgIyQYALFq0CA4ODv2e7PmxvXv3YsKECZg7dy4yMzPR1dVlNG5wcDAmT54stMXExECj0eDvf//76N/IEFn9CkdNTY3w94+Lwf39/WEwGAa9fsGCBTh58qTJ8y9fvhzLly8ftM/ChQtx5swZk+cgIiIiIuv14+0LpFIppFKpyeO1t7f3e7RfIpHA09NTeFpnII888gimT58Ob29vfPLJJ8jIyEBTUxPKysqEcX+YbAAQzgcb19ysPuEgIiIiIssyGOxz1+2r/67t6+tr1P7MM89g8+bN/fo//fTT2LFjx6BjfvbZZybHk5ycLPwdHByMqVOn4u6778YXX3yBgIAAk8c1N7tPOBYvXmy0a/gPbdy4ERs3brzBERERERGRNTl37pzRG0Wvtbrx1FNPITExcdCxZs6ciSlTpuBf//qXUXtvby8uXLhwzfqMgURERAC4sjF1QEAApkyZgvr6eqM+HR0dADCscUeb3SccxcXF6O7uHvAzT0/PGxwNEREREVmboW5hMHHiREycOPG6/aKionDx4kWcOnUKP/nJTwAAVVVV6OvrE5KIoWhsbAQATJ06VRj3ueeew7/+9S/hka2rW0bMmTNnyOOONrtPOHx8fCwdAhERERHZkVtuuQX33nsvkpKS8OKLL0Kn0yE1NRW/+MUvhDdUff3117j77rvxxz/+EeHh4fjiiy+wb98+LFmyBF5eXvjkk0+wfv163HnnnZg3bx4A4Oc//znmzJmDX/7yl/j973+P9vZ2bNq0CU8++eSIak5GyibeUkVERERENJbs3bsXs2fPxt13340lS5bgZz/7mdFbVHU6HZqamoS3UDk5OeG9997Dz3/+c8yePRtPPfUUHn74Ybz11lvCNWKxGOXl5RCLxYiKisKjjz4KuVxutG+HJdj9CgcRERERDc7cu25bK3Pes6en56Cb/P34bay+vr7429/+dt1xp0+fjnfeeWdUYhwtTDiszIMPAv/ZT5Ds1B13WDoCsgYffGDpCMgafPmlpSMgS9PrgVOnLB0F0cjwkSoiIiIiIjIbJhxERERERGQ2TDiIiIiIiMhsWMNBRERERINi0TiNBFc4iIiIiIjIbJhwEBERERGR2TDhICIiIiIis7H6hCM6Ohrr1q2zdBhERERERGQCq084BqPT6ZCRkYHg4GC4urrC29sbcrkcbW1tRv38/f0hEomMju3btw97vrNnz2L8+PHw8PDo99kbb7yB2bNnw9nZGcHBwVa3wyMRERGRqa4WjdvjQSNn0wlHV1cXlEolsrKyoFQqUVZWhqamJsTGxvbrm52djfPnzwvHmjVrhjWXTqdDfHw87hhgG+gTJ04gPj4eK1aswEcffYSlS5di6dKl+PTTT02+NyIiIiKiscAmEo7e3l6kpqZCJpNhwoQJyMrKgsFggEwmQ2VlJeLi4hAUFITIyEjs3r0bp06dQmtrq9EY48ePx5QpU4TD1dV1WDFs2rQJs2fPRlxcXL/PCgoKcO+99+I3v/kNbrnlFjz77LNYsGABdu/ePaL7JiIiIiKydTaRcJSUlEAikaC+vh4FBQXIz89HcXHxgH3VajVEIlG/x562b98OLy8vhIaG4vnnn0dvb++Q56+qqsIbb7yBF154YcDPa2trsWjRIqO2mJgY1NbWXnNMrVYLjUZjdBARERERjTU2sfGfr68vFAoFRCIRgoKCcPr0aSgUCiQlJRn16+npQUZGBuLj4+Hu7i60r127FgsWLICnpydOnDiBzMxMnD9/Hvn5+ded+9tvv0ViYiJee+01ozF/qL29HZMnTzZqmzx5Mtrb2685bk5ODrZs2XLd+YmIiIiIbJlNJByRkZEQiUTCeVRUFPLy8qDX6yEWiwFcqbGIi4uDwWBAUVGR0fXp6enC3/PmzYOTkxNSUlKQk5MDqVQ66NxJSUl45JFHcOedd47iHQGZmZlGcWk0Gvj6+o7qHERERESjwV4LqO3xns3BJh6pup6ryYZKpUJlZeU1VyKuioiIQG9vL1paWq47dlVVFXJzcyGRSCCRSLBixQqo1WpIJBK88sorAIApU6ago6PD6LqOjg5MmTLlmuNKpVK4u7sbHUREREREY41NrHDU1dUZnZ88eRKBgYEQi8VCstHc3Izq6mp4eXldd7zGxkY4ODhg0qRJ1+1bW1sLvV4vnP/1r3/Fjh07cOLECfj4+AC4suJy9OhRo/1CKisrERUVNcQ7JCIiIiIam2wi4WhtbUV6ejpSUlKgVCpRWFiIvLw86HQ6LFu2DEqlEuXl5dDr9ULdhKenJ5ycnFBbW4u6ujrcddddGD9+PGpra7F+/Xo8+uijuOmmm6479y233GJ03tDQAAcHB8ydO1doS0tLw8KFC5GXl4f77rsP+/fvR0NDA1566aXR/SKIiIiIiGyMTSQccrkc3d3dCA8Ph1gsRlpaGpKTk6FSqXDo0CEAQEhIiNE11dXViI6OhlQqxf79+7F582ZotVrMmDED69evN6qfGKnbbrsN+/btw6ZNm7Bx40YEBgbizTffNEpKiIiIiIjskchgMBgsHQRdKRqXyWR48EE1HB1Zz2HPBthbkuzQBx9YOgKyBl9+aekIyNL0eg1OnZJBrVZbpN7z6u+TNWvUkErt7/eJVqtBYaHlvv+xYkwUjRMRERERkXWy+4Rj8eLFcHNzG/DYtm2bpcMjIiIiIrJpNlHDYU7FxcXo7u4e8DNPT88bHA0RERER0dhi9wnH1VfbEhERERHR6LP7hIOIiIiIBsedxmkk7L6Gg4iIiIiIzIcrHFZmzhzA2dnSUZAlVVZaOgKyBl1dlo6ArMHNN1s6ArK0y5eBU6csHQXRyHCFg4iIiIiIzIYJBxERERERmQ0fqSIiIiKiQbFonEaCKxxERERERGQ2TDiIiIiIiMhsmHAQEREREZHZMOEgIiIiIiKzsfqi8ejoaISEhGDnzp2WDoWIiIjILrFonEbCplc4dDodMjIyEBwcDFdXV3h7e0Mul6Otrc2on7+/P0QikdGxffv2Ic3R0tLS71qRSISTJ08Kff7+97/j4YcfFuZhckREREREdIXVr3AMpqurC0qlEllZWZg/fz6+++47pKWlITY2Fg0NDUZ9s7OzkZSUJJyPHz9+WHO99957uPXWW4VzLy8vozhmzpyJ//f//h/Wr19v4t0QEREREY09NpFw9Pb2IjU1FaWlpXB0dMTq1auRnZ0NmUyGyspKo767d+9GeHg4Wltb4efnJ7SPHz8eU6ZMMTkGLy+va17/05/+FD/96U8BAE8//bTJcxARERERjTU28UhVSUkJJBIJ6uvrUVBQgPz8fBQXFw/YV61WQyQSwcPDw6h9+/bt8PLyQmhoKJ5//nn09vYOK4bY2FhMmjQJP/vZz3Do0CFTb0Wg1Wqh0WiMDiIiIiJrZDD8Xx2HPR0Gg6W/+bHBJlY4fH19oVAoIBKJEBQUhNOnT0OhUBg9IgUAPT09yMjIQHx8PNzd3YX2tWvXYsGCBfD09MSJEyeQmZmJ8+fPIz8//7pzu7m5IS8vD7fffjscHBxw8OBBLF26FG+++SZiY2NNvqecnBxs2bLF5OuJiIiIiGyBTSQckZGREIlEwnlUVBTy8vKg1+shFosBXCkgj4uLg8FgQFFRkdH16enpwt/z5s2Dk5MTUlJSkJOTA6lUOujcEyZMMLr+pz/9Kdra2vD888+PKOHIzMw0Glej0cDX19fk8YiIiIiIrJFNPFJ1PVeTDZVKhcrKSqPVjYFERESgt7cXLS0tJs0XERGBs2fPmnTtVVKpFO7u7kYHEREREdFYYxMrHHV1dUbnJ0+eRGBgIMRisZBsNDc3o7q62ujtUdfS2NgIBwcHTJo0yaR4GhsbMXXqVJOuJSIiIiKyJzaRcLS2tiI9PR0pKSlQKpUoLCxEXl4edDodli1bBqVSifLycuj1erS3twMAPD094eTkhNraWtTV1eGuu+7C+PHjUVtbi/Xr1+PRRx/FTTfddN25S0pK4OTkhNDQUABAWVkZXnnlFaOi9cuXL+PMmTPC319//TUaGxvh5uaGWbNmmeEbISIiIrpxuPEfjYRNJBxyuRzd3d0IDw+HWCxGWloakpOToVKphDdGhYSEGF1TXV2N6OhoSKVS7N+/H5s3b4ZWq8WMGTOwfv16o/qJ63n22WehUqkgkUgwe/ZsHDhwAMuWLRM+b2trExISAMjNzUVubi4WLlyImpqaEd07EREREZEtExkMfOGXNdBoNJDJZPjtb9VwdmY9hz378ENLR0DWoKvL0hGQNRjB9lE0Rly+rMGf/iSDWq22SL3n1d8nK1eq4eRkf79PLl/WoLjYct//WDEmisaJiIiIiMg62X3CsXjxYri5uQ14bNu2zdLhERERERHZNJuo4TCn4uJidHd3D/iZp6fnDY6GiIiIyPqwaJxGwu4TDh8fH0uHQEREREQ0Ztn9I1VERERERGQ+TDiIiIiIiMhs7P6RKmvzt78BEv6vYtd6ey0dAVmD48ctHQFZg337LB0BWVpXF/CnP1k6CqKR4U9bIiIiIhoUi8ZpJPhIFRERERERmQ0TDiIiIiIiMhsmHEREREREZDZMOIiIiIiIyGxYNE5EREREg2LROI0EVziIiIiIiMhsrD7hiI6Oxrp16ywdBhERERERmcDqE47B6HQ6ZGRkIDg4GK6urvD29oZcLkdbW5tRP39/f4hEIqNj+/btQ57HYDAgNzcXN998M6RSKXx8fPDcc88Jnx8/fhy33347vLy8MG7cOMyePRsKhWLU7pOIiIiIyFbZdA1HV1cXlEolsrKyMH/+fHz33XdIS0tDbGwsGhoajPpmZ2cjKSlJOB8/fvyQ50lLS8ORI0eQm5uL4OBgXLhwARcuXBA+d3V1RWpqKubNmwdXV1ccP34cKSkpcHV1RXJy8shvlIiIiIjIRtlEwtHb24vU1FSUlpbC0dERq1evRnZ2NmQyGSorK4367t69G+Hh4WhtbYWfn5/QPn78eEyZMmXYc3/22WcoKirCp59+iqCgIADAjBkzjPqEhoYiNDRUOPf390dZWRmOHTvGhIOIiIhsHovGaSRs4pGqkpISSCQS1NfXo6CgAPn5+SguLh6wr1qthkgkgoeHh1H79u3b4eXlhdDQUDz//PPo7e0d0txvvfUWZs6cifLycsyYMQP+/v5YuXKl0QrHj3300Uc4ceIEFi5ceM0+Wq0WGo3G6CAiIiIiGmtsYoXD19cXCoUCIpEIQUFBOH36NBQKhdEjUgDQ09ODjIwMxMfHw93dXWhfu3YtFixYAE9PT5w4cQKZmZk4f/488vPzrzv3l19+CZVKhTfeeAN//OMfodfrsX79eixbtgxVVVVGfadNm4ZvvvkGvb292Lx5M1auXHnNcXNycrBly5ZhfhNERERERLbFJhKOyMhIiEQi4TwqKgp5eXnQ6/UQi8UArhSQx8XFwWAwoKioyOj69PR04e958+bByckJKSkpyMnJgVQqHXTuvr4+aLVa/PGPf8TNN98MAHj55Zfxk5/8BE1NTcJjVgBw7NgxXLp0CSdPnsTTTz+NWbNmIT4+fsBxMzMzjeLSaDTw9fUd4jdCRERERGQbbCLhuJ6ryYZKpUJVVZXR6sZAIiIi0Nvbi5aWFqOEYSBTp06FRCIRkg0AuOWWWwAAra2tRtdfre0IDg5GR0cHNm/efM2EQyqVXjfZISIiIiKydTaRcNTV1Rmdnzx5EoGBgRCLxUKy0dzcjOrqanh5eV13vMbGRjg4OGDSpEnX7Xv77bejt7cXX3zxBQICAgAAn3/+OQBg+vTp17zu6soIERERka1j0TiNhE0kHK2trUhPT0dKSgqUSiUKCwuRl5cHnU6HZcuWQalUory8HHq9Hu3t7QAAT09PODk5oba2FnV1dbjrrrswfvx41NbWYv369Xj00Udx0003XXfuRYsWYcGCBXjiiSewc+dO9PX14cknn8Q999wjrHq88MIL8PPzw+zZswEA77//PnJzc7F27VrzfSlERERERDbAJhIOuVyO7u5uhIeHQywWIy0tDcnJyVCpVDh06BAAICQkxOia6upqREdHQyqVYv/+/di8eTO0Wi1mzJiB9evXG9VPDMbBwQFvvfUW1qxZgzvvvBOurq5YvHgx8vLyhD59fX3IzMzEV199BYlEgoCAAOzYsQMpKSmj9h0QEREREdkikcFgMFg6CLpSNC6TyfCzn6khkQxeg0Jj2xDf2Exj3PHjlo6ArMG+fZaOgCytq0uDlStlUKvV161RNYerv08eeUQNJyf7+31y+bIG+/ZZ7vsfK2xiHw4iIiIiIrJNdp9wLF68GG5ubgMe27Zts3R4RERERBZ3tWjcHg8aOZuo4TCn4uJidHd3D/iZp6fnDY6GiIiIiGhssfuEw8fHx9IhEBERERGNWXb/SBUREREREZkPEw4iIiIiIjIbu3+kytq8/b//hPv48ZYOgyzpP5tXkn3r8PuppUMgKzBhgqUjIEvTaICVKy0dhf0WUNvjPZsDVziIiIiIiMhsmHAQEREREZHZMOEgIiIiIiKzYcJBRERERERmw6JxIiIiIhqUwWCfBdQGg6UjGBu4wkFERERERGbDhIOIiIiIiMzGoglHdHQ01q1bZ8kQiIiIiIjIjKx2hUOn0yEjIwPBwcFwdXWFt7c35HI52trajPr5+/tDJBIZHdu3bx/SHD09PUhMTERwcDAkEgmWLl06YL+amhosWLAAUqkUs2bNwquvvnrdGEQiEZ588klTbp2IiIiIaMyw2qLxrq4uKJVKZGVlYf78+fjuu++QlpaG2NhYNDQ0GPXNzs5GUlKScD5+iDt16/V6jBs3DmvXrsXBgwcH7PPVV1/hvvvuw6pVq7B3714cPXoUK1euxNSpUxETEwMA+PDDD6HX64VrPv30U9xzzz34f//v/w33tomIiIisDncap5GweMLR29uL1NRUlJaWwtHREatXr0Z2djZkMhkqKyuN+u7evRvh4eFobW2Fn5+f0D5+/HhMmTJl2HO7urqiqKgIAPDBBx/g4sWL/fq8+OKLmDFjBvLy8gAAt9xyC44fPw6FQiEkHBMnTjS6Zvv27QgICMDChQuHHRMRERER0Vhi8UeqSkpKIJFIUF9fj4KCAuTn56O4uHjAvmq1GiKRCB4eHkbt27dvh5eXF0JDQ/H888+jt7d31OKrra3FokWLjNpiYmJQW1s7YP/Lly/jtddewxNPPAGRSHTNcbVaLTQajdFBRERERDTWWHyFw9fXFwqFAiKRCEFBQTh9+jQUCsX/b+/+o6Kq8/+BP4fBGYgfg0MiP0TBJLQCgVgQ3W/BpmvS5rJnjc0y/NEKWQqCukjlJtQKZUCslm2Lvz5sLrtt2pKVhYq2IuCPcc5SuqQZgyZYisygwsAM9/uHy2wTPxWGOw7Pxzn3HO77vu+9r/v2njn35fv9vtdsiBRwY75Feno65s6dC1dXV1N5cnIywsLCoFQqcfjwYWRkZKC+vh55eXmDEl9DQwNGjx5tVjZ69GjodDq0tLTA0dHRbNsHH3yApqYmLFiwoNfjZmdnIzMzc1BiJCIiIiKyVqL3cEyZMsWsJyAqKgqnT582mxPR3t6O+Ph4CIJgGgLVKS0tDdHR0QgODsYzzzyD3NxcbNiwAXq9fsiu4Yc2b96MWbNmwdvbu9d6GRkZ0Gq1puXcuXNDFCERERER0dARvYejL53Jhkajwf79+816N7oTGRkJg8GA2tpaBAYGDvj8np6euHjxolnZxYsX4erq2qV3Q6PRYO/evdi5c2efx5XL5ZDL5QOOj4iIiMjSOGmcBkL0Ho6qqiqz9crKQ8BZ+wAAQdFJREFUSgQEBEAqlZqSjdOnT2Pv3r1wd3fv83hqtRp2dnbw8PAYlPiioqKwb98+s7LS0lJERUV1qbt161Z4eHjgkUceGZRzExERERHd7kTv4airq0NaWhqSkpKgUqmwYcMG5Obmor29HXPmzIFKpcLu3bthNBrR0NAAAFAqlZDJZKioqEBVVRViYmLg4uKCiooKpKamYt68eRg5cmS/zn/y5Em0tbWhsbERzc3NUKvVAICQkBAAwDPPPIONGzfid7/7HRYtWoT9+/fj73//Oz766COz43R0dGDr1q2YP38+7O1Fb1YiIiIiIqsg+pNxQkICWlpaEBERAalUipSUFCQmJkKj0aCkpATA/x7+O5WVlSE6OhpyuRzFxcVYu3Yt9Ho9/P39kZqairS0tH6fPzY2FhqNxrQeGhoKABAEAQDg7++Pjz76CKmpqSgoKMCYMWNQWFhoeiVup71796Kurg6LFi26lWYgIiIiIrJJEqHzyZpEpdPpoFAooP3yS7j288OFZKP+25NHw9vFsT8ROwSyAnfeKXYEJDadTgelUgGtVtvnPFZLnV+hUOBXv9JixIihP7/Y2tt12LVLvPa3FaL3cBARERGRdeOkcRoI0SeNW9KsWbPg7Ozc7bJu3TqxwyMiIiIisnk23cNRWFiIlpaWbrcplcohjoaIiIiIaPix6YTDx8dH7BCIiIiIiIY1mx5SRURERERE4rLpHg4iIiIiGjhOGqeBYMJhbdavB2QysaMgES3u+JPYIZAV0OnEjoCswfjxYkdAYtPrxY6AaOA4pIqIiIiIiCyGCQcREREREVkMEw4iIiIiIrIYzuEgIiIiol5x0jgNBHs4iIiIiIjIYphwEBERERGRxTDhICIiIiIiixE14YiOjsby5cvFDIGIiIiIiCzIans42tvbkZ6ejqCgIDg5OcHb2xsJCQm4cOGCWT0/Pz9IJBKzJScnp1/naG1txYIFCxAUFAR7e3vExcV1qVNfX48nnngCd999N+zs7LpNkHbu3Inw8HC4ubnByckJISEhKCoqupXLJiIiIrI6nZPGh+NCA2e1Ccf169ehUqmwZs0aqFQq7Ny5EzU1NZg9e3aXullZWaivrzcty5Yt69c5jEYjHB0dkZycjOnTp3dbR6/XY9SoUXjxxRcxefLkbusolUq88MILqKiowL///W8sXLgQCxcuxKefftr/CyYiIiIiskGivxbXYDBg6dKlKCoqwogRI7BkyRJkZWVBoVCgtLTUrO7GjRsRERGBuro6jB071lTu4uICT0/Pmz63k5MTNm3aBAAoLy9HU1NTlzp+fn4oKCgAAGzZsqXb40RHR5utp6SkYPv27Th06BBmzpx503EREREREdkK0Xs4tm/fDnt7exw5cgQFBQXIy8tDYWFht3W1Wi0kEgnc3NzMynNycuDu7o7Q0FCsX78eBoNhCCLvniAI2LdvH2pqavDAAw/0WE+v10On05ktRERERES2RvQeDl9fX+Tn50MikSAwMBDV1dXIz8/H4sWLzeq1trYiPT0dc+fOhaurq6k8OTkZYWFhUCqVOHz4MDIyMlBfX4+8vLwhvQ6tVgsfHx/o9XpIpVK89dZbmDFjRo/1s7OzkZmZOYQREhERERENPdETjilTpkAikZjWo6KikJubC6PRCKlUCuDGBPL4+HgIgmAaAtUpLS3N9HdwcDBkMhmSkpKQnZ0NuVw+NBeBG8O61Go1rl69in379iEtLQ3jx4/vMtyqU0ZGhlnsOp0Ovr6+QxQtERERUf8N1wnUw/GaLUH0hKMvncmGRqPB/v37zXo3uhMZGQmDwYDa2loEBgYOUZSAnZ0dJkyYAAAICQnBqVOnkJ2d3WPCIZfLhzQhIiIiIiISg+gJR1VVldl6ZWUlAgICIJVKTcnG6dOnUVZWBnd39z6Pp1arYWdnBw8PD0uF3C8dHR3Q6/WixkBEREREJDbRE466ujqkpaUhKSkJKpUKGzZsQG5uLtrb2zFnzhyoVCrs3r0bRqMRDQ0NAG68hlYmk6GiogJVVVWIiYmBi4sLKioqkJqainnz5mHkyJH9Ov/JkyfR1taGxsZGNDc3Q61WA7jRS9Gps+zq1av4/vvvoVarIZPJcM899wC4MR8jPDwcd911F/R6PT7++GMUFRV1Gf5FRERERDTciJ5wJCQkoKWlBREREZBKpUhJSUFiYiI0Gg1KSkoAmD/8A0BZWRmio6Mhl8tRXFyMtWvXQq/Xw9/fH6mpqWZzI/oSGxsLjUZjWg8NDQVw421TPy4DgOPHj2PHjh0YN24camtrAQDXrl3Ds88+i/Pnz8PR0RETJ07EX/7yF/zmN7+52eYgIiIiIrIpEuGHT9YkGp1OB4VCAe2CBXCVycQOh0S0uONPYodAVoBvyiYAGD9e7AhIbHq9Dvn5Cmi12j7nsVpC5/PJjBlajBgx9OcXW3u7DqWl4rW/rRD9OxxERERERGS7bDrhmDVrFpydnbtd1q1bJ3Z4REREREQ2T/Q5HJZUWFiIlpaWbrcplcohjoaIiIiIaPix6YTDx8dH7BCIiIiIiIY1m044iIiIiGjgBGF4fnWbr1YaHDY9h4OIiIiIiMTFhIOIiIiIiCyGQ6qsTVIS4OwsdhQkoj8L1WKHQFZg48EgsUMgK/D992JHQGLjkB6yBezhICIiIiIii2EPBxERERH1qqNjeE4aH47XbAns4SAiIiIiIothwkFERERERBbDhIOIiIiIiCyGCQcREREREVmMqJPGo6OjERISgjfeeEPMMIiIiIioF5w0TgNhtT0c7e3tSE9PR1BQEJycnODt7Y2EhARcuHDBrJ6fnx8kEonZkpOT069ztLa2YsGCBQgKCoK9vT3i4uK61Kmvr8cTTzyBu+++G3Z2dli+fHmvxywuLoZEIun2WEREREREw43VJhzXr1+HSqXCmjVroFKpsHPnTtTU1GD27Nld6mZlZaG+vt60LFu2rF/nMBqNcHR0RHJyMqZPn95tHb1ej1GjRuHFF1/E5MmTez1ebW0tVq5cif/3//5fv85PRERERGTrRP8Oh8FgwNKlS1FUVIQRI0ZgyZIlyMrKgkKhQGlpqVndjRs3IiIiAnV1dRg7dqyp3MXFBZ6enjd9bicnJ2zatAkAUF5ejqampi51/Pz8UFBQAADYsmVLj8cyGo148sknkZmZiX/961/dHouIiIiIaLgRvYdj+/btsLe3x5EjR1BQUIC8vDwUFhZ2W1er1UIikcDNzc2sPCcnB+7u7ggNDcX69ethMBiGIHJzWVlZ8PDwwNNPP92v+nq9HjqdzmwhIiIiIrI1ovdw+Pr6Ij8/HxKJBIGBgaiurkZ+fj4WL15sVq+1tRXp6emYO3cuXF1dTeXJyckICwuDUqnE4cOHkZGRgfr6euTl5Q3ZNRw6dAibN2+GWq3u9z7Z2dnIzMy0XFBEREREg4STxmkgRO/hmDJlCiQSiWk9KioKp0+fhtFoNJW1t7cjPj4egiCYhkB1SktLQ3R0NIKDg/HMM88gNzcXGzZsgF6vH5L4m5ub8dRTT+HPf/4z7rzzzn7vl5GRAa1Wa1rOnTtnwSiJiIiIiMQheg9HXzqTDY1Gg/3795v1bnQnMjISBoMBtbW1CAwMtHh8X3/9NWpra/Hoo4+ayjr+mw7b29ujpqYGd911V5f95HI55HK5xeMjIiIiIhKT6AlHVVWV2XplZSUCAgIglUpNycbp06dRVlYGd3f3Po+nVqthZ2cHDw8PS4VsZuLEiaiurjYre/HFF9Hc3IyCggL4+voOSRxERERERNZI9ISjrq4OaWlpSEpKgkqlwoYNG5Cbm4v29nbMmTMHKpUKu3fvhtFoRENDAwBAqVRCJpOhoqICVVVViImJgYuLCyoqKpCamop58+Zh5MiR/Tr/yZMn0dbWhsbGRjQ3N5vmYYSEhJjqdJZdvXoV33//PdRqNWQyGe655x44ODjgvvvuMztm56T2H5cTEREREQ03oiccCQkJaGlpQUREBKRSKVJSUpCYmAiNRoOSkhIA5g//AFBWVobo6GjI5XIUFxdj7dq10Ov18Pf3R2pqKtLS0vp9/tjYWGg0GtN6aGgoAEAQhC5lAHD8+HHs2LED48aNQ21t7S1cMREREdHthZPGaSBETTgOHDhg+vvHk8H9/PzMHvq7ExYWhsrKygHF0J+koa84fmzbtm23FgwRERERDQuNjY1YtmwZPvzwQ9jZ2eHXv/41CgoK4Ozs3G392tpa+Pv7d7vt73//Ox577DEAMHsZU6e//vWvePzxxwcv+Jskeg8HEREREdFw8+STT6K+vh6lpaVob2/HwoULkZiYiB07dnRb39fXF/X19WZl77zzDtavX49Zs2aZlW/duhUPP/ywaf3H37AbajadcMyaNQv/+te/ut32/PPP4/nnnx/iiIiIiIhouDt16hT27NmDo0ePIjw8HACwYcMGxMbG4vXXX4e3t3eXfaRSKTw9Pc3Kdu3ahfj4+C69Im5ubl3qismmE47CwkK0tLR0u02pVA5xNERERER0O9LpdGbrA/28QUVFBdzc3EzJBgBMnz4ddnZ2qKqqwq9+9as+j3H8+HGo1Wq8+eabXbY999xz+O1vf4vx48fjmWeewcKFC7sdajVUbDrh8PHxETsEIiIiotvecJ80/uPPHLz00ktYu3btLR+3oaGhyycc7O3toVQqTW9l7cvmzZsxadIkTJ061aw8KysLP/vZz3DHHXfgs88+w7PPPourV68iOTn5luMdKJtOOIiIiIiIBurcuXNmH5/uqXdj9erVePXVV3s91qlTpwYcT0tLC3bs2IE1a9Z02fbDstDQUFy7dg3r169nwkFEREREZK1cXV3NEo6erFixAgsWLOi1zvjx4+Hp6YnvvvvOrNxgMKCxsbFfcy/+8Y9/4Pr160hISOizbmRkJF5++WXo9foBDQMbCCYcRERERESDYNSoURg1alSf9aKiotDU1ITjx4/j/vvvBwDs378fHR0diIyM7HP/zZs3Y/bs2f06l1qtxsiRI0VLNgAmHFbnrMM9cHHsO4Mm2/XJJ2JHQNbg++/FjoCsQTcvqqFhpod339BtbtKkSXj44YexePFivP3222hvb8fSpUvx+OOPm95Q9e233+Khhx7C//3f/yEiIsK075kzZ/D555/j448/7nLcDz/8EBcvXsSUKVPg4OCA0tJSrFu3DitXrhyya+sOEw4iIiIi6tVwnzRuCe+++y6WLl2Khx56yPThvz/+8Y+m7e3t7aipqcH169fN9tuyZQvGjBmDn//8512OOWLECLz55ptITU2FIAiYMGEC8vLysHjxYstdSD9IhJv9jDZZhE6ng0KhwIkTWri4sIdjOGMPBwHs4aAb2MNBLS06pKYqoNVq+zWHYLB1Pp9MmaKFvf3wez4xGHSorBSv/W2FndgBEBERERGR7WLCQUREREREFsOEg4iIiIiILIaTxomIiIioV5w0TgMhag9HdHQ0li9fLmYIRERERERkQVY7pKq9vR3p6ekICgqCk5MTvL29kZCQgAsXLpjV8/Pzg0QiMVtycnL6dY7W1lYsWLAAQUFBsLe3R1xcXJc6O3fuxIwZMzBq1Ci4uroiKioKn376aZ8xSCQSPPfcc7d8/UREREREtsBqE47r169DpVJhzZo1UKlU2LlzJ2pqajB79uwudbOyslBfX29ali1b1q9zGI1GODo6Ijk5GdOnT++2zueff44ZM2bg448/xvHjxxETE4NHH30UJ06cMNU5evSo2flLS0sBAI899tgtXDkRERERke0QfQ6HwWDA0qVLUVRUhBEjRmDJkiXIysqCQqEwPbh32rhxIyIiIlBXV4exY8eayl1cXODp6XnT53ZycsKmTZsAAOXl5WhqaupS54033jBbX7duHf75z3/iww8/RGhoKAB0+ax8Tk4O7rrrLjz44IM3HRMRERERkS0RvYdj+/btsLe3x5EjR1BQUIC8vDwUFhZ2W1er1UIikcDNzc2sPCcnB+7u7ggNDcX69ethMBgsFm9HRweam5uhVCq73d7W1oa//OUvWLRoESQSSY/H0ev10Ol0ZgsRERGRNeqcND4cFxo40Xs4fH19kZ+fD4lEgsDAQFRXVyM/P7/LJ9hbW1uRnp6OuXPnmn3pMTk5GWFhYVAqlTh8+DAyMjJQX1+PvLw8i8T7+uuv4+rVq4iPj+92+wcffICmpiYsWLCg1+NkZ2cjMzPTAhESEREREVkP0ROOKVOmmPUEREVFITc3F0ajEVKpFMCNCeTx8fEQBME0BKpTWlqa6e/g4GDIZDIkJSUhOzsbcrl8UGPdsWMHMjMz8c9//hMeHh7d1tm8eTNmzZoFb2/vXo+VkZFhFrtOp4Ovr++gxktEREREJDbRE46+dCYbGo0G+/fvN+vd6E5kZCQMBgNqa2sRGBg4aHEUFxfjt7/9Ld57770eJ5hrNBrs3bsXO3fu7PN4crl80BMiIiIiIiJrI3rCUVVVZbZeWVmJgIAASKVSU7Jx+vRplJWVwd3dvc/jqdVq2NnZ9dgDcSv++te/YtGiRSguLsYjjzzSY72tW7fCw8Oj1zpERERERMOJ6AlHXV0d0tLSkJSUBJVKhQ0bNiA3Nxft7e2YM2cOVCoVdu/eDaPRiIaGBgCAUqmETCZDRUUFqqqqEBMTAxcXF1RUVCA1NRXz5s3DyJEj+3X+kydPoq2tDY2NjWhuboZarQYAhISEALgxjGr+/PkoKChAZGSkKQZHR0coFArTcTo6OrB161bMnz8f9vaiNysRERHRoBGE4TmBWhDEjsA2iP5knJCQgJaWFkREREAqlSIlJQWJiYnQaDQoKSkB8L+H/05lZWWIjo6GXC5HcXEx1q5dC71eD39/f6SmpprNjehLbGwsNBqNab3zVbfCf++wd955BwaDAc8995zZh/zmz5+Pbdu2mdb37t2Luro6LFq06GabgIiIiIjIZkkEgbmbNdDpdFAoFDhxQgsXl97nqZBt++QTsSMga/D992JHQNagj/eP0DDQ0qJDaqoCWq22z3msltD5fHL//VpIpcPv+cRo1OH4cfHa31aI/h0OIiIiIiKyXTadcMyaNQvOzs7dLuvWrRM7PCIiIiIimyf6HA5LKiwsREtLS7fbevpSOBERERGZ6+gAfvDZtGFjOE6UtwSbTjh8fHzEDoGIiIiIaFiz6SFVREREREQkLiYcRERERERkMUw4iIiIiIjIYmx6DsftqLYWcHISOwoS0/HjYkdA1mDKFLEjICL6H04ap4FgDwcREREREVkMEw4iIiIiIrIYJhxERERERGQxTDiIiIiIiMhiOGmciIiIiHrFSeM0EOzhICIiIiIiixE14YiOjsby5cvFDIGIiIiIiCzIans42tvbkZ6ejqCgIDg5OcHb2xsJCQm4cOGCWT0/Pz9IJBKzJScnp1/naG1txYIFCxAUFAR7e3vExcV1qXPgwIEux5dIJGhoaDDV2bRpE4KDg+Hq6gpXV1dERUXhk08+GdD1ExERERHZAqudw3H9+nWoVCqsWbMGkydPxpUrV5CSkoLZs2fj2LFjZnWzsrKwePFi07qLi0u/zmE0GuHo6Ijk5GS8//77vdatqamBq6urad3Dw8P095gxY5CTk4OAgAAIgoDt27fjl7/8JU6cOIF77723X7EQEREREdki0RMOg8GApUuXoqioCCNGjMCSJUuQlZUFhUKB0tJSs7obN25EREQE6urqMHbsWFO5i4sLPD09b/rcTk5O2LRpEwCgvLwcTU1NPdb18PCAm5tbt9seffRRs/U//OEP2LRpEyorK5lwEBER0W2Pk8ZpIEQfUrV9+3bY29vjyJEjKCgoQF5eHgoLC7utq9VqIZFIujz45+TkwN3dHaGhoVi/fj0MBsOgxxkSEgIvLy/MmDED5eXlPdYzGo0oLi7GtWvXEBUVNehxEBERERHdTkTv4fD19UV+fj4kEgkCAwNRXV2N/Px8syFSwI35Funp6Zg7d67Z0Kbk5GSEhYVBqVTi8OHDyMjIQH19PfLy8gYlPi8vL7z99tsIDw+HXq9HYWEhoqOjUVVVhbCwMFO96upqREVFobW1Fc7Ozti1axfuueeeHo+r1+uh1+tN6zqdblDiJSIiIiKyJqInHFOmTIHkB310UVFRyM3NhdFohFQqBXBjAnl8fDwEQTANgeqUlpZm+js4OBgymQxJSUnIzs6GXC4fcHyBgYEIDAw0rU+dOhVff/018vPzUVRUZFZPrVZDq9XiH//4B+bPn4+DBw/2mHRkZ2cjMzNzwPEREREREVkz0YdU9aUz2dBoNCgtLTXr3ehOZGQkDAYDamtrLRZTREQEzpw5Y1Ymk8kwYcIE3H///cjOzsbkyZNRUFDQ4zEyMjKg1WpNy7lz5ywWLxERERGRWETv4aiqqjJbr6ysREBAAKRSqSnZOH36NMrKyuDu7t7n8dRqNezs7MzeIjXY1Go1vLy8eq3T0dFhNmTqx+Ry+aD0wBARERFZGieN00CInnDU1dUhLS0NSUlJUKlU2LBhA3Jzc9He3o45c+ZApVJh9+7dMBqNpm9fKJVKyGQyVFRUoKqqCjExMXBxcUFFRQVSU1Mxb948jBw5sl/nP3nyJNra2tDY2Ijm5mao1WoANyaJA8Abb7wBf39/3HvvvWhtbUVhYSH279+Pzz77zHSMjIwMzJo1C2PHjkVzczN27NiBAwcO4NNPPx3UtiIiIiIiut2InnAkJCSgpaUFERERkEqlSElJQWJiIjQaDUpKSgD87+G/U1lZGaKjoyGXy1FcXIy1a9dCr9fD398fqampZvM6+hIbGwuNRmNaDw0NBQAIggAAaGtrw4oVK/Dtt9/ijjvuQHBwMPbu3YuYmBjTPt999x0SEhJQX18PhUKB4OBgfPrpp5gxY8atNgsRERERkU2QCJ1P1iQqnU4HhUKBXbu0cHLqfZ4K2bYdO8SOgKzBlCliR0BE1qClRYfUVAW0Wm2f81gtofP5ZNIkLaTS4fd8YjTqcOqUeO1vK6x+0jgREREREd2+bDrhmDVrFpydnbtd1q1bJ3Z4RERERLeFjo7hu9DAiT6Hw5IKCwvR0tLS7TalUjnE0RARERERDT82nXD4+PiIHQIRERER0bBm00OqiIiIiIhIXEw4iIiIiIjIYmx6SBURERERDRy/NE4DwYTDyshkNxYavhwdxY6ArEFxsdgRkDUoLBQ7AhJbc7PYERANHIdUERERERGRxTDhICIiIiIii2HCQUREREREFsM5HERERETUK04ap4FgDwcREREREVkMEw4iIiIiIrIYUROO6OhoLF++XMwQiIiIiIjIgqy2h6O9vR3p6ekICgqCk5MTvL29kZCQgAsXLpjV8/Pzg0QiMVtycnL6dY7W1lYsWLAAQUFBsLe3R1xcXJc6Bw4c6HJ8iUSChoYGs3rffvst5s2bB3d3dzg6OiIoKAjHjh275esnIiIiIrIFVjtp/Pr161CpVFizZg0mT56MK1euICUlBbNnz+7yIJ+VlYXFixeb1l1cXPp1DqPRCEdHRyQnJ+P999/vtW5NTQ1cXV1N6x4eHqa/r1y5gmnTpiEmJgaffPIJRo0ahdOnT2PkyJH9ioOIiIjImgnC8JxALQhiR2AbRE84DAYDli5diqKiIowYMQJLlixBVlYWFAoFSktLzepu3LgRERERqKurw9ixY03lLi4u8PT0vOlzOzk5YdOmTQCA8vJyNDU19VjXw8MDbm5u3W579dVX4evri61bt5rK/P39bzoeIiIiIiJbI/qQqu3bt8Pe3h5HjhxBQUEB8vLyUFhY2G1drVYLiUTS5cE/JycH7u7uCA0Nxfr162EwGAY9zpCQEHh5eWHGjBkoLy8321ZSUoLw8HA89thj8PDwQGhoKP785z8PegxERERERLcb0Xs4fH19kZ+fD4lEgsDAQFRXVyM/P99siBRwY75Feno65s6daza0KTk5GWFhYVAqlTh8+DAyMjJQX1+PvLy8QYnPy8sLb7/9NsLDw6HX61FYWIjo6GhUVVUhLCwMAHD27Fls2rQJaWlpeP7553H06FEkJydDJpNh/vz53R5Xr9dDr9eb1nU63aDES0RERERkTURPOKZMmQLJD74kExUVhdzcXBiNRkilUgA3JpDHx8dDEATTEKhOaWlppr+Dg4Mhk8mQlJSE7OxsyOXyAccXGBiIwMBA0/rUqVPx9ddfIz8/H0VFRQCAjo4OhIeHY926dQCA0NBQfPHFF3j77bd7TDiys7ORmZk54PiIiIiIiKyZ6EOq+tKZbGg0GpSWlpr1bnQnMjISBoMBtbW1FospIiICZ86cMa17eXnhnnvuMaszadIk1NXV9XiMjIwMaLVa03Lu3DmLxUtEREQ0EB0dw3ehgRO9h6OqqspsvbKyEgEBAZBKpaZk4/Tp0ygrK4O7u3ufx1Or1bCzszN7i9RgU6vV8PLyMq1PmzYNNTU1ZnW++uorjBs3rsdjyOXyQemBISIiIiKyZqInHHV1dUhLS0NSUhJUKhU2bNiA3NxctLe3Y86cOVCpVNi9ezeMRqPp2xdKpRIymQwVFRWoqqpCTEwMXFxcUFFRgdTUVMybN6/fr6Q9efIk2tra0NjYiObmZqjVagA3JokDwBtvvAF/f3/ce++9aG1tRWFhIfbv34/PPvvMdIzU1FRMnToV69atQ3x8PI4cOYJ33nkH77zzzqC2FRERERHR7Ub0hCMhIQEtLS2IiIiAVCpFSkoKEhMTodFoUFJSAuB/D/+dysrKEB0dDblcjuLiYqxduxZ6vR7+/v5ITU01m9fRl9jYWGg0GtN6aGgoAED474uX29rasGLFCnz77be44447EBwcjL179yImJsa0z09+8hPs2rULGRkZyMrKgr+/P9544w08+eSTt9osREREREQ2QSII/KSJNdDpdFAoFPjoIy2cnHqfp0K27W9/EzsCsganTokdAVmDHt4ST8NIc7MOoaEKaLXaPuexWkLn84mfnxZ2dsPv+aSjQ4faWvHa31aI3sNBRERERNZtuE6eHq7XPdis/i1VAzFr1iw4Ozt3u3S+wpaIiIiIiCzHpns4CgsL0dLS0u02pVI5xNEQEREREQ0/Np1w+Pj4iB0CEREREdGwZtNDqoiIiIiISFw23cNBRERERAM3XCdPD9frHmzs4SAiIiIiIothD4eV+emEBri6XBM7DBLRg09fEDsEsgYTJ4odAVmDM2fEjoBEprt2VewQiAaMPRxERERERGQxTDiIiIiIiMhiOKSKiIiIiHo1XCdPD9frHmzs4SAiIiIiIothwkFERERERBbDhIOIiIiIiCxG1IQjOjoay5cvFzMEIiIiIiKyIKvt4Whvb0d6ejqCgoLg5OQEb29vJCQk4MIF828U+Pn5QSKRmC05OTn9OkdraysWLFiAoKAg2NvbIy4urtt6er0eL7zwAsaNGwe5XA4/Pz9s2bLFrM57772HiRMnwsHBAUFBQfj4449v6bqJiIiIrE1Hx/BdaOCs9i1V169fh0qlwpo1azB58mRcuXIFKSkpmD17No4dO2ZWNysrC4sXLzatu7i49OscRqMRjo6OSE5Oxvvvv99jvfj4eFy8eBGbN2/GhAkTUF9fj44f3IGHDx/G3LlzkZ2djV/84hfYsWMH4uLioFKpcN99993klRMRERER2Q7REw6DwYClS5eiqKgII0aMwJIlS5CVlQWFQoHS0lKzuhs3bkRERATq6uowduxYU7mLiws8PT1v+txOTk7YtGkTAKC8vBxNTU1d6uzZswcHDx7E2bNnoVQqAdzoVfmhgoICPPzww1i1ahUA4OWXX0ZpaSk2btyIt99++6bjIiIiIiKyFaIPqdq+fTvs7e1x5MgRFBQUIC8vD4WFhd3W1Wq1kEgkcHNzMyvPycmBu7s7QkNDsX79ehgMhkGLr6SkBOHh4Xjttdfg4+ODu+++GytXrkRLS4upTkVFBaZPn26238yZM1FRUTFocRARERER3Y5E7+Hw9fVFfn4+JBIJAgMDUV1djfz8fLMhUsCN+Rbp6emYO3cuXF1dTeXJyckICwuDUqnE4cOHkZGRgfr6euTl5Q1KfGfPnsWhQ4fg4OCAXbt24dKlS3j22Wdx+fJlbN26FQDQ0NCA0aNHm+03evRoNDQ09HhcvV4PvV5vWtfpdIMSLxERERGRNRE94ZgyZQokEolpPSoqCrm5uTAajZBKpQBuTCCPj4+HIAimIVCd0tLSTH8HBwdDJpMhKSkJ2dnZkMvlA46vo6MDEokE7777LhQKBQAgLy8Pc+bMwVtvvQVHR8dbOm52djYyMzMHHB8RERGRpQ3XydPD9boHm+hDqvrSmWxoNBqUlpaa9W50JzIyEgaDAbW1tYNyfi8vL/j4+JiSDQCYNGkSBEHA+fPnAQCenp64ePGi2X4XL17sdV5JRkYGtFqtaTl37tygxEtEREREZE1ETziqqqrM1isrKxEQEACpVGpKNk6fPo29e/fC3d29z+Op1WrY2dnBw8NjUOKbNm0aLly4gKtXr5rKvvrqK9jZ2WHMmDEAbvTK7Nu3z2y/0tJSREVF9XhcuVwOV1dXs4WIiIiIyNaIPqSqrq4OaWlpSEpKgkqlwoYNG5Cbm4v29nbMmTMHKpUKu3fvhtFoNM2JUCqVkMlkqKioQFVVFWJiYuDi4oKKigqkpqZi3rx5GDlyZL/Of/LkSbS1taGxsRHNzc1Qq9UAgJCQEADAE088gZdffhkLFy5EZmYmLl26hFWrVmHRokWm4VQpKSl48MEHkZubi0ceeQTFxcU4duwY3nnnnUFvLyIiIiKi24noCUdCQgJaWloQEREBqVSKlJQUJCYmQqPRoKSkBMD/Hv47lZWVITo6GnK5HMXFxVi7di30ej38/f2RmppqNq+jL7GxsdBoNKb10NBQAIAgCAAAZ2dnlJaWYtmyZQgPD4e7uzvi4+PxyiuvmPaZOnUqduzYgRdffBHPP/88AgIC8MEHH/AbHEREREQ07EmEzidrEpVOp4NCoYC2pgau/fxwIdmoCxfEjoCswcSJYkdA1uDMGbEjIJHprl6F4qc/hVarFWX4defzyZ13amFnN/yGf3d06HDpkkK09rcVos/hICIiIiIi22XTCcesWbPg7Ozc7bJu3TqxwyMiIiIisnmiz+GwpMLCQrMvgv+QUqkc4miIiIiIiIYfm044fHx8xA6BiIiIiGhYs+mEg4iIiIgGbrh+cXu4Xvdgs+k5HEREREREJC4mHEREREREZDEcUmVtNm4E5HKxoyAxBQeLHQFZg+++EzsCsgYODmJHQGK7dk3sCIgGjD0cRERERERkMezhICIiIqJeCcLwnEAtCGJHYBvYw0FERERERBbDhIOIiIiIiCyGCQcREREREVkMEw4iIiIiIrIYUSeNR0dHIyQkBG+88YaYYRARERFRLzo6AIlE7CiGHieNDw6r7eFob29Heno6goKC4OTkBG9vbyQkJODChQtm9fz8/CCRSMyWnJycfp2jtbUVCxYsQFBQEOzt7REXF9dtPb1ejxdeeAHjxo2DXC6Hn58ftmzZYtq+c+dOhIeHw83NDU5OTggJCUFRUdEtXzsRERERka2w2tfiXr9+HSqVCmvWrMHkyZNx5coVpKSkYPbs2Th27JhZ3aysLCxevNi07uLi0q9zGI1GODo6Ijk5Ge+//36P9eLj43Hx4kVs3rwZEyZMQH19PTp+8G44pVKJF154ARMnToRMJsPu3buxcOFCeHh4YObMmTd55UREREREtkP0hMNgMGDp0qUoKirCiBEjsGTJEmRlZUGhUKC0tNSs7saNGxEREYG6ujqMHTvWVO7i4gJPT8+bPreTkxM2bdoEACgvL0dTU1OXOnv27MHBgwdx9uxZKJVKADd6VX4oOjrabD0lJQXbt2/HoUOHmHAQERER0bAm+pCq7du3w97eHkeOHEFBQQHy8vJQWFjYbV2tVguJRAI3Nzez8pycHLi7uyM0NBTr16+HwWAYtPhKSkoQHh6O1157DT4+Prj77ruxcuVKtLS0dFtfEATs27cPNTU1eOCBBwYtDiIiIiKi25HoPRy+vr7Iz8+HRCJBYGAgqqurkZ+fbzZECrgx3yI9PR1z586Fq6urqTw5ORlhYWFQKpU4fPgwMjIyUF9fj7y8vEGJ7+zZszh06BAcHBywa9cuXLp0Cc8++ywuX76MrVu3mupptVr4+PhAr9dDKpXirbfewowZM3o8rl6vh16vN63rdLpBiZeIiIhosHHSOA2E6AnHlClTIPnBHRwVFYXc3FwYjUZIpVIANyaQx8fHQxAE0xCoTmlpaaa/g4ODIZPJkJSUhOzsbMjl8gHH19HRAYlEgnfffRcKhQIAkJeXhzlz5uCtt96Co6MjgBvDutRqNa5evYp9+/YhLS0N48eP7zLcqlN2djYyMzMHHB8RERERkTUTfUhVXzqTDY1Gg9LSUrPeje5ERkbCYDCgtrZ2UM7v5eUFHx8fU7IBAJMmTYIgCDh//rypzM7ODhMmTEBISAhWrFiBOXPmIDs7u8fjZmRkQKvVmpZz584NSrxERERERNZE9ISjqqrKbL2yshIBAQGQSqWmZOP06dPYu3cv3N3d+zyeWq2GnZ0dPDw8BiW+adOm4cKFC7h69aqp7KuvvoKdnR3GjBnT434dHR1mQ6Z+TC6Xw9XV1WwhIiIiIrI1og+pqqurQ1paGpKSkqBSqbBhwwbk5uaivb0dc+bMgUqlwu7du2E0GtHQ0ADgxmtoZTIZKioqUFVVhZiYGLi4uKCiogKpqamYN28eRo4c2a/znzx5Em1tbWhsbERzczPUajUAICQkBADwxBNP4OWXX8bChQuRmZmJS5cuYdWqVVi0aJFpOFV2djbCw8Nx1113Qa/X4+OPP0ZRUVGX4V9ERERERMON6AlHQkICWlpaEBERAalUipSUFCQmJkKj0aCkpATA/x7+O5WVlSE6OhpyuRzFxcVYu3Yt9Ho9/P39kZqaajavoy+xsbHQaDSm9dDQUAA33jYFAM7OzigtLcWyZcsQHh4Od3d3xMfH45VXXjHtc+3aNTz77LM4f/48HB0dMXHiRPzlL3/Bb37zm1ttFiIiIiKrwUnjNBASQWBTWgOdTgeFQgHtsmVwHYTJ7nQbCw4WOwKyBnfeKXYEZA0cHMSOgESmu3YNikcfhVarFWX4defziZOTFhLJ8Bv+LQg6XLumEK39bYXocziIiIiIiMh22XTCMWvWLDg7O3e7rFu3TuzwiIiIiIhsnuhzOCypsLCwxy+CK5XKIY6GiIiIiGj4semEw8fHR+wQiIiIiG57nDROA2HTQ6qIiIiIiEhcTDiIiIiIiMhimHAQEREREZHF2PQcjttSRgbA9zwPb21tYkdAVqC+daTYIZAVuH5d7AhIbM3NOrFDIBowJhxERERE1CtOGqeB4JAqIiIiIiKyGCYcRERERERkMUw4iIiIiIjIYphwEBERERGRxXDSOBERERH1ipPGaSBE7eGIjo7G8uXLxQyBiIiIiIgsyGqHVLW3tyM9PR1BQUFwcnKCt7c3EhIScOHCBbN6fn5+kEgkZktOTk6/ztHa2ooFCxYgKCgI9vb2iIuL67aeXq/HCy+8gHHjxkEul8PPzw9btmwxbY+Oju4Sg0QiwSOPPHLL109EREREZAusdkjV9evXoVKpsGbNGkyePBlXrlxBSkoKZs+ejWPHjpnVzcrKwuLFi03rLi4u/TqH0WiEo6MjkpOT8f777/dYLz4+HhcvXsTmzZsxYcIE1NfXo6Ojw7R9586daPvBx9ouX76MyZMn47HHHuvv5RIRERER2STREw6DwYClS5eiqKgII0aMwJIlS5CVlQWFQoHS0lKzuhs3bkRERATq6uowduxYU7mLiws8PT1v+txOTk7YtGkTAKC8vBxNTU1d6uzZswcHDx7E2bNnoVQqAdzoVfmhzvJOxcXFuOOOO5hwEBEREdGwJ/qQqu3bt8Pe3h5HjhxBQUEB8vLyUFhY2G1drVYLiUQCNzc3s/KcnBy4u7sjNDQU69evh8FgGLT4SkpKEB4ejtdeew0+Pj64++67sXLlSrS0tPS4z+bNm/H444/Dyclp0OIgIiIiEktHx/BdaOBE7+Hw9fVFfn4+JBIJAgMDUV1djfz8fLMhUsCN+Rbp6emYO3cuXF1dTeXJyckICwuDUqnE4cOHkZGRgfr6euTl5Q1KfGfPnsWhQ4fg4OCAXbt24dKlS3j22Wdx+fJlbN26tUv9I0eO4IsvvsDmzZt7Pa5er4derzet63S6QYmXiIiIiMiaiJ5wTJkyBZIfvGctKioKubm5MBqNkEqlAG5MII+Pj4cgCKYhUJ3S0tJMfwcHB0MmkyEpKQnZ2dmQy+UDjq+jowMSiQTvvvsuFAoFACAvLw9z5szBW2+9BUdHR7P6mzdvRlBQECIiIno9bnZ2NjIzMwccHxERERGRNRN9SFVfOpMNjUaD0tJSs96N7kRGRsJgMKC2tnZQzu/l5QUfHx9TsgEAkyZNgiAIOH/+vFnda9euobi4GE8//XSfx83IyIBWqzUt586dG5R4iYiIiIisiegJR1VVldl6ZWUlAgICIJVKTcnG6dOnsXfvXri7u/d5PLVaDTs7O3h4eAxKfNOmTcOFCxdw9epVU9lXX30FOzs7jBkzxqzue++9B71ej3nz5vV5XLlcDldXV7OFiIiIiMjWiD6kqq6uDmlpaUhKSoJKpcKGDRuQm5uL9vZ2zJkzByqVCrt374bRaERDQwOAG2+FkslkqKioQFVVFWJiYuDi4oKKigqkpqZi3rx5GDlyZL/Of/LkSbS1taGxsRHNzc1Qq9UAgJCQEADAE088gZdffhkLFy5EZmYmLl26hFWrVmHRokXdDqeKi4vrV2JEREREdLvgl8ZpIERPOBISEtDS0oKIiAhIpVKkpKQgMTERGo0GJSUlAP738N+prKwM0dHRkMvlKC4uxtq1a6HX6+Hv74/U1FSzeR19iY2NhUajMa2HhoYCAIT/3mHOzs4oLS3FsmXLEB4eDnd3d8THx+OVV14xO05NTQ0OHTqEzz777FaagYiIiIjIJkkEgbmbNdDpdFAoFNBeuMDhVcPdDz4iScNXfWv/emnJtl2/LnYEJLbmZh1CQxXQarWiPB90Pp9IJFpIJMPv+UQQdBAE8drfVog+h4OIiIiIiGyXTSccs2bNgrOzc7fLunXrxA6PiIiIiMjmiT6Hw5IKCwt7/CK4Uqkc4miIiIiIbk+CwAnUg+0Pf/gDPvroI6jVashkMjQ1NfW5jyAIeOmll/DnP/8ZTU1NmDZtGjZt2oSAgABTncbGRixbtgwffvgh7Ozs8Otf/xoFBQVwdna24NX0zqYTDh8fH7FDICIiIiLqoq2tDY899hiioqKwefPmfu3z2muv4Y9//CO2b98Of39/rFmzBjNnzsTJkyfh4OAAAHjyySdRX1+P0tJStLe3Y+HChUhMTMSOHTsseTm94qRxK8FJ42TCSeMEThqnGzhpnKxl0jigBTAcn090ACzb/tu2bcPy5cv77OEQBAHe3t5YsWIFVq5cCQDQarUYPXo0tm3bhscffxynTp3CPffcg6NHjyI8PBwAsGfPHsTGxuL8+fPw9va2yDX0xabncBARERER2YJvvvkGDQ0NmD59uqlMoVAgMjISFRUVAICKigq4ubmZkg0AmD59Ouzs7Lp8bHso2fSQqttJZ0eTrrlZ5EhIdOzhIADNeqnYIZAVYA8HXb2qA/C/5wTx6EQ+v1huXLdOZ379crkccrl8SCPp/AD26NGjzcpHjx5t2tbQ0AAPDw+z7fb29lAqlaY6YmDCYSUuX74MAPANDBQ5EiIiIrI2ly9f/u/QpqElk8ng6emJhgbfIT+3tXB2doavr/n1v/TSS1i7dm2XuqtXr8arr77a6/FOnTqFiRMnDmaIVo8Jh5XofGtWXV2dKD8o1kCn08HX1xfnzp0btvNY2AZsA4BtALANALYBwDYAbozRHzt2rGhv13RwcMA333yDtmHc+y4IAiQSiVlZT70bK1aswIIFC3o93vjx428pDk9PTwDAxYsX4eXlZSq/ePEiQkJCTHW+++47s/0MBgMaGxtN+4uBCYeVsLO7MZ1GoVAM2x/VTq6urmwDtgHbAGwDgG0AsA0AtgHwv+cEMTg4OJjegES9GzVqFEaNGmWRY/v7+8PT0xP79u0zJRg6nQ5VVVVYsmQJACAqKgpNTU04fvw47r//fgDA/v370dHRgcjISIvE1R+cNE5ERERENMTq6uqgVqtRV1cHo9EItVoNtVqNq1evmupMnDgRu3btAgBIJBIsX74cr7zyCkpKSlBdXY2EhAR4e3sjLi4OADBp0iQ8/PDDWLx4MY4cOYLy8nIsXboUjz/+uGhvqALYw0FERERENOR+//vfY/v27ab10NBQAEBZWRmio6MBADU1NdBqtaY6v/vd73Dt2jUkJiaiqakJP/3pT7Fnzx6zHqh3330XS5cuxUMPPWT68N8f//jHobmoHjDhsBJyuRwvvfTSkL/xwJqwDdgGANsAYBsAbAOAbQCwDQC2gS3btm0btm3b1mudH7+dTCKRICsrC1lZWT3uo1QqRf3IX3f44T8iIiIiIrIYzuEgIiIiIiKLYcJBREREREQWw4SDiIiIiIgshgmHiBobG/Hkk0/C1dUVbm5uePrpp81ehdad6OhoSCQSs+WZZ54ZoogH7s0334Sfnx8cHBwQGRmJI0eO9Fr/vffew8SJE+Hg4ICgoCB8/PHHQxSp5dxMG2zbtq3Lv/ft/i70zz//HI8++ii8vb0hkUjwwQcf9LnPgQMHEBYWBrlcjgkTJvQ5yc7a3WwbHDhwoMt9IJFI0NDQMDQBD7Ls7Gz85Cc/gYuLCzw8PBAXF4eampo+97Ol34NbaQNb+z3YtGkTgoODTd/YiIqKwieffNLrPrZ0DwA33wa2dg/Q8MGEQ0RPPvkkvvzyS5SWlmL37t34/PPPkZiY2Od+ixcvRn19vWl57bXXhiDagfvb3/6GtLQ0vPTSS1CpVJg8eTJmzpzZ5YuYnQ4fPoy5c+fi6aefxokTJxAXF4e4uDh88cUXQxz54LnZNgBufPDqh//eGo1mCCMefNeuXcPkyZPx5ptv9qv+N998g0ceeQQxMTFQq9VYvnw5fvvb3+LTTz+1cKSWc7Nt0KmmpsbsXvDw8LBQhJZ18OBBPPfcc6isrERpaSna29vx85//HNeuXetxH1v7PbiVNgBs6/dgzJgxyMnJwfHjx3Hs2DH87Gc/wy9/+Ut8+eWX3da3tXsAuPk2AGzrHqBhRCBRnDx5UgAgHD161FT2ySefCBKJRPj222973O/BBx8UUlJShiDCwRcRESE899xzpnWj0Sh4e3sL2dnZ3daPj48XHnnkEbOyyMhIISkpyaJxWtLNtsHWrVsFhUIxRNENPQDCrl27eq3zu9/9Trj33nvNyn7zm98IM2fOtGBkQ6c/bVBWViYAEK5cuTIkMQ217777TgAgHDx4sMc6tvh78EP9aQNb/z0QBEEYOXKkUFhY2O02W78HOvXWBsPhHiDbxB4OkVRUVMDNzQ3h4eGmsunTp8POzg5VVVW97vvuu+/izjvvxH333YeMjAxcv37d0uEOWFtbG44fP47p06ebyuzs7DB9+nRUVFR0u09FRYVZfQCYOXNmj/Wt3a20AQBcvXoV48aNg6+vb5//82WLbO0+GIiQkBB4eXlhxowZKC8vFzucQdP5USulUtljHVu/D/rTBoDt/h4YjUYUFxfj2rVriIqK6raOrd8D/WkDwHbvAbJt/PCfSBoaGroMh7C3t4dSqex1XPYTTzyBcePGwdvbG//+97+Rnp6Ompoa7Ny509IhD8ilS5dgNBoxevRos/LRo0fjP//5T7f7NDQ0dFv/dh23fittEBgYiC1btiA4OBharRavv/46pk6dii+//BJjxowZirBF19N9oNPp0NLSAkdHR5EiGzpeXl54++23ER4eDr1ej8LCQkRHR6OqqgphYWFihzcgHR0dWL58OaZNm4b77ruvx3q29nvwQ/1tA1v8PaiurkZUVBRaW1vh7OyMXbt24Z577um2rq3eAzfTBrZ4D9DwwIRjkK1evRqvvvpqr3VOnTp1y8f/4RyPoKAgeHl54aGHHsLXX3+Nu+6665aPS9YpKirK7H+6pk6dikmTJuFPf/oTXn75ZREjo6EUGBiIwMBA0/rUqVPx9ddfIz8/H0VFRSJGNnDPPfccvvjiCxw6dEjsUETT3zawxd+DwMBAqNVqaLVa/OMf/8D8+fNx8ODBHh+4bdHNtIEt3gM0PDDhGGQrVqzAggULeq0zfvx4eHp6dpkobDAY0NjYCE9Pz36fLzIyEgBw5swZq0447rzzTkilUly8eNGs/OLFiz1er6en503Vt3a30gY/NmLECISGhuLMmTOWCNEq9XQfuLq6DovejZ5ERETc9g/pS5cuNb0wo6//nbW134NON9MGP2YLvwcymQwTJkwAANx///04evQoCgoK8Kc//alLXVu9B26mDX7MFu4BGh44h2OQjRo1ChMnTux1kclkiIqKQlNTE44fP27ad//+/ejo6DAlEf2hVqsB3BhyYc1kMhnuv/9+7Nu3z1TW0dGBffv29ThWNSoqyqw+AJSWlvY6ttWa3Uob/JjRaER1dbXV/3sPJlu7DwaLWq2+be8DQRCwdOlS7Nq1C/v374e/v3+f+9jafXArbfBjtvh70NHRAb1e3+02W7sHetJbG/yYLd4DZKPEnrU+nD388MNCaGioUFVVJRw6dEgICAgQ5s6da9p+/vx5ITAwUKiqqhIEQRDOnDkjZGVlCceOHRO++eYb4Z///Kcwfvx44YEHHhDrEm5KcXGxIJfLhW3btgknT54UEhMTBTc3N6GhoUEQBEF46qmnhNWrV5vql5eXC/b29sLrr78unDp1SnjppZeEESNGCNXV1WJdwoDdbBtkZmYKn376qfD1118Lx48fFx5//HHBwcFB+PLLL8W6hAFrbm4WTpw4IZw4cUIAIOTl5QknTpwQNBqNIAiCsHr1auGpp54y1T979qxwxx13CKtWrRJOnTolvPnmm4JUKhX27Nkj1iUM2M22QX5+vvDBBx8Ip0+fFqqrq4WUlBTBzs5O2Lt3r1iXMCBLliwRFAqFcODAAaG+vt60XL9+3VTH1n8PbqUNbO33YPXq1cLBgweFb775Rvj3v/8trF69WpBIJMJnn30mCILt3wOCcPNtYGv3AA0fTDhEdPnyZWHu3LmCs7Oz4OrqKixcuFBobm42bf/mm28EAEJZWZkgCIJQV1cnPPDAA4JSqRTkcrkwYcIEYdWqVYJWqxXpCm7ehg0bhLFjxwoymUyIiIgQKisrTdsefPBBYf78+Wb1//73vwt33323IJPJhHvvvVf46KOPhjjiwXczbbB8+XJT3dGjRwuxsbGCSqUSIerB0/mK1x8vndc9f/584cEHH+yyT0hIiCCTyYTx48cLW7duHfK4B9PNtsGrr74q3HXXXYKDg4OgVCqF6OhoYf/+/eIEPwi6u3YAZv+utv57cCttYGu/B4sWLRLGjRsnyGQyYdSoUcJDDz1ketAWBNu/BwTh5tvA1u4BGj4kgiAIQ9adQkREREREwwrncBARERERkcUw4SAiIiIiIothwkFERERERBbDhIOIiIiIiCyGCQcREREREVkMEw4iIiIiIrIYJhxERERERGQxTDiIiIiIiMhimHAQEVmZtWvXIiQk5Kb2kUgk+OCDDywSDxER0UAw4SAisiCJRNLrsnbt2i77rFy5Evv27Rv6YImIiCzAXuwAiIhsWX19venvv/3tb/j973+PmpoaU5mzs7Ppb0EQYDQa4ezsbFZORER0O2MPBxGRBXl6epoWhUIBiURiWv/Pf/4DFxcXfPLJJ7j//vshl8tx6NChLkOqjh49ihkzZuDOO++EQqHAgw8+CJVKJd5FERER3QQmHEREIlu9ejVycnJw6tQpBAcHd9ne3NyM+fPn49ChQ6isrERAQABiY2PR3NwsQrREREQ3h0OqiIhElpWVhRkzZvS4/Wc/+5nZ+jvvvAM3NzccPHgQv/jFLywdHhER0YCwh4OISGTh4eG9br948SIWL16MgIAAKBQKuLq64urVq6irqxuiCImIiG4deziIiETm5OTU6/b58+fj8uXLKCgowLhx4yCXyxEVFYW2trYhipCIiOjWMeEgIrJy5eXleOuttxAbGwsAOHfuHC5duiRyVERERP3DhIOIyMoFBASgqKgI4eHh0Ol0WLVqFRwdHcUOi4iIqF84h4OIyMpt3rwZV65cQVhYGJ566ikkJyfDw8ND7LCIiIj6RSIIgiB2EEREREREZJvYw0FERERERBbDhIOIiIiIiCyGCQcREREREVkMEw4iIiIiIrIYJhxERERERGQxTDiIiIiIiMhimHAQEREREZHFMOEgIiIiIiKLYcJBREREREQWw4SDiIiIiIgshgkHERERERFZDBMOIiIiIiKymP8P/GnihuQjruEAAAAASUVORK5CYII=", "text/plain": ["<Figure size 800x1372 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import TwoSlopeNorm\n", "\n", "def plot_mi_heatmap(\n", "    data,\n", "    *,\n", "    mi_col: str = \"modulation_index\",\n", "    unit_col: str = \"unit\",\n", "    vmax: float | None = None\n", "):\n", "    \"\"\"\n", "    Heatmap of modulation index (units × trials), diverging colormap:\n", "      blue < 0 < red, with 0 in white. NaNs shown in light gray.\n", "    Y-axis tick labels are the unit names from `unit_col`.\n", "    \"\"\"\n", "    mi_lists = data[mi_col].tolist()\n", "    n_units = len(mi_lists)\n", "    max_trials = max((len(m) for m in mi_lists), default=0)\n", "\n", "    # build matrix (units x trials), pad with NaN\n", "    M = np.full((n_units, max_trials), np.nan, dtype=float)\n", "    for i, mis in enumerate(mi_lists):\n", "        M[i, :len(mis)] = np.asarray(mis, dtype=float)\n", "\n", "    # symmetric normalization around 0\n", "    if vmax is None:\n", "        finite = M[np.isfinite(M)]\n", "        abs_max = np.max(np.abs(finite)) if finite.size else 1.0\n", "    else:\n", "        abs_max = float(vmax)\n", "    norm = TwoSlopeNorm(vmin=-abs_max, vcenter=0.0, vmax=+abs_max)\n", "\n", "    # diverging colormap with white center; gray for NaN\n", "    cmap = plt.get_cmap(\"bwr\").copy()\n", "    cmap.set_bad(\"0.85\", alpha=1.0)  # NaNs as light gray\n", "\n", "    # make the figure tall enough for labels\n", "    fig_h = max(4, 0.28 * n_units)\n", "    plt.figure(figsize=(8, fig_h))\n", "    im = plt.imshow(M, aspect=\"auto\", interpolation=\"nearest\", cmap=cmap, norm=norm)\n", "    cbar = plt.colorbar(im)\n", "    cbar.set_label(\"Modulation index\")\n", "\n", "    # x/y labels and ticks\n", "    plt.xlabel(\"Trial\")\n", "    plt.ylabel(\"Unit\")\n", "\n", "    # unit names as yticklabels\n", "    units = data[unit_col].astype(str).tolist() if unit_col in data.columns else [str(i) for i in range(n_units)]\n", "    plt.yticks(np.arange(n_units), units)\n", "\n", "    plt.title(\"Modulation index (units × trials)\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "plot_mi_heatmap(data)\n"]}, {"cell_type": "code", "execution_count": 33, "id": "65e93d2d", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'unit_class'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\base.py:3802\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key, method, tolerance)\u001b[0m\n\u001b[0;32m   3801\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3802\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3803\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\index.pyx:138\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\index.pyx:165\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:5745\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:5753\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON>eyError\u001b[0m: 'unit_class'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[33], line 11\u001b[0m\n\u001b[0;32m      4\u001b[0m class_colors \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mexcited\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m#1b9e77\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124minhibited\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m#d95f02\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m      7\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmixed/non-responsive\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m#808080\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m      8\u001b[0m }\n\u001b[0;32m     10\u001b[0m \u001b[38;5;66;03m# counts across the whole dataset\u001b[39;00m\n\u001b[1;32m---> 11\u001b[0m counts \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43munit_class\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mvalue_counts()\u001b[38;5;241m.\u001b[39mreindex(class_order)\u001b[38;5;241m.\u001b[39mfillna(\u001b[38;5;241m0\u001b[39m)\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mint\u001b[39m)\n\u001b[0;32m     12\u001b[0m total \u001b[38;5;241m=\u001b[39m counts\u001b[38;5;241m.\u001b[39msum()\n\u001b[0;32m     14\u001b[0m \u001b[38;5;66;03m# labels with n values relative to total\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\frame.py:3807\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3805\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   3806\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3807\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3808\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3809\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\base.py:3804\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key, method, tolerance)\u001b[0m\n\u001b[0;32m   3802\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[0;32m   3803\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m-> 3804\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3805\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3806\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3808\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3809\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON>eyError\u001b[0m: 'unit_class'"]}], "source": ["import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {\n", "    'excited': '#1b9e77',\n", "    'inhibited': '#d95f02',\n", "    'mixed/non-responsive': '#808080'\n", "}\n", "\n", "# counts across the whole dataset\n", "counts = data['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "total = counts.sum()\n", "\n", "# labels with n values relative to total\n", "labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]\n", "colors = [class_colors[c] for c in class_order]\n", "\n", "fig, ax = plt.subplots(figsize=(5, 5))\n", "ax.pie(\n", "    counts.values, labels=labels, colors=colors,\n", "    autopct=lambda p: f'{p:.1f}%' if p > 0 else '', startangle=90, counterclock=False,\n", "    wedgeprops=dict(linewidth=1, edgecolor='white')\n", ")\n", "ax.set_title(f'Response classification (total n={total})')\n", "ax.axis('equal')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "bf01c1b5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Side-by-side pies by recording (b24 vs b25)\n", "import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {'excited': '#1b9e77', 'inhibited': '#d95f02', 'mixed/non-responsive': '#808080'}\n", "subsets = {k: v.copy() for k, v in data.groupby('animal_id')}\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(10, 5))\n", "for ax, animal in zip(axes, ['b24', 'b25']):\n", "    df_sub = subsets.get(animal, None)\n", "    if df_sub is None or df_sub.empty:\n", "        ax.axis('off')\n", "        ax.set_title(f'{animal} (no data)')\n", "        continue\n", "\n", "    counts = df_sub['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "    total = int(counts.sum())\n", "\n", "    labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]\n", "    colors = [class_colors[c] for c in class_order]\n", "\n", "    _ = ax.pie(\n", "        counts.values,\n", "        labels=labels,\n", "        colors=colors,\n", "        autopct=lambda p: f'{p:.1f}%' if p > 0 else '',\n", "        startangle=90,\n", "        counterclock=False,\n", "        wedgeprops=dict(linewidth=1, edgecolor='white')\n", "    )\n", "    ax.set_title(f'{animal} (total n={total})')\n", "    ax.axis('equal')\n", "\n", "fig.tight_layout()\n", "fig.savefig('unit_classification_by_recording.png', dpi=200, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "ac178da3", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "unit", "rawType": "object", "type": "string"}, {"name": "modulated_trial", "rawType": "object", "type": "string"}, {"name": "unit_class", "rawType": "object", "type": "string"}], "ref": "168b0e62-8e86-47b0-9906-0db3a9f834a0", "rows": [["0", "b24_0", "[-1, -1, 0, 0]", "mixed/non-responsive"], ["1", "b24_3", "[1, 1, 0, 1]", "excited"], ["2", "b24_6", "[-1, -1, 0, -1]", "inhibited"], ["3", "b24_9", "[1, 0, 0, -1]", "mixed/non-responsive"], ["4", "b24_12", "[1, 1, 0, 1]", "excited"], ["5", "b24_14", "[1, 1, 1, 1]", "excited"], ["6", "b24_17", "[1, 1, 0, 1]", "excited"], ["7", "b24_18", "[-1, -1, -1, 0]", "inhibited"], ["8", "b24_20", "[-1, -1, 0, -1]", "inhibited"], ["9", "b24_25", "[1, 1, 0, 1]", "excited"], ["10", "b24_27", "[1, 1, 0, 1]", "excited"], ["11", "b24_29", "[1, 1, 0, 1]", "excited"], ["12", "b24_33", "[-1, -1, 1, -1]", "inhibited"], ["13", "b24_35", "[-1, -1, 0, -1]", "inhibited"], ["14", "b24_39", "[1, 1, 0, -1]", "mixed/non-responsive"], ["15", "b24_50", "[0, 1, 0, 1]", "mixed/non-responsive"], ["16", "b24_66", "[1, 1, -1, -1]", "mixed/non-responsive"], ["17", "b24_71", "[-1, 1, 1, 1]", "excited"], ["18", "b24_83", "[-1, -1, 0, -1]", "inhibited"], ["19", "b24_91", "[-1, -1, -1, -1]", "inhibited"], ["20", "b24_114", "[-1, -1, 0, -1]", "inhibited"], ["21", "b24_121", "[-1, -1, 0, -1]", "inhibited"], ["22", "b24_169", "[1, 1, 1, -1]", "excited"], ["23", "b24_1", "[-1, -1, 0, -1]", "inhibited"], ["24", "b24_4", "[-1, -1, 0, -1]", "inhibited"], ["25", "b24_5", "[1, 1, 0, 1]", "excited"], ["26", "b24_11", "[0, -1, 0, -1]", "mixed/non-responsive"], ["27", "b24_13", "[1, 1, 0, 1]", "excited"], ["28", "b24_15", "[0, 1, -1, 1]", "mixed/non-responsive"], ["29", "b24_24", "[1, 1, 0, 1]", "excited"], ["30", "b24_28", "[1, 1, 1, 1]", "excited"], ["31", "b24_30", "[1, 0, 0, 1]", "mixed/non-responsive"], ["32", "b24_32", "[1, 1, 0, 1]", "excited"], ["33", "b24_34", "[1, 1, 0, 1]", "excited"], ["34", "b24_41", "[0, 1, 1, 0]", "mixed/non-responsive"], ["35", "b25_36", "[-1, -1, -1, -1, 0, 0]", "inhibited"], ["36", "b25_39", "[-1, -1, 0, -1, 0, 0]", "mixed/non-responsive"], ["37", "b25_40", "[-1, -1, -1, -1, 0, 0]", "inhibited"], ["38", "b25_51", "[-1, -1, -1, -1, 0, 0]", "inhibited"], ["39", "b25_63", "[-1, -1, -1, 0, 0, 0]", "mixed/non-responsive"], ["40", "b25_107", "[1, 1, -1, 0, -1, 0]", "mixed/non-responsive"], ["41", "b25_113", "[1, -1, -1, -1, -1, 0]", "inhibited"], ["42", "b25_114", "[1, 1, -1, -1, -1, 0]", "mixed/non-responsive"], ["43", "b25_127", "[-1, -1, -1, -1, -1, 0]", "inhibited"], ["44", "b25_153", "[-1, -1, -1, -1, -1, 0]", "inhibited"], ["45", "b25_156", "[0, -1, -1, -1, -1, 0]", "inhibited"], ["46", "b25_160", "[1, 1, 1, 1, -1, 0]", "excited"], ["47", "b25_163", "[1, 1, 1, 1, -1, 0]", "excited"], ["48", "b25_167", "[0, 1, -1, -1, -1, 0]", "mixed/non-responsive"]], "shape": {"columns": 3, "rows": 49}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>unit</th>\n", "      <th>modulated_trial</th>\n", "      <th>unit_class</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>b24_0</td>\n", "      <td>[-1, -1, 0, 0]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>b24_3</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>b24_6</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>b24_9</td>\n", "      <td>[1, 0, 0, -1]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>b24_12</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>b24_14</td>\n", "      <td>[1, 1, 1, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>b24_17</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>b24_18</td>\n", "      <td>[-1, -1, -1, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>b24_20</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>b24_25</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>b24_27</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>b24_29</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>b24_33</td>\n", "      <td>[-1, -1, 1, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>b24_35</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>b24_39</td>\n", "      <td>[1, 1, 0, -1]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>b24_50</td>\n", "      <td>[0, 1, 0, 1]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>b24_66</td>\n", "      <td>[1, 1, -1, -1]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>b24_71</td>\n", "      <td>[-1, 1, 1, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>b24_83</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>b24_91</td>\n", "      <td>[-1, -1, -1, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>b24_114</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>b24_121</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>b24_169</td>\n", "      <td>[1, 1, 1, -1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>b24_1</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>b24_4</td>\n", "      <td>[-1, -1, 0, -1]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>b24_5</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>b24_11</td>\n", "      <td>[0, -1, 0, -1]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>b24_13</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>b24_15</td>\n", "      <td>[0, 1, -1, 1]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>b24_24</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>b24_28</td>\n", "      <td>[1, 1, 1, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>b24_30</td>\n", "      <td>[1, 0, 0, 1]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>b24_32</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>b24_34</td>\n", "      <td>[1, 1, 0, 1]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>b24_41</td>\n", "      <td>[0, 1, 1, 0]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>b25_36</td>\n", "      <td>[-1, -1, -1, -1, 0, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>b25_39</td>\n", "      <td>[-1, -1, 0, -1, 0, 0]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>b25_40</td>\n", "      <td>[-1, -1, -1, -1, 0, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>b25_51</td>\n", "      <td>[-1, -1, -1, -1, 0, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>b25_63</td>\n", "      <td>[-1, -1, -1, 0, 0, 0]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>b25_107</td>\n", "      <td>[1, 1, -1, 0, -1, 0]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>b25_113</td>\n", "      <td>[1, -1, -1, -1, -1, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>b25_114</td>\n", "      <td>[1, 1, -1, -1, -1, 0]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>b25_127</td>\n", "      <td>[-1, -1, -1, -1, -1, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>b25_153</td>\n", "      <td>[-1, -1, -1, -1, -1, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>b25_156</td>\n", "      <td>[0, -1, -1, -1, -1, 0]</td>\n", "      <td>inhibited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>b25_160</td>\n", "      <td>[1, 1, 1, 1, -1, 0]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>b25_163</td>\n", "      <td>[1, 1, 1, 1, -1, 0]</td>\n", "      <td>excited</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>b25_167</td>\n", "      <td>[0, 1, -1, -1, -1, 0]</td>\n", "      <td>mixed/non-responsive</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       unit          modulated_trial            unit_class\n", "0     b24_0           [-1, -1, 0, 0]  mixed/non-responsive\n", "1     b24_3             [1, 1, 0, 1]               excited\n", "2     b24_6          [-1, -1, 0, -1]             inhibited\n", "3     b24_9            [1, 0, 0, -1]  mixed/non-responsive\n", "4    b24_12             [1, 1, 0, 1]               excited\n", "5    b24_14             [1, 1, 1, 1]               excited\n", "6    b24_17             [1, 1, 0, 1]               excited\n", "7    b24_18          [-1, -1, -1, 0]             inhibited\n", "8    b24_20          [-1, -1, 0, -1]             inhibited\n", "9    b24_25             [1, 1, 0, 1]               excited\n", "10   b24_27             [1, 1, 0, 1]               excited\n", "11   b24_29             [1, 1, 0, 1]               excited\n", "12   b24_33          [-1, -1, 1, -1]             inhibited\n", "13   b24_35          [-1, -1, 0, -1]             inhibited\n", "14   b24_39            [1, 1, 0, -1]  mixed/non-responsive\n", "15   b24_50             [0, 1, 0, 1]  mixed/non-responsive\n", "16   b24_66           [1, 1, -1, -1]  mixed/non-responsive\n", "17   b24_71            [-1, 1, 1, 1]               excited\n", "18   b24_83          [-1, -1, 0, -1]             inhibited\n", "19   b24_91         [-1, -1, -1, -1]             inhibited\n", "20  b24_114          [-1, -1, 0, -1]             inhibited\n", "21  b24_121          [-1, -1, 0, -1]             inhibited\n", "22  b24_169            [1, 1, 1, -1]               excited\n", "23    b24_1          [-1, -1, 0, -1]             inhibited\n", "24    b24_4          [-1, -1, 0, -1]             inhibited\n", "25    b24_5             [1, 1, 0, 1]               excited\n", "26   b24_11           [0, -1, 0, -1]  mixed/non-responsive\n", "27   b24_13             [1, 1, 0, 1]               excited\n", "28   b24_15            [0, 1, -1, 1]  mixed/non-responsive\n", "29   b24_24             [1, 1, 0, 1]               excited\n", "30   b24_28             [1, 1, 1, 1]               excited\n", "31   b24_30             [1, 0, 0, 1]  mixed/non-responsive\n", "32   b24_32             [1, 1, 0, 1]               excited\n", "33   b24_34             [1, 1, 0, 1]               excited\n", "34   b24_41             [0, 1, 1, 0]  mixed/non-responsive\n", "35   b25_36   [-1, -1, -1, -1, 0, 0]             inhibited\n", "36   b25_39    [-1, -1, 0, -1, 0, 0]  mixed/non-responsive\n", "37   b25_40   [-1, -1, -1, -1, 0, 0]             inhibited\n", "38   b25_51   [-1, -1, -1, -1, 0, 0]             inhibited\n", "39   b25_63    [-1, -1, -1, 0, 0, 0]  mixed/non-responsive\n", "40  b25_107     [1, 1, -1, 0, -1, 0]  mixed/non-responsive\n", "41  b25_113   [1, -1, -1, -1, -1, 0]             inhibited\n", "42  b25_114    [1, 1, -1, -1, -1, 0]  mixed/non-responsive\n", "43  b25_127  [-1, -1, -1, -1, -1, 0]             inhibited\n", "44  b25_153  [-1, -1, -1, -1, -1, 0]             inhibited\n", "45  b25_156   [0, -1, -1, -1, -1, 0]             inhibited\n", "46  b25_160      [1, 1, 1, 1, -1, 0]               excited\n", "47  b25_163      [1, 1, 1, 1, -1, 0]               excited\n", "48  b25_167    [0, 1, -1, -1, -1, 0]  mixed/non-responsive"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data[['unit', 'modulated_trial', 'unit_class']]"]}, {"cell_type": "markdown", "id": "ebe6c2be", "metadata": {}, "source": ["## Composite plots per recording with unit-class borders\n", "We generate per-recording composite grids of firing rates with balloon intervals.\n", "Plot boxes are outlined by unit class: red=excited, blue=inhibited, green=non-responsive.\n"]}, {"cell_type": "code", "execution_count": null, "id": "4887e273", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_units_grid_with_shading\n", "\n", "# Build a mapping from unit id -> unit class for border coloring\n", "unit_class_map = dict(zip(data['unit'], data['unit_class']))\n", "\n", "# Create one composite figure per recording (animal_id)\n", "grid_params = dict(\n", "    bin_size=10, t_start=0, t_end=None, n_cols=4,\n", "    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,\n", "    margins=(0.06, 0.92, 0.08, 0.9), dpi=200\n", ")\n", "for animal, df_sub in data.groupby('animal_id'):\n", "    out_file = f'{animal}_modulation_units_grid.png'\n", "    suptitle = f'{animal}: FR with balloon intervals'\n", "    _ = plot_units_grid_with_shading(\n", "        df_sub,\n", "        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],\n", "        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],\n", "        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],\n", "        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],\n", "        unit_class_map=unit_class_map, unit_col_name='unit'\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}