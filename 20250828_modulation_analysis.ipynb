{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory Analysis: b24, b25, b27, b28 datasets\n", "\n", "This notebook loads unit data for two specific recordings (b24 and b25), inspects basic structure, and optionally opens the standalone time-series viewer for interactive visualization with balloon event overlays."]}, {"cell_type": "code", "execution_count": 1, "id": "2ad46552", "metadata": {}, "outputs": [], "source": ["# Imports and dataset paths\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "from typing import Tuple, Sequence, Union, Dict, Any, Optional, List"]}, {"cell_type": "markdown", "id": "d414ab77", "metadata": {}, "source": ["## Basic info and sample rows\n", "We print number of units, columns and dtypes, and show a small sample for each dataset."]}, {"cell_type": "code", "execution_count": 2, "id": "72fc0a02", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 126 rows (units) from: ['b24', 'b25', 'b27', 'b28']\n"]}], "source": ["paths = {'b24': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\\unit_summary.pkl'),\n", "         'b25': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\\unit_summary.pkl'),\n", "         'b27': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0\\catgt\\unit_summary.pkl'),\n", "         'b28': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p1_r2_g0\\catgt\\unit_summary.pkl')\n", "         }\n", "dfs = []\n", "for name, p in paths.items():\n", "    if not p.exists(): print(f\"⚠ Missing: {p}\"); continue\n", "    df = pd.read_pickle(p).copy(); dfs.append(df)\n", "data = pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()\n", "print(f\"Loaded {len(data)} rows (units) from: {[d for d in paths if (paths[d]).exists()]}\")\n", "\n", "data['unit'] = data['animal_id'] + '_' + data['unit'].astype(str)\n", "data.drop(columns=['spike_times_samples', 'peak_channel', 'depth'], inplace=True)"]}, {"cell_type": "code", "execution_count": 3, "id": "468d9daf", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "data[\"balloon_off_sec\"] = data[\"balloon_off_sec\"].apply(\n", "    lambda offs: [[max(e - 200.0, 0.0), max(e, 0.0)] if np.isfinite(e) else [s, e] for s, e in offs]\n", ")"]}, {"cell_type": "markdown", "id": "7c905542", "metadata": {}, "source": ["## Compute modulation per unit and trial\n", "This section applies the imported functions to the loaded DataFrame.\n", "- Observed MI: ON vs OFF-tail per trial using balloon_off_sec_tail (last 60 s of OFF).\n", "- Null MI: per OFF trial, compare whole OFF vs random subwindows inside OFF."]}, {"cell_type": "code", "execution_count": 4, "id": "9601c9e7", "metadata": {}, "outputs": [], "source": ["# Apply functions to DataFrame\n", "\n", "from modulation_functions import (\n", "    modulation_index_single_trial,\n", "    compute_trialwise_mi,\n", "    compute_trialwise_mi_null,\n", ")\n", "\n", "data['modulation_index'] = data.apply(compute_trialwise_mi, axis=1)\n", "\n", "data['modulation_index_null'] = data.apply(\n", "    lambda r: compute_trialwise_mi_null(\n", "        r,\n", "        window_len=40,            # samples\n", "        n_iter=10000,              # how many times to resample the OFF baseline\n", "        mi_formula='difference_over_sum',\n", "        seed=42,                  # set for reproducibility; or None\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "markdown", "id": "mod-flag-intro", "metadata": {}, "source": ["## Modulation flag (per trial)\n", "We classify each trial as enhanced (+1), suppressed (−1), or not modulated (0)\n", "by comparing the observed MI against its per-trial null distribution.\n", "Ties and invalid/null cases return 0.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "mod-flag-import", "metadata": {}, "outputs": [], "source": ["from modulation_functions import modulation_flag, compute_trialwise_modulation_flags\n", "\n", "# Add the -1/0/1 flags per trial\n", "data['modulated_trial'] = data.apply(\n", "    lambda r: compute_trialwise_modulation_flags(r, lower_quantile=0.1, upper_quantile=0.9),\n", "    axis=1\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "92482e9d", "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 4410x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import TwoSlopeNorm\n", "\n", "def plot_mi_heatmap(\n", "    data,\n", "    *,\n", "    mi_col: str = \"modulation_index\",\n", "    unit_col: str = \"unit\",\n", "    vmax: float | None = None\n", "):\n", "    \"\"\"\n", "    Heatmap of modulation index (trials × units), diverging colormap:\n", "      blue < 0 < red, with 0 in white. NaNs shown in light gray.\n", "\n", "    X-axis: Unit id (unit names from `unit_col`)\n", "    Y-axis: trial1, trial2, ...\n", "    \"\"\"\n", "    # Collect MI lists per unit\n", "    mi_lists = data[mi_col].tolist()\n", "    n_units = len(mi_lists)\n", "    max_trials = max((len(m) for m in mi_lists), default=0)\n", "\n", "    # Build matrix (units x trials), then transpose to (trials x units)\n", "    M = np.full((n_units, max_trials), np.nan, dtype=float)\n", "    for i, mis in enumerate(mi_lists):\n", "        M[i, :len(mis)] = np.asarray(mis, dtype=float)\n", "    M = M.T  # now shape = (max_trials, n_units): rows=trials, cols=units\n", "\n", "    # Symmetric normalization around 0\n", "    if vmax is None:\n", "        finite = M[np.isfinite(M)]\n", "        abs_max = np.max(np.abs(finite)) if finite.size else 1.0\n", "    else:\n", "        abs_max = float(vmax)\n", "    norm = TwoSlopeNorm(vmin=-abs_max, vcenter=0.0, vmax=+abs_max)\n", "\n", "    # Diverging colormap with white center; gray for NaN\n", "    cmap = plt.get_cmap(\"bwr\").copy()\n", "    cmap.set_bad(\"0.85\", alpha=1.0)  # NaNs as light gray\n", "\n", "    # Figure size: wide for many units, tall for many trials\n", "    fig_w = max(6, 0.35 * n_units)\n", "    fig_h = max(4, 0.28 * max_trials)\n", "\n", "    fig, ax = plt.subplots(figsize=(fig_w, fig_h))\n", "    im = ax.imshow(M, aspect=\"auto\", interpolation=\"nearest\", cmap=cmap, norm=norm)\n", "\n", "    # Colorbar\n", "    cbar = fig.colorbar(im, ax=ax)\n", "    cbar.set_label(\"Modulation index\")\n", "\n", "    # Tick labels\n", "    # X: units\n", "    units = data[unit_col].astype(str).tolist() if unit_col in data.columns else [str(i) for i in range(n_units)]\n", "    ax.set_xticks(np.arange(n_units))\n", "    ax.set_xticklabels(units, rotation=90, ha=\"center\", va=\"top\")\n", "\n", "    # Y: trials as trial1, trial2, ...\n", "    ax.set_yticks(np.arange(max_trials))\n", "    ax.set_yticklabels([f\"trial{i+1}\" for i in range(max_trials)])\n", "    ax.invert_yaxis()  # put trial1 at the top\n", "\n", "    # Axis labels & title\n", "    ax.set_xlabel(\"Unit id\")\n", "    ax.set_ylabel(\"(Trial #)\")\n", "\n", "    fig.tight_layout()\n", "    plt.show()\n", "\n", "# usage:\n", "plot_mi_heatmap(data)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "2d7f6fcf", "metadata": {}, "outputs": [], "source": ["def classify_unit(modulated_trial):\n", "    n = len(modulated_trial)\n", "    n_pos = sum(x == 1 for x in modulated_trial)\n", "    n_neg = sum(x == -1 for x in modulated_trial)\n", "\n", "    if n_pos >= n / 2 and n_neg == 0:\n", "        return \"excited\"\n", "    elif n_neg >= n / 2 and n_pos == 0:\n", "        return \"inhibited\"\n", "    else:\n", "        return \"mixed/non-responsive\"\n", "\n", "data['unit_class'] = data['modulated_trial'].apply(classify_unit)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "65e93d2d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {\n", "    'excited': '#1b9e77',\n", "    'inhibited': '#d95f02',\n", "    'mixed/non-responsive': '#808080'\n", "}\n", "\n", "# counts across the whole dataset\n", "counts = data['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "total = counts.sum()\n", "\n", "# labels with n values relative to total\n", "labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]\n", "colors = [class_colors[c] for c in class_order]\n", "\n", "fig, ax = plt.subplots(figsize=(5, 5))\n", "ax.pie(\n", "    counts.values, labels=labels, colors=colors,\n", "    autopct=lambda p: f'{p:.1f}%' if p > 0 else '', startangle=90, counterclock=False,\n", "    wedgeprops=dict(linewidth=1, edgecolor='white')\n", ")\n", "ax.set_title(f'Response classification (total n={total})')\n", "ax.axis('equal')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 9, "id": "bf01c1b5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {'excited': '#1b9e77', 'inhibited': '#d95f02', 'mixed/non-responsive': '#808080'}\n", "subsets = {k: v.copy() for k, v in data.groupby('animal_id')}\n", "\n", "animals = ['b24', 'b25', 'b27', 'b28']\n", "\n", "fig, axes = plt.subplots(1, 4, figsize=(16, 4))  # 1x4 layout\n", "axes = axes.flatten()\n", "\n", "for ax, animal in zip(axes, animals):\n", "    df_sub = subsets.get(animal, None)\n", "    if df_sub is None or df_sub.empty:\n", "        ax.axis('off')\n", "        ax.set_title(f'{animal} (no data)', pad=2)\n", "        continue\n", "\n", "    counts = df_sub['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "    total = int(counts.sum())\n", "\n", "    # labels for legend\n", "    labels = [f'{cls}\\n(n={counts[cls]}/{total})' for cls in class_order]  # split in multiple lines\n", "    colors = [class_colors[c] for c in class_order]\n", "\n", "    wedges, texts, autotexts = ax.pie(\n", "        counts.values,\n", "        colors=colors,\n", "        autopct=lambda p: f'{p:.1f}%' if p > 0 else '',\n", "        startangle=90,\n", "        counterclock=False,\n", "        wedgeprops=dict(linewidth=1, edgecolor='white'),\n", "        textprops={'fontsize': 8}  # smaller %\n", "    )\n", "\n", "    # legend outside\n", "    ax.legend(\n", "        wedges, labels,\n", "        loc='upper center',\n", "        bbox_to_anchor=(0.5, -0.05),\n", "        fontsize=7,  # smaller font\n", "        ncol=1,\n", "        frameon=False\n", "    )\n", "\n", "    ax.set_title(f'{animal} (n={total} units)', pad=2)\n", "    ax.axis('equal')\n", "\n", "fig.tight_layout()\n", "fig.savefig('unit_classification_by_recording.png', dpi=200, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "ebe6c2be", "metadata": {}, "source": ["## Composite plots per recording with unit-class borders\n", "We generate per-recording composite grids of firing rates with balloon intervals.\n", "Plot boxes are outlined by unit class: red=excited, blue=inhibited, green=non-responsive.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "4887e273", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_units_grid_with_shading\n", "\n", "# Build a mapping from unit id -> unit class for border coloring\n", "unit_class_map = dict(zip(data['unit'], data['unit_class']))\n", "\n", "# Create one composite figure per recording (animal_id)\n", "grid_params = dict(\n", "    bin_size=10, t_start=0, t_end=None, n_cols=4,\n", "    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,\n", "    margins=(0.06, 0.92, 0.08, 0.9), dpi=200\n", ")\n", "for animal, df_sub in data.groupby('animal_id'):\n", "    out_file = f'{animal}_modulation_units_grid.png'\n", "    suptitle = f'{animal}: FR with balloon intervals'\n", "    _ = plot_units_grid_with_shading(\n", "        df_sub,\n", "        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],\n", "        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],\n", "        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],\n", "        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],\n", "        unit_class_map=unit_class_map, unit_col_name='unit'\n", "    )"]}, {"cell_type": "code", "execution_count": 11, "id": "eb7c86b8", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "animal_id", "rawType": "object", "type": "string"}, {"name": "excited", "rawType": "int64", "type": "integer"}, {"name": "inhibited", "rawType": "int64", "type": "integer"}, {"name": "mixed/non-responsive", "rawType": "int64", "type": "integer"}], "ref": "db142820-2807-41e0-b377-adc78103f67b", "rows": [["b24", "15", "11", "9"], ["b25", "2", "8", "4"], ["b27", "10", "6", "34"], ["b28", "1", "3", "23"]], "shape": {"columns": 3, "rows": 4}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>unit_class</th>\n", "      <th>excited</th>\n", "      <th>inhibited</th>\n", "      <th>mixed/non-responsive</th>\n", "    </tr>\n", "    <tr>\n", "      <th>animal_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>b24</th>\n", "      <td>15</td>\n", "      <td>11</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b25</th>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b27</th>\n", "      <td>10</td>\n", "      <td>6</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b28</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["unit_class  excited  inhibited  mixed/non-responsive\n", "animal_id                                           \n", "b24              15         11                     9\n", "b25               2          8                     4\n", "b27              10          6                    34\n", "b28               1          3                    23"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# show a table how many excited inhibited and mixed units we have per animal_id, and summarize, with percentages\n", "\n", "data.groupby(['animal_id', 'unit_class']).size().unstack(fill_value=0)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "65acb127", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import math\n", "from matplotlib import pyplot as plt\n", "\n", "def _get_time_vec(tv):\n", "    if tv is None:\n", "        return lambda i: None\n", "    if isinstance(tv, np.ndarray) and tv.ndim == 1 and tv.dtype != object:\n", "        return lambda i: tv  # shared\n", "    if hasattr(tv, \"iloc\"):  # pandas Series\n", "        return lambda i: tv.iloc[i]\n", "    return lambda i: tv[i]  # list / object array\n", "\n", "def _series_to_list(x):\n", "    return x.tolist() if hasattr(x, \"tolist\") else list(x)\n", "\n", "def plot_recording_waveforms(\n", "    rec_name, rec_data, \n", "    max_cols=8, figsize_per_subplot=(1.5, 1.8),\n", "    save=True, outdir=\".\"\n", "):\n", "    # Accept DataFrame slice or dict\n", "    if hasattr(rec_data, \"__getitem__\") and not isinstance(rec_data, dict):\n", "        if len(rec_data) == 0:\n", "            print(f\"[skip] {rec_name}: no units found.\")\n", "            return\n", "        wf = rec_data[\"waveform_mean\"]\n", "        waveforms = _series_to_list(wf)\n", "        units = _series_to_list(rec_data[\"unit\"]) if \"unit\" in rec_data else None\n", "        tv_col = rec_data[\"waveform_time_vector_ms\"] if \"waveform_time_vector_ms\" in rec_data else None\n", "    else:\n", "        if rec_data is None:\n", "            print(f\"[skip] {rec_name}: data is None.\")\n", "            return\n", "        wf = rec_data.get(\"waveform_mean\", [])\n", "        if isinstance(wf, np.ndarray) and wf.ndim == 2:\n", "            waveforms = [wf[i, :] for i in range(wf.shape[0])]\n", "        else:\n", "            waveforms = list(wf)\n", "        units = rec_data.get(\"unit\", None)\n", "        tv_col = rec_data.get(\"waveform_time_vector_ms\", None)\n", "\n", "    n_units = len(waveforms)\n", "    if n_units == 0:\n", "        print(f\"[skip] {rec_name}: no units found.\")\n", "        return\n", "\n", "    tv_get = _get_time_vec(tv_col)\n", "\n", "    # Grid\n", "    ncols = min(max_cols, max(1, int(math.ceil(math.sqrt(n_units)))))\n", "    nrows = int(math.ceil(n_units / ncols))\n", "\n", "    fig_w = figsize_per_subplot[0] * ncols\n", "    fig_h = figsize_per_subplot[1] * nrows\n", "    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(fig_w, fig_h))\n", "\n", "    if not isinstance(axes, np.ndarray):\n", "        axes = np.array([axes])\n", "    axes = axes.reshape(nrows, ncols)\n", "\n", "    for i, ax in enumerate(axes.flat):\n", "        if i < n_units:\n", "            y = np.asarray(waveforms[i]).ravel()\n", "            x = tv_get(i)\n", "            if x is None:\n", "                x = np.arange(len(y))\n", "            ax.plot(x, y, color=\"black\")\n", "            if units is not None and i < len(units):\n", "                ax.set_title(f\"Unit {units[i]}\", fontsize=7)\n", "            ax.set_xticks([]); ax.set_yticks([])\n", "            ax.axis(\"off\")\n", "        else:\n", "            ax.axis(\"off\")\n", "\n", "    # ---- Add 1 ms scalebar INSIDE the last subplot without changing limits ----\n", "    last_ax = axes.flat[min(n_units, len(axes.flat)) - 1]\n", "    # Save current limits (so the scalebar doesn't shrink the waveform)\n", "    xlim = last_ax.get_xlim()\n", "    ylim = last_ax.get_ylim()\n", "\n", "    xspan = xlim[1] - xlim[0]\n", "    yspan = ylim[1] - ylim[0]\n", "\n", "    # Desired bar length (1 ms in data units)\n", "    bar_len = 1.0  # ms\n", "    # If the axis window is <1 ms, fall back to 50% of the span\n", "    if bar_len > xspan:\n", "        bar_len = 0.5 * xspan\n", "\n", "    # Place near bottom-right with a small margin\n", "    x_margin = 0.05 * xspan\n", "    y_margin = 0.08 * yspan\n", "    x_end = xlim[1] - x_margin\n", "    x_start = x_end - bar_len\n", "    y_bar = ylim[0] + y_margin\n", "\n", "    # Draw the bar + label, then restore limits so autoscale doesn't change\n", "    last_ax.plot([x_start, x_end], [y_bar, y_bar], color=\"black\", lw=2, solid_capstyle=\"butt\", clip_on=False, zorder=5)\n", "    last_ax.text((x_start + x_end) / 2, y_bar, \"1 ms\", ha=\"center\", va=\"bottom\", fontsize=7)\n", "\n", "    last_ax.set_xlim(xlim)\n", "    last_ax.set_ylim(ylim)\n", "    # --------------------------------------------------------------------------\n", "\n", "    fig.suptitle(f\"Recording: {rec_name} — mean waveforms ({n_units} units)\", fontsize=10)\n", "    fig.tight_layout(rect=[0, 0, 1, 0.94])\n", "\n", "    if save:\n", "        plt.savefig(f\"{outdir}/waveforms_{rec_name}.png\", dpi=200)\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 13, "id": "2a3651be", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'plot_all_recordings' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[13], line 10\u001b[0m\n\u001b[0;32m      2\u001b[0m recordings \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m      3\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb24\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb24\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m      4\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb25\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb25\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb27\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb27\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb28\u001b[39m\u001b[38;5;124m\"\u001b[39m: data[data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manimal_id\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb28\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m      7\u001b[0m }\n\u001b[0;32m      9\u001b[0m \u001b[38;5;66;03m# Plot all of them\u001b[39;00m\n\u001b[1;32m---> 10\u001b[0m \u001b[43mplot_all_recordings\u001b[49m(recordings)\n", "\u001b[1;31mNameError\u001b[0m: name 'plot_all_recordings' is not defined"]}], "source": ["# Build dictionary of the recordings of interest\n", "recordings = {\n", "    \"b24\": data[data[\"animal_id\"] == \"b24\"],\n", "    \"b25\": data[data[\"animal_id\"] == \"b25\"],\n", "    \"b27\": data[data[\"animal_id\"] == \"b27\"],\n", "    \"b28\": data[data[\"animal_id\"] == \"b28\"],\n", "}\n", "\n", "# Plot all of them\n", "plot_all_recordings(recordings)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}