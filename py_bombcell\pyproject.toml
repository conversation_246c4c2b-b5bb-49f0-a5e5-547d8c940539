[build-system]
requires = ["setuptools>=61.0", "wheel"] 
build-backend = "setuptools.build_meta"

[project]
name = "bombcell"
version = "0.56"
description = "Python port of bombcell. Automated quality control, curation and neuron classification of spike-sorted electrophysiology data."
readme = "README.md"
license = {text = "GPL-3.0"}
authors = [
    {name = "<PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON><PERSON>"},
]
maintainers = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
]
dependencies = [
    "numpy>=1.19.0",
    "scipy>=1.7.0",
    "matplotlib>=3.3.0",
    "pandas>=1.3.0",
    "ipywidgets>=7.6.0",
    "joblib>=1.0.0",
    "tqdm>=4.50.0",
    "pyarrow>=5.0.0",
    "numba>=0.58.1",
    "upsetplot",
    "mtscomp",
    "cachecache",
]

[project.urls]
Homepage = "https://github.com/Julie-Fabre/bombcell"
Repository = "https://github.com/Julie-Fabre/bombcell"
Issues = "https://github.com/Julie-Fabre/bombcell/issues"

[tool.setuptools.packages.find]
where = ["."]

[tool.setuptools]
include-package-data = true
