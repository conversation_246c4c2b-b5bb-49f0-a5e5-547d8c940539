{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory Analysis: b24 and b25 datasets\n", "\n", "This notebook loads unit data for two specific recordings (b24 and b25), inspects basic structure, and optionally opens the standalone time-series viewer for interactive visualization with balloon event overlays."]}, {"cell_type": "code", "execution_count": 1, "id": "4be8d9da", "metadata": {}, "outputs": [], "source": ["# Imports and dataset paths\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "from IPython.display import display"]}, {"cell_type": "markdown", "id": "c94c4824", "metadata": {}, "source": ["## Basic info and sample rows\n", "We print number of units, columns and dtypes, and show a small sample for each dataset."]}, {"cell_type": "code", "execution_count": 2, "id": "61f5a0b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 49 rows from: ['b24', 'b25']\n"]}], "source": ["paths = {'b24': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\\unit_summary.pkl'),\n", "         'b25': Path(r'Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\\unit_summary.pkl')}\n", "dfs = []\n", "for name, p in paths.items():\n", "    if not p.exists(): print(f\"⚠ Missing: {p}\"); continue\n", "    df = pd.read_pickle(p).copy(); dfs.append(df)\n", "data = pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()\n", "print(f\"Loaded {len(data)} rows from: {[d for d in paths if (paths[d]).exists()]}\")\n", "\n", "data['unit'] = data['animal_id'] + '_' + data['unit'].astype(str)\n", "data.drop(columns=['spike_times_samples', 'peak_channel', 'depth', 'depth_penetration_um'], inplace=True)"]}, {"cell_type": "code", "execution_count": 3, "id": "56a368cd", "metadata": {}, "outputs": [], "source": ["data[\"balloon_off_sec\"] = data[\"balloon_off_sec\"].apply(\n", "    lambda offs: [[max(e - 150.0, 0.0), max(e, 0.0)] if np.isfinite(e) else [s, e] for s, e in offs]\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "49904533", "metadata": {}, "outputs": [], "source": ["# mask: units whose name starts with \"b25\"\n", "mask = data[\"unit\"].astype(str).str.startswith(\"b25\")\n", "\n", "def _filter_before_tmax(on_list, off_list, tmax=2400.0):\n", "    kept_on, kept_off = [], []\n", "    for on, off in zip(on_list, off_list):\n", "        on0, on1 = float(on[0]), float(on[1])\n", "        off0, off1 = float(off[0]), float(off[1])\n", "        if (on0 < tmax and on1 < tmax) and (off0 < tmax and off1 < tmax):\n", "            kept_on.append([on0, on1])   # keep list-of-lists format\n", "            kept_off.append([off0, off1])\n", "    return kept_on, kept_off\n", "\n", "filtered_pairs = data.loc[mask].apply(\n", "    lambda r: _filter_before_tmax(r[\"balloon_on_sec\"], r[\"balloon_off_sec\"], tmax=2400.0),\n", "    axis=1\n", ")\n", "\n", "data.loc[mask, \"balloon_on_sec\"]  = filtered_pairs.map(lambda x: x[0])\n", "data.loc[mask, \"balloon_off_sec\"] = filtered_pairs.map(lambda x: x[1])"]}, {"cell_type": "markdown", "id": "ca41f283", "metadata": {}, "source": ["### Plotting functions"]}, {"cell_type": "code", "execution_count": 5, "id": "11bbfbf1", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_each_unit_with_shading\n", "# plot_each_unit_with_shading(data, bin_size=15, t_start=0, t_end=None);\n"]}, {"cell_type": "code", "execution_count": 6, "id": "186d199e", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_units_grid_with_shading"]}, {"cell_type": "code", "execution_count": 8, "id": "dd84dd62", "metadata": {}, "outputs": [], "source": ["# Save per-recording composite grids (split by animal_id)\n", "params = dict(bin_size=10, t_start=0, t_end=None, n_cols=4, figsize_per_subplot=(4.5, 2), wspace=0.2, hspace=0.4, margins=(0.06, 0.92, 0.08, 0.9), dpi=200)\n", "for animal, df_sub in data.groupby('animal_id'):\n", "    out_file = f\"{animal}_units_grid.png\"\n", "    suptitle = f\"{animal}: firing rate with balloon intervals\"\n", "    _ = plot_units_grid_with_shading(\n", "        df_sub,\n", "        bin_size=params['bin_size'],\n", "        t_start=params['t_start'],\n", "        t_end=params['t_end'],\n", "        n_cols=params['n_cols'],\n", "        figsize_per_subplot=params['figsize_per_subplot'],\n", "        wspace=params['wspace'], hspace=params['hspace'],\n", "        margins=params['margins'],\n", "        suptitle=suptitle,\n", "        out_path=out_file,\n", "        dpi=params['dpi']\n", "    )\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}