clear; clc;

addpath(genpath('C:\Users\<USER>\Documents\GitHub\bombcell'));
addpath(genpath('C:\Users\<USER>\Documents\GitHub\npy-matlab'));

datasetPaths = {
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b12', 'b12_p1_r1_g0', 'b12_p1_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b12', 'b12_p2_r1_g0', 'b12_p2_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b13', 'b13_p1_r2_g0', 'b13_p1_r2_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b14', 'b14_p1_r1_g0', 'b14_p1_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b15', 'b15_p1_r1_g0', 'b15_p1_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b15', 'b15_p2_r1_g0', 'b15_p2_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b16', 'b16_p1_r1_g0', 'b16_p1_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b16', 'b16_p2_r1_g0', 'b16_p2_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b17', 'b17_p1_r1_g0', 'b17_p1_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b17', 'b17_p1_r2_g0', 'b17_p1_r2_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b18', 'b18_p1_r1_g0', 'b18_p1_r1_g0_imec0')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b24', 'b24_p1_r1_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b24', 'b24_p1_r1_g0_p', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b25', 'b25_p1_r1_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b25', 'b25_p1_r2_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b27', 'b27_p1_r1_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b27', 'b27_p1_r1_g0_p', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b28', 'b28_p1_r2_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b28', 'b28_p2_r1_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b24', 'b24_p1_r1_g0_p', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b29', 'b29_p1_r1_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'bal_npx', 'data', 'b30', 'b30_p1_r1_g0', 'catgt')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'co2_nts', 'data', 'd01', 'd01_p1_r1r2')
    fullfile('Z:', 'users', 'izouridis', 'projects', 'co2_nts', 'data', 'd02', 'd02_p1_r1_g0', 'catgt')
};

% ---- Parameters you want to pass ----
gainToUV = NaN;             % set to a number (e.g., 0.195) to override bitVolts->uV scaling; keep NaN to use meta
paramOverrides = struct( ...
    'minNumSpikes',           150, ...
    'minPresenceRatio',       0.3, ...
    'lratioMax',              0.5, ...
    'maxRPVviolations',       0.15, ...
    'maxPercSpikesMissing',   30, ...
    'minWvDuration',          80, ...
    'maxWvDuration',          1500, ...
    'maxWvBaselineFraction',  0.3, ...
    'splitGoodAndMua_NonSomatic', 0 ...
    ... % You can also force these if needed:
    ... % 'ephys_sample_rate', 30000, ...
    ... % 'nChannels', 385, ...
    ... % 'nSyncChannels', 1 ...
);

for i = numel(datasetPaths)
    ds = datasetPaths{i};
    fprintf('\n==== [%d/%d] %s ====\n', i, numel(datasetPaths), ds);
    try
        runBombcellQualityAssessment(ds, ...
            'SaveClusterKSLabel', true, ...
            'LaunchGUI', false, ...
            'GainToUV', gainToUV, ...
            'ParamOverrides', paramOverrides);
        fprintf('Done: %s\n', ds);
    catch ME
        fprintf(2, 'FAILED: %s\n  -> %s\n', ds, ME.message);
    end
end
