# bal_npx: Neuropixels Analysis Toolkit

bal_npx is a small, practical toolkit to support Neuropixels experiments and analysis around:
- Preparing Kilosort channel maps from SpikeGLX metadata
- Running Bombcell quality metrics on Kilosort results
- Registering probe tracks to an atlas (AP_histology) and extracting trajectory regions
- Extracting and plotting analog signals (e.g., balloon pressure) alongside unit firing rates
- Pulling unit-level features (spike times, waveforms, depths, firing rates)

It relies on a few external MATLAB toolboxes and common Python packages. This README gives you everything you need to install dependencies, run the main functions, and complete an end‑to‑end workflow.


## Dependencies and Setup

### MATLAB toolboxes
Please install/clone these and add them to your MATLAB path (using `addpath(genpath('...'))`).

1) Bombcell (MATLAB)
- Repo: https://github.com/Julie-Fabre/bombcell
- Requirements: MATLAB >= 2019a
- Setup:
  ```matlab
  addpath(genpath('C:\path\to\bombcell'))
  ```

2) SGLXMetaToCoords
- Repo: https://github.com/jenniferColonell/SGLXMetaToCoords
- Purpose: Read SpikeGLX metadata and produce Kilosort-compatible coordinates
- Setup:
  ```matlab
  addpath(genpath('C:\path\to\SGLXMetaToCoords'))
  ```

3) FieldTrip
- Website: https://www.fieldtriptoolbox.org/
- Purpose: Signal browsing/annotation and preprocessing
- Setup:
  ```matlab
  addpath('C:\path\to\fieldtrip');
  ft_defaults; % important initialization
  ```

4) AP_histology
- Repo: https://github.com/cortex-lab/AP_histology
- Purpose: Register probe tracks to atlas, produce `probe_ccf.mat`
- Setup:
  ```matlab
  addpath(genpath('C:\path\to\AP_histology'))
  addpath(genpath('C:\path\to\npy-matlab')) % AP_histology relies on reading/writing NPY
  % Also download/point to the atlas data, e.g. D:\AP_histology_atlas
  ```

5) npy-matlab
- Repo: https://github.com/kwikteam/npy-matlab
- Purpose: Read/write NumPy .npy files in MATLAB
- Setup:
  ```matlab
  addpath(genpath('C:\path\to\npy-matlab'))
  ```


### Python packages
Create a Python environment and install:

```bash
# recommended
conda create -n bal_npx python=3.11 -y
conda activate bal_npx

# core packages
pip install numpy pandas scipy matplotlib

# spikeinterface (for reading/resampling analog)
pip install spikeinterface

# npyx (unit I/O utilities)
# If available on PyPI:
pip install npyx
# Otherwise, install from your internal/source location
```


### Important note about hard-coded paths
Several scripts include absolute paths (e.g. `C:\...`, `Z:\...`). Update these to match your environment:
- `generateKilosortChanMap_script.m`, `get_probe_map_for_kilosort.m`
- `annotate_balloon.m`
- `run_AP_histology.m`
- Any example usage blocks in this README (adjust to your data locations)


## Function and Script Documentation

### MATLAB: `generateKilosortChanMap(metaFullPath, numShanks, bProcLF)`
- Purpose: Create a Kilosort-compatible `*_kilosortChanMap.mat` from a SpikeGLX `.ap.meta` (or `.lf.meta` if `bProcLF=1`).
- Inputs:
  - `metaFullPath` (string): Full path to the SpikeGLX `.ap.meta` file
  - `numShanks` (int): Number of shanks (1 or 4)
  - `bProcLF` (int, optional, default 0): 0 for AP, 1 for LFP
- Outputs:
  - Saves `<recording>_kilosortChanMap.mat` in the same directory, containing variables: `chanMap`, `connected`, `xcoords`, `ycoords`, `kcoords`
- Prerequisites:
  - `SGLXMetaToCoords` in MATLAB path
- Example:
  ```matlab
  generateKilosortChanMap("Z:\users\lab\data\mouse01\session01\imec0\rec_t0.imec0.ap.meta", 1);
  % For 4-shank probes:
  generateKilosortChanMap("Z:\users\lab\data\mouse01\session01\imec0\rec_t0.imec0.ap.meta", 4);
  ```
- Notes:
  - For `numShanks=4`, the function expects 8 unique x-positions (2 per shank) from the metadata; otherwise it raises an error.


### MATLAB: `runBombcellQualityAssessment(toy_dataset_location, Name,Value,...)`
- Purpose: Run Bombcell quality metrics on a Kilosort output folder.
- Inputs:
  - `toy_dataset_location` (string): Path to Kilosort output directory
  - Name-Value pairs (optional):
    - `SaveClusterKSLabel` (logical, default `true`): Save `cluster_KSLabel.tsv` one directory up
    - `LaunchGUI` (logical, default `false`): Open Bombcell GUI afterwards
    - `GainToUV` (numeric, default `NaN`): Conversion factor to microvolts (if needed)
    - `ParamOverrides` (struct, default `struct()`): Override defaults like thresholds and sample rate
- Outputs:
  - Creates a `bombcell` subfolder with `templates._bc_unit_labels.tsv`
  - Optionally writes `cluster_KSLabel.tsv` next to the Kilosort outputs
- Prerequisites:
  - `bombcell` on MATLAB path
  - SpikeGLX meta/raw discoverable under the dataset folder (looks for `*ap.meta` and `*ap.bin`/`*ap.dat`)
- Example:
  ```matlab
  runBombcellQualityAssessment("Z:\users\lab\kilosort\mouse01\session01\imec0", ...
      'SaveClusterKSLabel', true, ...
      'GainToUV', NaN, ...
      'ParamOverrides', struct('minNumSpikes', 150));
  ```


### MATLAB: `run_AP_histology.m`
- Purpose: Launch AP_histology to register probe tracks to atlas, then convert the resulting `probe_ccf.mat` to per-probe `probe_ccf_struct_#.mat` for easier Python ingestion.
- Inputs:
  - Edit internal `paths` list to include your `.../results/probe_ccf.mat` files
- Outputs:
  - For each input, saves `probe_ccf_struct_#.mat` files in the same results folder (variable name `probe`)
- Prerequisites:
  - AP_histology and npy-matlab on path; atlas folder available; your histology data prepared per AP_histology instructions
- Example:
  ```matlab
  % Update addpath to your locations, then run the script
  run('run_AP_histology.m');
  ```


### MATLAB: `annotate_balloon.m`
- Purpose: Manually annotate balloon on/off periods from an analog signal using FieldTrip’s viewer.
- Inputs/Expectations:
  - For each basepath in the script’s `basepaths` cell array, expects a `channel_1.mat` containing variables `channel_1` (vector) and `time_axis` (vector)
- Outputs:
  - Writes `bal_on_sec.txt` and `bal_off_sec.txt` (tab-delimited) with start/stop times (seconds) in each basepath
- Prerequisites:
  - FieldTrip on path (`ft_defaults` executed)
- Example:
  ```matlab
  % Edit basepaths, then run the script
  run('annotate_balloon.m');
  ```


### MATLAB: `get_probe_map_for_kilosort.m` and `generateKilosortChanMap_script.m`
- Purpose: Example scripts showing how to call `SGLXMetaToCoords` or `generateKilosortChanMap` with hard-coded paths.
- Action: Edit paths and run; or prefer calling `generateKilosortChanMap(...)` directly in your own scripts.


### Python: `npx_utils.extract_unit_data(datapath, savepath=None, fs=30000, periods='all', quality='good')`
- Purpose: For each unit in a Kilosort output folder, load spike times (via `npyx`), compute mean firing rate, waveforms on the peak channel, and depth.
- Inputs:
  - `datapath` (str): Kilosort output folder (must contain `channel_positions.npy`)
  - `savepath` (str|None): Optional `.pkl` path to save the resulting DataFrame
  - `fs` (int): Sampling rate (Hz). Used for spike time conversion and waveform time vector
  - `periods` (str|list): Time intervals (seconds) to include, or `'all'`
  - `quality` (str): Unit quality tag for filtering (passed to `npyx.gl.get_units`)
- Returns:
  - `pandas.DataFrame` with columns like `unit`, `mean_firing_rate`, `peak_channel`, `depth`, `spike_times_sec`, `waveform_mean`, etc.
- Example:
  ```python
  from npx_utils import extract_unit_data

  df = extract_unit_data(r"Z:\users\lab\kilosort\mouse01\session01\imec0",
                         savepath=r"Z:\users\lab\analysis\mouse01\units.pkl")
  ```


### Python: `npx_utils.extract_analog_data(datapath, real_channel_indices, labels, target_fs=30000, savepath=None)`
- Purpose: Read analog data from a folder containing SpikeGLX `.meta` and `.bin`, resample to `target_fs` if needed, and return traces for selected channels.
- Inputs:
  - `datapath` (str): Folder containing a single `.meta` and matching `.bin`
  - `real_channel_indices` (list[int]): Indices of channels with real data to keep
  - `labels` (list[str]): Channel names corresponding to the indices
  - `target_fs` (float): Desired sampling rate after resampling
  - `savepath` (str|None): Optional folder to save each channel as `<label>.npy`
- Returns:
  - `traces` (np.ndarray): shape `(num_samples, num_real_channels)`
  - `labels` (np.ndarray): labels for the channels you kept
  - `effective_fs` (float): Sampling rate after resampling
  - `time_vector` (np.ndarray): seconds, length `num_samples`
- Example:
  ```python
  from npx_utils import extract_analog_data

  traces, labels, fs, t = extract_analog_data(
      r"Z:\users\lab\recordings\mouse01\session01\imec0",
      real_channel_indices=[0,1,2],
      labels=["piezo","balloon","respiration"],
      target_fs=30000,
      savepath=r"Z:\users\lab\analysis\mouse01\analog")
  ```


### Python: `npx_utils.plot_firing_rate_and_analog(df, analog_trace, time_axis, savepath=None, bin_size=2, duration_sec=None, analog_label='...')`
- Purpose: Plot a z-scored firing-rate heatmap (from `df['spike_times_sec']`) with the analog trace below.
- Inputs:
  - `df` (DataFrame): From `extract_unit_data`, must have `spike_times_sec` and `depth`
  - `analog_trace` (np.ndarray): 1D analog signal
  - `time_axis` (np.ndarray): 1D seconds for the analog trace
  - `savepath` (str|None): Save the figure if provided
  - `bin_size` (float): Bin size (s) for firing rates
  - `duration_sec` (float|None): If None, inferred from spikes and analog
  - `analog_label` (str): Y-axis label for the analog trace
- Example:
  ```python
  from npx_utils import plot_firing_rate_and_analog

  plot_firing_rate_and_analog(
      df, traces[:, 1], t,
      savepath=r"Z:\users\lab\analysis\mouse01\fr_vs_balloon.png",
      bin_size=2,
      analog_label="Balloon pressure (a.u.)")
  ```


### Python: `npx_utils.extract_probe_trajectory(filepath, output_path=None)`
- Purpose: Load a `probe_ccf_struct_#.mat` and export a cleaned CSV of trajectory regions.
- Inputs:
  - `filepath` (str): Path to `probe_ccf_struct_#.mat`
  - `output_path` (str|None): Optional CSV path; if None, saves next to input
- Returns:
  - `pandas.DataFrame` with `region_name`, `region_acronym`, `depth_from`, `depth_to`
- Example:
  ```python
  from npx_utils import extract_probe_trajectory

  df_tract = extract_probe_trajectory(
      r"Z:\users\lab\tracks\mouse01\results\probe_ccf_struct_1.mat")
  ```


## Complete Workflow Guide

### Step 1: Prepare chanMap for sorting
Use the MATLAB function to create a Kilosort chanMap from `.ap.meta`:
```matlab
generateKilosortChanMap("Z:\...\imec0\rec_t0.imec0.ap.meta", 1); % or 4 for 4-shank probes
```
This produces `<...>_kilosortChanMap.mat` with `chanMap`, `connected`, `xcoords`, `ycoords`, `kcoords`.

### Step 2: Run Kilosort externally
Run Kilosort with your preferred workflow (MATLAB Kilosort or Kilosort4). This repository does not run Kilosort itself.
- Expected outputs (vary by version): `spike_times`, `spike_clusters`, `templates`, `templateWaveforms`, `pcFeatures`, `pcFeatureIdx`, and `channel_positions.npy`, etc.

### Step 3: Bombcell quality metrics (MATLAB)
```matlab
runBombcellQualityAssessment("Z:\...\kilosort\imec0", 'SaveClusterKSLabel', true);
```
Outputs:
- `<kilosort>/bombcell/templates._bc_unit_labels.tsv`
- `<kilosort>/cluster_KSLabel.tsv` (if `SaveClusterKSLabel=true`)
Parameters you may override via `ParamOverrides`: thresholds like `minNumSpikes`, `minPresenceRatio`, `lratioMax`, `maxRPVviolations`, waveform duration limits, etc.

### Step 4: Probe trajectory and histology mapping
1. Run AP_histology to create `probe_ccf.mat`.
2. Use the provided script to convert to per-probe struct files:
   ```matlab
   run('run_AP_histology.m');
   ```
3. In Python, export a regions CSV:
   ```python
   from npx_utils import extract_probe_trajectory
   df_tract = extract_probe_trajectory(r"Z:\...\results\probe_ccf_struct_1.mat")
   ```

### Step 5: Analog signal extraction and plotting (Python)
```python
from npx_utils import extract_analog_data, extract_unit_data, plot_firing_rate_and_analog

# analog
traces, labels, fs, t = extract_analog_data(
    r"Z:\...\recordings\mouse01\session01\imec0",
    real_channel_indices=[0,1,2],
    labels=["piezo","balloon","respiration"],
    target_fs=30000,
    savepath=r"Z:\...\analysis\mouse01\analog")

# units
df = extract_unit_data(r"Z:\...\kilosort\mouse01\session01\imec0",
                       savepath=r"Z:\...\analysis\mouse01\units.pkl")

# plot
plot_firing_rate_and_analog(df, traces[:, 1], t,
                            savepath=r"Z:\...\analysis\mouse01\fr_vs_balloon.png",
                            bin_size=2,
                            analog_label="Balloon pressure (a.u.)")
```


## File Structure
A typical on-disk layout might look like:
```
Z:\users\lab\
  recordings\mouse01\session01\imec0\
    rec_t0.imec0.ap.meta
    rec_t0.imec0.ap.bin
    ...
  kilosort\mouse01\session01\imec0\
    channel_positions.npy
    spike_times.npy / spike_times.npy
    templates.npy
    pcFeatures.npy
    ...
  tracks\mouse01\results\
    probe_ccf.mat
    probe_ccf_struct_1.mat
```
Repository contents (key files):
```
bal_npx/
  npx_utils.py
  generateKilosortChanMap.m
  generateKilosortChanMap_script.m
  get_probe_map_for_kilosort.m
  runBombcellQualityAssessment.m
  annotate_balloon.m
  run_AP_histology.m
  test/
```


## Troubleshooting
- Missing `.meta` or `.bin` in `extract_analog_data`: ensure the folder has exactly one metadata and matching binary file.
- `channel_positions.npy` missing for `extract_unit_data`: your Kilosort run should produce it; otherwise generate or copy the correct file.
- `npyx` import errors: install `npyx` or point Python to your local package.
- Memory errors reading/resampling analog: try processing smaller chunks or skip resampling if not needed.
- FieldTrip errors: ensure `ft_defaults` was run and FieldTrip is on the MATLAB path.
- AP_histology atlas path: verify your atlas directory exists and is correctly referenced in AP_histology.
- Bombcell `bc.*` not found: make sure Bombcell is on MATLAB path.
- `numShanks=4` x-coordinate error in `generateKilosortChanMap`: verify your probe layout really has 4 shanks and metadata reflects 8 distinct x-positions.
- Windows paths in Python: use raw strings `r"C:\\..."` or double backslashes.
- Network paths (e.g., `Z:\`): confirm you have permissions and stable connectivity.


## Examples
- Create chanMap (1-shank):
  ```matlab
  generateKilosortChanMap("Z:\users\lab\data\mouse02\s01\imec0\m02_s01_t0.imec0.ap.meta", 1);
  ```
- Run Bombcell:
  ```matlab
  runBombcellQualityAssessment("Z:\users\lab\kilosort\mouse02\s01\imec0", 'SaveClusterKSLabel', true);
  ```
- Convert AP_histology output and export regions:
  ```matlab
  run('run_AP_histology.m');
  ```
  ```python
  from npx_utils import extract_probe_trajectory
  df = extract_probe_trajectory(r"Z:\users\lab\tracks\mouse02\results\probe_ccf_struct_1.mat")
  df.to_csv(r"Z:\users\lab\tracks\mouse02\results\probe_regions.csv", index=False)
  ```
- Extract analog channels and plot with firing rates:
  ```python
  from npx_utils import extract_analog_data, extract_unit_data, plot_firing_rate_and_analog
  traces, labels, fs, t = extract_analog_data(r"Z:\users\lab\recordings\mouse02\s01\imec0",
                                              [0,1,2], ["piezo","balloon","resp"], 30000)
  df = extract_unit_data(r"Z:\users\lab\kilosort\mouse02\s01\imec0")
  plot_firing_rate_and_analog(df, traces[:, 1], t)
  ```

If anything is unclear or your setup differs (e.g., different directory layout, additional channels), adapt the examples accordingly. Feel free to open issues or PRs with improvements.

