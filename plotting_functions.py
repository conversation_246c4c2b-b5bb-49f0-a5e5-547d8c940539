"""
Plotting utilities for exploratory analysis of unit firing rates and balloon events.

This module contains reusable plotting functions that were originally defined
inside exploratory_analysis_b24_b25.ipynb.

Conventions
- Time is in seconds.
- Balloon timing columns are sequences of [start, end] pairs per row.
"""
from __future__ import annotations

from pathlib import Path
from typing import Any, List, Mapping, MutableMapping, Optional, Sequence, Tuple, Dict

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


def _paired_intervals_from_row(row: Mapping[str, Any], key: str = "balloon_on_sec") -> List[Tuple[float, float]]:
    """
    Extract a list of (start, end) float tuples from a DataFrame row for a given key.

    Parameters
    ----------
    row : Mapping[str, Any]
        Row-like object (e.g., a pandas Series) containing the intervals.
    key : str
        Column name that holds intervals as an array-like of shape (N, 2).

    Returns
    -------
    List[Tuple[float, float]]
        List of (start, end) pairs as floats. Returns an empty list if not present
        or in an unexpected format.
    """
    iv = row.get(key, None)
    if iv is None:
        return []
    arr = np.asarray(iv, dtype=float)
    if arr.ndim == 2 and arr.shape[1] == 2:
        return [(float(a), float(b)) for a, b in arr]
    return []


def _window_from_spikes(
    spike_times: Sequence[float],
    t_start: Optional[float] = None,
    t_end: Optional[float] = None,
    fallback: float = 1.0,
) -> Tuple[float, float]:
    """
    Infer a plotting window from spike timestamps if explicit t_start/t_end not provided.

    Parameters
    ----------
    spike_times : Sequence[float]
        Spike timestamps (s) for a unit.
    t_start, t_end : Optional[float]
        Optional bounds; if omitted, they are inferred from spike times.
    fallback : float
        If spikes are empty or t_end <= t_start, use this duration as a fallback.

    Returns
    -------
    (float, float)
        Start and end time for plotting.
    """
    st = np.asarray(spike_times, dtype=float).ravel()
    if st.size == 0:
        if t_start is None:
            t_start = 0.0
        if t_end is None:
            t_end = fallback
    else:
        if t_start is None:
            t_start = max(0.0, float(np.nanmin(st)))
        if t_end is None:
            t_end = float(np.nanmax(st))
        if t_end <= t_start:
            t_end = t_start + fallback
    return float(t_start), float(t_end)


def plot_each_unit_with_shading(
    df: pd.DataFrame,
    *,
    bin_size: float = 1.0,
    spike_key: str = "spike_times_sec",
    balloon_key: str = "balloon_on_sec",
    t_start: Optional[float] = None,
    t_end: Optional[float] = None,
    shade_alpha: float = 0.2,
    out_dir: Optional[Path | str] = None,
    dpi: int = 200,
) -> List[str]:
    """
    For every row (unit) in `df`, plot that unit's firing rate (Hz) with shaded balloon intervals.

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame with at least the columns specified by `spike_key` and `balloon_key`.
        Expected formats:
          - spike_key: array-like of spike times in seconds
          - balloon_key: array-like of shape (N, 2) with [start, end] pairs per row
    bin_size : float, default 1.0
        Histogram bin size in seconds.
    spike_key : str, default "spike_times_sec"
        DataFrame column name for spike time arrays.
    balloon_key : str, default "balloon_on_sec"
        DataFrame column name for balloon intervals; used for shading.
    t_start, t_end : Optional[float]
        Optional plotting window. If None, inferred per row from its spike times.
    shade_alpha : float, default 0.2
        Transparency for shaded balloon intervals.
    out_dir : Optional[Path | str]
        If provided, saves each figure to this directory instead of displaying on screen.
    dpi : int, default 200
        DPI when saving figures.

    Returns
    -------
    List[str]
        Paths of saved figures when `out_dir` is provided; otherwise an empty list.
    """
    saved_paths: List[str] = []
    save_dir: Optional[Path] = None
    if out_dir is not None:
        save_dir = Path(out_dir)
        save_dir.mkdir(parents=True, exist_ok=True)

    for idx, row in df.iterrows():
        # Window per unit (unless a fixed window is given)
        u_t_start, u_t_end = _window_from_spikes(
            row.get(spike_key, []), t_start=t_start, t_end=t_end, fallback=bin_size
        )

        # Common edges for this unit
        nbins = int(np.ceil((u_t_end - u_t_start) / bin_size))
        edges = u_t_start + np.arange(nbins + 1) * bin_size
        centers = (edges[:-1] + edges[1:]) / 2.0

        # Histogram for this unit
        st = np.asarray(row.get(spike_key, []), dtype=float).ravel()
        counts, _ = np.histogram(st, bins=edges)
        rate = counts.astype(float) / bin_size  # Hz

        # Plot
        fig, ax = plt.subplots(figsize=(12, 3.5))
        ax.step(centers, rate, where="mid", label="Firing rate (Hz)")
        ax.set_xlim(u_t_start, u_t_end)
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Firing rate (Hz)")

        # Title
        unit = row.get("unit", idx)
        animal = row.get("animal_id", "")
        ttl = f"Unit {unit}  |  Animal {animal}  |  bin={bin_size}s"
        ax.set_title(ttl)

        # Shade balloon intervals for this unit
        intervals = _paired_intervals_from_row(row, key=balloon_key)
        for on, off in intervals:
            if np.isfinite(on) and np.isfinite(off) and off > on:
                ax.axvspan(on, off, alpha=shade_alpha)

        ax.legend(loc="upper right")
        fig.tight_layout()

        if save_dir is not None:
            fname = f"unit_{unit}_bin{bin_size:.3f}s.png"
            fpath = save_dir / fname
            fig.savefig(fpath, dpi=dpi, bbox_inches="tight")
            plt.close(fig)
            saved_paths.append(str(fpath))

    return saved_paths



def _count_spikes_in_interval(spikes_sorted: np.ndarray, start: float, end: float) -> int:
    """Count spikes in half-open interval [start, end) using searchsorted."""
    li = np.searchsorted(spikes_sorted, start, side="left")
    ri = np.searchsorted(spikes_sorted, end, side="left")
    return int(max(0, ri - li))





def plot_units_grid_with_shading(
    df: pd.DataFrame,
    *,
    bin_size: float = 1.0,
    spike_key: str = "spike_times_sec",
    balloon_key: str = "balloon_on_sec",
    t_start: Optional[float] = None,
    t_end: Optional[float] = None,
    shade_alpha: float = 0.2,
    n_cols: int = 4,
    figsize_per_subplot: Tuple[float, float] = (4.0, 2.5),
    wspace: float = 0.25,
    hspace: float = 0.35,
    margins: Tuple[float, float, float, float] = (0.06, 0.98, 0.06, 0.06),  # left, right, bottom, top
    suptitle: Optional[str] = None,
    suptitle_y: Optional[float] = None,
    out_path: Optional[Path | str] = None,
    dpi: int = 200,
    file_format: Optional[str] = None,
    unit_class_map: Optional[Dict[Any, str]] = None,
    unit_col_name: str = "unit",
    show_legend: bool = True,
) -> Optional[str]:


    """
    Create a single composite figure with subplots for each unit showing firing-rate histograms
    and shaded balloon intervals.

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame with one row per unit and columns for `spike_key` and `balloon_key`.
    bin_size : float, default 1.0
        Histogram bin size in seconds.
    spike_key : str, default "spike_times_sec"
        Column name with arrays of spike timestamps (seconds).
    balloon_key : str, default "balloon_on_sec"
        Column name with intervals [[on, off], ...] for shading.
    t_start, t_end : Optional[float]
        Optional fixed plotting window for all units; if None, window is inferred per row.
    shade_alpha : float
        Alpha for shading balloon intervals.
    n_cols : int, default 4
        Number of columns in the subplot grid.
    figsize_per_subplot : (float, float)
        Size (width, height) in inches for each subplot; total size scales by rows/cols.
    wspace, hspace : float
        Subplot spacing parameters passed to subplots_adjust.
    margins : (left, right, bottom, top)
        Figure margins passed to subplots_adjust.
    suptitle : Optional[str]
        Optional figure-level title.
    out_path : Optional[Path | str]
        If provided, saves the composite figure here and returns the path. If None, returns None.
    dpi : int
        DPI used when saving the figure.
    file_format : Optional[str]
        Explicit image format to use when saving. If None, inferred from `out_path` extension.

    Returns
    -------
    Optional[str]
        Saved image path if `out_path` is provided; otherwise None.
    """
    n_units = len(df)
    if n_units == 0:
        return None

    n_cols = max(1, int(n_cols))
    n_rows = int(np.ceil(n_units / n_cols))

    # Compute figure size
    sub_w, sub_h = figsize_per_subplot
    fig_w = sub_w * n_cols
    fig_h = sub_h * n_rows

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_w, fig_h), squeeze=False)
    axes_flat = axes.ravel()

    for ax in axes_flat:
        ax.set_visible(False)

    for idx, (row_idx, row) in enumerate(df.iterrows()):
        ax = axes_flat[idx]
        ax.set_visible(True)

        # Window per unit
        u_t_start, u_t_end = _window_from_spikes(
            row.get(spike_key, []), t_start=t_start, t_end=t_end, fallback=bin_size
        )
        nbins = int(np.ceil((u_t_end - u_t_start) / bin_size))
        edges = u_t_start + np.arange(nbins + 1) * bin_size
        centers = (edges[:-1] + edges[1:]) / 2.0

        st = np.asarray(row.get(spike_key, []), dtype=float).ravel()
        counts, _ = np.histogram(st, bins=edges)
        bin_widths = np.diff(edges)
        # Convert counts to firing rate (Hz) by dividing by bin width in seconds
        rate = counts.astype(float) / bin_widths

        # Plot histogram of firing rate (Hz)
        ax.step(centers, rate, where="mid", color="#2E2E2E")
        ax.set_xlim(u_t_start, u_t_end)

        # Axis ticks on all subplots; x-label only on bottom row
        if idx % n_cols == 0:
            ax.set_ylabel("FR (Hz)")
        ax.tick_params(axis="x", labelbottom=True)
        if idx // n_cols == n_rows - 1:
            ax.set_xlabel("Time (s)")
        else:
            ax.set_xlabel("")

        # Title with unit id
        unit = row.get(unit_col_name, row_idx)
        ax.set_title(f"Unit {unit}", fontsize=9)

        # Optional thick colored border based on unit class
        if unit_class_map is not None and unit in unit_class_map:
            cls = unit_class_map[unit]
            key = str(cls).lower()
            color = {
                "excited": "#1b9e77",                # teal/green
                "inhibited": "#d95f02",              # orange
                "non-responsive": "#808080",         # light grey
                "nonresponsive": "#808080",
                "mixed/non-responsive": "#808080",
                "mixed": "#808080",
            }.get(key, "black")
            # Set spine colors and linewidth
            for spine in ax.spines.values():
                spine.set_edgecolor(color)
                spine.set_linewidth(3.0)

        # Shade balloon intervals
        intervals = _paired_intervals_from_row(row, key=balloon_key)
        for on, off in intervals:
            if np.isfinite(on) and np.isfinite(off) and off > on:
                ax.axvspan(on, off, alpha=shade_alpha)

        # No legends per subplot

    # Spacing and margins
    left, right, bottom, top = margins
    fig.subplots_adjust(left=left, right=right, bottom=bottom, top=top, wspace=wspace, hspace=hspace)

    # Optional legend explaining border colors
    if show_legend and len(df) > 0:
        import matplotlib.patches as mpatches
        handles = [
            mpatches.Patch(edgecolor="#1b9e77", facecolor="none", linewidth=3, label="Excited"),
            mpatches.Patch(edgecolor="#d95f02", facecolor="none", linewidth=3, label="Inhibited"),
            mpatches.Patch(edgecolor="#808080", facecolor="none", linewidth=3, label="Mixed or non-responsive"),
        ]
        # Place the legend in figure space to avoid overlap; adjust anchor if needed
        fig.legend(handles=handles, loc="upper right", bbox_to_anchor=(0.985, 0.985), frameon=False)

    # Suptitle: simplify to recording name and move closer to plots
    title_text = None
    if suptitle is None:
        # Try to infer from df if a single recording is present
        if isinstance(df, pd.DataFrame) and ("animal_id" in df.columns):
            uniq = pd.unique(df["animal_id"].astype(str))
            if uniq.size == 1:
                title_text = str(uniq[0])
    else:
        # If user provided a title, simplify by removing trailing details after ':'
        title_text = str(suptitle).split(":", 1)[0].strip()

    if title_text:
        y = 0.96 if suptitle_y is None else suptitle_y
        fig.suptitle(title_text, y=y)

    saved_path: Optional[str] = None
    if out_path is not None:
        out_path = Path(out_path)
        if file_format is None:
            fig.savefig(out_path, dpi=dpi, bbox_inches="tight")
        else:
            fig.savefig(out_path, dpi=dpi, bbox_inches="tight", format=file_format)
        plt.close(fig)
        saved_path = str(out_path)

    return saved_path

__all__ = [
    "plot_each_unit_with_shading",
    "plot_units_grid_with_shading",
    "plot_unit_trials_with_modulation",
]
