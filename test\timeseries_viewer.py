"""
Standalone interactive time-series viewer with event overlays and firing rate utilities.

Example:
    import numpy as np
    from timeseries_viewer import calculate_firing_rate, viewer

    time_vec = np.arange(0, 1000, 0.1)
    spikes = np.sort(np.random.uniform(0, 1000, size=5000))
    fr = calculate_firing_rate(spikes, time_vec, method='gaussian', sigma=1.0)
    events = [[100, 200], [400, 500], [700, 800]]
    viewer(time_vec, fr, events=events, title="Unit 42 Firing Rate")
"""
from __future__ import annotations

from dataclasses import dataclass
from typing import Iterable, List, Optional, Tuple

import numpy as np
import plotly.graph_objects as go
import ipywidgets as W
from IPython.display import display


# =============================
# Firing rate calculation
# =============================

def calculate_firing_rate(
    spike_times: np.ndarray,
    time_vector: np.ndarray,
    method: str = 'gaussian',
    **kwargs,
) -> np.ndarray:
    """
    Compute firing rate aligned to a provided time vector.

    Parameters
    ----------
    spike_times : array-like
        1D array of spike timestamps (seconds)
    time_vector : array-like
        1D array of time points (seconds) to evaluate the firing rate
    method : str
        'gaussian', 'histogram', or 'sliding_window'
    kwargs : dict
        Method-specific parameters:
        - gaussian: sigma (float, seconds)
        - sliding_window: window_size (float, seconds)

    Returns
    -------
    np.ndarray
        1D array of firing rates (Hz) corresponding to time_vector
    """
    tv = np.asarray(time_vector, dtype=float)
    if tv.ndim != 1 or tv.size < 2:
        raise ValueError("time_vector must be a 1D array with at least 2 points")

    st = np.asarray(spike_times, dtype=float)
    if st.ndim != 1:
        raise ValueError("spike_times must be a 1D array")

    # Ensure strictly increasing time vector
    if not np.all(np.diff(tv) > 0):
        # attempt to sort, but warn by raising if duplicates remain
        order = np.argsort(tv)
        tv = tv[order]
        if not np.all(np.diff(tv) > 0):
            raise ValueError("time_vector must be strictly increasing")

    dt = float(np.median(np.diff(tv)))
    if not np.isfinite(dt) or dt <= 0:
        raise ValueError("Could not determine a positive time step from time_vector")

    # Build histogram of spikes per bin defined by time_vector
    edges = np.concatenate([tv - dt/2, tv[-1:] + dt/2])
    counts, _ = np.histogram(st, bins=edges)

    method = (method or 'gaussian').lower()
    if method == 'histogram':
        # counts per bin / dt -> Hz
        rate = counts.astype(float) / dt
    elif method == 'gaussian':
        sigma = float(kwargs.get('sigma', 0.1))  # seconds
        if sigma <= 0:
            # no smoothing: fallback to histogram
            rate = counts.astype(float) / dt
        else:
            half_width = max(1, int(np.ceil(5 * sigma / dt)))
            kx = np.arange(-half_width, half_width + 1) * dt
            kernel = np.exp(-0.5 * (kx / sigma) ** 2)
            kernel /= kernel.sum()  # unit area (discrete)
            rate = np.convolve(counts.astype(float), kernel, mode='same') / dt
    elif method == 'sliding_window':
        win = float(kwargs.get('window_size', 1.0))
        if win <= 0:
            raise ValueError("sliding_window requires window_size > 0")
        n = max(1, int(round(win / dt)))
        kernel = np.ones(n, dtype=float) / win  # normalize by window_size to get Hz
        rate = np.convolve(counts.astype(float), kernel, mode='same')
    else:
        raise ValueError("Unknown method: {}".format(method))

    return rate.astype(float)


# =============================
# Interactive time-series viewer
# =============================

@dataclass
class _ViewState:
    x_min: float
    x_max: float
    y_min: float
    y_max: float


def _validate_inputs(time_vector: np.ndarray, time_series: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    t = np.asarray(time_vector, dtype=float)
    y = np.asarray(time_series, dtype=float)
    if t.ndim != 1 or y.ndim != 1:
        raise ValueError("time_vector and time_series must be 1D arrays")
    if t.size != y.size:
        raise ValueError("time_vector and time_series must be the same length")
    if not np.all(np.diff(t) > 0):
        order = np.argsort(t)
        t = t[order]
        y = y[order]
        if not np.all(np.diff(t) > 0):
            raise ValueError("time_vector must be strictly increasing")
    return t, y


def _clean_events(events: Optional[Iterable[Iterable[float]]]) -> List[Tuple[float, float]]:
    intervals: List[Tuple[float, float]] = []
    if events is None:
        return intervals
    try:
        for e in events:
            if e is None:
                continue
            a, b = float(e[0]), float(e[1])
            if np.isfinite(a) and np.isfinite(b) and b > a:
                intervals.append((a, b))
    except Exception:
        # ignore malformed entries
        pass
    # sort by start
    intervals.sort(key=lambda ab: ab[0])
    return intervals


def _add_event_shading(fig: go.FigureWidget, intervals: List[Tuple[float, float]], x_min: float, x_max: float,
                       color: str = 'LightSkyBlue', opacity: float = 0.3):
    # Clear any existing vrects
    fig.layout.shapes = ()
    for a, b in intervals:
        if b <= x_min or a >= x_max:
            continue
        x0, x1 = max(a, x_min), min(b, x_max)
        fig.add_vrect(x0=x0, x1=x1, fillcolor=color, opacity=opacity, line_width=0, layer='below')


def viewer(time_vector=None,
           time_series=None,
           events=None,
           title: str = "Time Series Viewer",
           *,
           enable_keyboard: bool = True,
           show_cursor_controls: bool = True,
           spike_times: Optional[np.ndarray] = None,
           rate_method: str = 'gaussian',
           rate_kwargs: Optional[dict] = None,
           show_raster: bool = False):
    """
    Interactive time-series viewer with event overlays and pan/zoom controls.

    Parameters
    ----------
    time_vector : 1D array-like
        Time points in seconds
    time_series : 1D array-like
        Values aligned to time_vector
    events : list/array of [start, end]
        Optional intervals to shade on the plot
    title : str
        Plot title
    """
    # Build series from either provided continuous data or raw spike times
    spikes_for_raster = None
    if time_series is None and spike_times is not None:
        st = np.asarray(spike_times, dtype=float).ravel()
        st = st[np.isfinite(st)]
        if st.size == 0:
            raise ValueError("spike_times provided but empty/non-finite")
        if time_vector is None:
            dt_auto = float((rate_kwargs or {}).get('dt', 0.1))
            t_min = float(np.nanmin(st)); t_max = float(np.nanmax(st))
            pad = max(0.5, 0.02 * (t_max - t_min))
            tv = np.arange(t_min - pad, t_max + pad + dt_auto, dt_auto)
        else:
            tv = np.asarray(time_vector, dtype=float)
        y = calculate_firing_rate(st, tv, method=rate_method, **(rate_kwargs or {}))
        t = tv
        if show_raster:
            spikes_for_raster = st
    else:
        t, y = _validate_inputs(time_vector, time_series)
        if spike_times is not None and show_raster:
            st = np.asarray(spike_times, dtype=float).ravel()
            st = st[np.isfinite(st)]
            spikes_for_raster = st if st.size else None
    intervals = _clean_events(events)

    # Initial ranges with small margins
    x_min_all, x_max_all = float(t[0]), float(t[-1])
    y_min_all, y_max_all = float(np.nanmin(y)), float(np.nanmax(y))
    if not np.isfinite(y_min_all) or not np.isfinite(y_max_all):
        y_min_all, y_max_all = 0.0, 1.0
    if y_min_all == y_max_all:
        y_min_all -= 0.5
        y_max_all += 0.5

    x_pad = 0.02 * (x_max_all - x_min_all) if x_max_all > x_min_all else 1.0
    y_pad = 0.10 * (y_max_all - y_min_all) if y_max_all > y_min_all else 1.0

    state = _ViewState(
        x_min=x_min_all - x_pad,
        x_max=x_max_all + x_pad,
        y_min=y_min_all - y_pad,
        y_max=y_max_all + y_pad,
    )

    # Figure
    fig = go.FigureWidget()
    fig.add_trace(go.Scatter(x=t, y=y, mode='lines', name='Signal'))
    fig.update_layout(title=title, height=450, legend_orientation='h')
    fig.update_xaxes(title='Time (s)')
    fig.update_yaxes(title='Amplitude')

    _add_event_shading(fig, intervals, state.x_min, state.x_max)
    fig.update_xaxes(range=[state.x_min, state.x_max])
    fig.update_yaxes(range=[state.y_min, state.y_max])
    # Optional spike raster overlay on a secondary y-axis
    if spikes_for_raster is not None:
        fig.add_trace(go.Scatter(x=spikes_for_raster,
                                 y=np.ones_like(spikes_for_raster),
                                 mode='markers',
                                 marker=dict(size=4, color='black', opacity=0.6),
                                 name='Spikes',
                                 yaxis='y2'))
        fig.update_layout(yaxis2=dict(overlaying='y', side='right', range=[0,1],
                                      showticklabels=False, title='Spikes', visible=True))

    # Widgets
    step_x = 0.1 * (x_max_all - x_min_all) if x_max_all > x_min_all else 1.0
    step_y = 0.1 * (y_max_all - y_min_all) if y_max_all > y_min_all else 1.0

    x_range = W.FloatRangeSlider(
        value=[state.x_min, state.x_max],
        min=x_min_all, max=x_max_all,
        step=max(1e-9, (x_max_all - x_min_all) / 1000.0),
        description='X range:', layout=W.Layout(width='95%')
    )
    y_range = W.FloatRangeSlider(
        value=[state.y_min, state.y_max],
        min=y_min_all - y_pad, max=y_max_all + y_pad,
        step=max(1e-12, (y_max_all - y_min_all) / 1000.0),
        description='Y range:', layout=W.Layout(width='95%')
    )

    pan_left_btn = W.Button(description='⟸ Pan Left')
    pan_right_btn = W.Button(description='Pan Right ⟹')
    zoom_in_x_btn = W.Button(description='Zoom In X')
    zoom_out_x_btn = W.Button(description='Zoom Out X')

    zoom_in_y_btn = W.Button(description='Zoom In Y')
    zoom_out_y_btn = W.Button(description='Zoom Out Y')

    reset_btn = W.Button(description='Reset')

    # Optional cursor controls
    cursor_dur = W.FloatText(value=5.0, description='Duration (s):')
    cursor_btn = W.Button(description='Play cursor') if show_cursor_controls else None

    info = W.HTML(value='')


    def _update_info():
        info.value = f"<b>View</b> x=[{state.x_min:.3f}, {state.x_max:.3f}] \n"
        info.value += f"y=[{state.y_min:.3f}, {state.y_max:.3f}]"

    def _apply_axes():
        with fig.batch_update():
            fig.update_xaxes(range=[state.x_min, state.x_max])
            fig.update_yaxes(range=[state.y_min, state.y_max])
        _add_event_shading(fig, intervals, state.x_min, state.x_max)
        _update_info()

    def _on_x_range_change(change):
        if change['name'] == 'value':
            v = change['new']
            try:
                state.x_min, state.x_max = float(v[0]), float(v[1])
                _apply_axes()
            except Exception:
                pass

    def _on_y_range_change(change):
        if change['name'] == 'value':
            v = change['new']
            try:
                state.y_min, state.y_max = float(v[0]), float(v[1])
                _apply_axes()
            except Exception:
                pass

    def _pan(delta):
        span = state.x_max - state.x_min
        state.x_min += delta
        state.x_max += delta
        # clamp to data limits
        if state.x_min < x_min_all:
            shift = x_min_all - state.x_min
            state.x_min += shift
            state.x_max += shift
        if state.x_max > x_max_all:
            shift = state.x_max - x_max_all
            state.x_min -= shift
            state.x_max -= shift
        x_range.value = [state.x_min, state.x_max]

    def _zoom_x(factor: float):
        # factor < 1 => zoom in, > 1 => zoom out
        cx = 0.5 * (state.x_min + state.x_max)
        half = 0.5 * (state.x_max - state.x_min) * factor
        half = max((x_max_all - x_min_all) / 1000.0, half)
        state.x_min = max(x_min_all, cx - half)
        state.x_max = min(x_max_all, cx + half)
        x_range.value = [state.x_min, state.x_max]

    def _zoom_y(factor: float):
        cy = 0.5 * (state.y_min + state.y_max)
        half = 0.5 * (state.y_max - state.y_min) * factor
        half = max((y_max_all - y_min_all) / 1000.0, half)
        state.y_min = cy - half
        state.y_max = cy + half
        y_range.value = [state.y_min, state.y_max]

    def _reset(_=None):
        state.x_min = x_min_all - x_pad
        state.x_max = x_max_all + x_pad
        state.y_min = y_min_all - y_pad
        state.y_max = y_max_all + y_pad
        x_range.value = [state.x_min, state.x_max]
        y_range.value = [state.y_min, state.y_max]

    def _play_cursor(_=None):
        if not show_cursor_controls:
            return
        # Add a vertical line and animate across current x-range over cursor_dur seconds
        x_start, x_end = state.x_min, state.x_max
        duration = float(cursor_dur.value) if cursor_dur.value else max(0.01, x_end - x_start)
        if duration <= 0:
            duration = max(0.01, x_end - x_start)
        fig.add_shape(type='line', x0=x_start, x1=x_start, y0=0, y1=1,
                      xref='x', yref='paper', line=dict(color='red', width=2))
        import time as _time
        fps = 30.0
        t0 = _time.perf_counter()
        while True:
            elapsed = _time.perf_counter() - t0
            pos = x_start + (elapsed / duration) * (x_end - x_start)
            if elapsed >= duration:
                break
            with fig.batch_update():
                fig.layout.shapes[-1].x0 = pos
                fig.layout.shapes[-1].x1 = pos
            _time.sleep(1.0 / fps)
        with fig.batch_update():
            # remove last shape (cursor)
            fig.layout.shapes = fig.layout.shapes[:-1]


    # Wire up controls
    x_range.observe(_on_x_range_change, names='value')
    y_range.observe(_on_y_range_change, names='value')

    pan_left_btn.on_click(lambda _: _pan(-step_x))
    pan_right_btn.on_click(lambda _: _pan(step_x))
    zoom_in_x_btn.on_click(lambda _: _zoom_x(0.5))
    zoom_out_x_btn.on_click(lambda _: _zoom_x(2.0))
    zoom_in_y_btn.on_click(lambda _: _zoom_y(0.5))
    zoom_out_y_btn.on_click(lambda _: _zoom_y(2.0))
    reset_btn.on_click(_reset)
    if cursor_btn is not None:
        cursor_btn.on_click(_play_cursor)

    # Optional keyboard shortcuts via ipyevents (if available)
    kb_label = W.HTML("<i>Tip: click the plot, then use Left/Right arrows to pan; +/- to zoom X; [/] to zoom Y; 0 to reset.</i>")
    key_box = None
    try:
        if enable_keyboard:
            from ipyevents import Event  # type: ignore
            key_box = W.Box()
            key_box.layout.border = '1px dashed #ccc'
            key_box.layout.padding = '2px'
            key_box.layout.margin = '2px 0px'
            ev = Event(source=key_box, watched_events=['keydown'])
            def _on_key(event):
                k = (event or {}).get('key', '')
                if k == 'ArrowLeft':
                    _pan(-step_x)
                elif k == 'ArrowRight':
                    _pan(step_x)
                elif k in ['+', '=']:
                    _zoom_x(0.5)
                elif k in ['-','_']:
                    _zoom_x(2.0)
                elif k == '[':
                    _zoom_y(0.5)
                elif k == ']':
                    _zoom_y(2.0)
                elif k == '0':
                    _reset()
            ev.on_dom_event(_on_key)
    except Exception:
        key_box = None

    # Layout
    top_controls = [W.HTML("<b>Controls</b>"), x_range, W.HBox([pan_left_btn, pan_right_btn, zoom_in_x_btn, zoom_out_x_btn, reset_btn])]
    bottom_controls = [y_range, W.HBox([zoom_in_y_btn, zoom_out_y_btn])]
    if cursor_btn is not None:
        bottom_controls.append(W.HBox([cursor_dur, cursor_btn]))
    bottom_controls.extend([info])

    right_panel = W.VBox(top_controls + bottom_controls)

    # Assemble display, optionally with keyboard box
    if key_box is not None:
        display(W.VBox([fig, kb_label, key_box, right_panel]))
    else:
        display(W.VBox([fig, kb_label, right_panel]))

    _update_info()

    return fig  # return handle for further customization

