{"cells": [{"cell_type": "code", "execution_count": null, "id": "2fc93f5f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Load the unit data and balloon ON/OFF intervals (assumed already loaded)\n", "# unit_data, balloon_on_sec, balloon_off_sec\n", "\n", "def compute_mean_firing_rate_in_intervals(spike_times, intervals):\n", "    rates = []\n", "    for _, row in intervals.iterrows():\n", "        start, end = row['start_sec'] + 5, row['end_sec']\n", "        count = sum((spike_times >= start) & (spike_times < end))\n", "        duration = end - start\n", "        rate = count / duration if duration > 0 else 0\n", "        rates.append(rate)\n", "    return sum(rates) / len(rates) if rates else 0\n", "\n", "# Prepare result list\n", "unit_rates = []\n", "\n", "for idx, row in unit_data.iterrows():\n", "    unit_id = row['unit']\n", "    spike_times = pd.Series(row['spike_times_sec'])\n", "\n", "    mean_rate_on = compute_mean_firing_rate_in_intervals(spike_times, balloon_on_sec)\n", "    mean_rate_off = compute_mean_firing_rate_in_intervals(spike_times, balloon_off_sec)\n", "\n", "    unit_rates.append({\n", "        'unit': unit_id,\n", "        'mean_rate_on': mean_rate_on,\n", "        'mean_rate_off': mean_rate_off\n", "    })\n", "\n", "# Create resulting DataFrame\n", "unit_firing_by_condition = pd.DataFrame(unit_rates)\n", "\n", "# Preview\n", "print(unit_firing_by_condition.head(10))\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "# Determine colors: red if ON > OFF, else black\n", "colors = ['red' if on > off else 'black'\n", "          for on, off in zip(unit_firing_by_condition['mean_rate_on'],\n", "                             unit_firing_by_condition['mean_rate_off'])]\n", "\n", "# Create the scatter plot\n", "plt.figure(figsize=(6, 6))  # Square figure\n", "plt.plot([0, max(unit_firing_by_condition['mean_rate_on'].max(),\n", "                 unit_firing_by_condition['mean_rate_off'].max())],\n", "         [0, max(unit_firing_by_condition['mean_rate_on'].max(),\n", "                 unit_firing_by_condition['mean_rate_off'].max())],\n", "         'k--', label='y = x', zorder=0)  # Diagonal line\n", "\n", "plt.scatter(unit_firing_by_condition['mean_rate_on'],\n", "            unit_firing_by_condition['mean_rate_off'],\n", "            c=colors)\n", "\n", "plt.xlabel('Mean firing rate during balloon ON')\n", "plt.ylabel('Mean firing rate during balloon OFF')\n", "plt.axis('square')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 8, "id": "7b50159c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "\n", "def plot_mua_depth_profile(folder, fs=30000, bin_size_um=50, trajectory_path=None):\n", "    \"\"\"\n", "    Plot MUA spike rate vs. brain depth, optionally overlaying brain regions from a trajectory file.\n", "\n", "    Parameters:\n", "    - folder (str): Path to Kilosort output\n", "    - fs (int): Sampling rate in Hz\n", "    - bin_size_um (int): Bin size for MUA profile (µm)\n", "    - trajectory_path (str or None): Path to trajectory file (must contain 'start', 'end', 'structure')\n", "    \"\"\"\n", "\n", "    # Load spike data\n", "    spike_times = np.load(os.path.join(folder, 'spike_times.npy'))\n", "    spike_templates = np.load(os.path.join(folder, 'spike_templates.npy'))\n", "    templates = np.load(os.path.join(folder, 'templates.npy'))\n", "    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))\n", "    channel_positions = np.load(os.path.join(folder, 'channel_positions.npy'))\n", "\n", "    # Get main channel for each template\n", "    template_amplitudes = templates.ptp(axis=1)\n", "    max_chan_indices = np.argmax(template_amplitudes, axis=1)\n", "    template_main_channels = channel_map[max_chan_indices]\n", "    channel_depths = channel_positions[:, 1]\n", "    spike_depths = channel_depths[template_main_channels[spike_templates]]\n", "\n", "    # Bin and compute spike rates\n", "    depth_bins = np.arange(channel_depths.min(), channel_depths.max() + bin_size_um, bin_size_um)\n", "    depth_centers = 0.5 * (depth_bins[:-1] + depth_bins[1:])\n", "    spike_counts, _ = np.histogram(spike_depths, bins=depth_bins)\n", "    duration_sec = spike_times.max() / fs\n", "    spike_rates = spike_counts / duration_sec\n", "\n", "    # Offset to align with brain surface (if trajectory is given)\n", "    offset_from_brain_surface = 0\n", "    if trajectory_path is not None:\n", "        if trajectory_path.endswith('.npz'):\n", "            traj_data = np.load(trajectory_path, allow_pickle=True)\n", "            trajectory = traj_data['trajectory'].item() if 'trajectory' in traj_data else traj_data\n", "        else:\n", "            trajectory = np.load(trajectory_path, allow_pickle=True).item()\n", "\n", "        tip_depth_brain = max(trajectory['end'])  # tip is deepest structure end\n", "        tip_depth_probe = depth_bins.max()        # max depth from probe tip\n", "        offset_from_brain_surface = tip_depth_brain - tip_depth_probe\n", "        depth_centers += offset_from_brain_surface\n", "\n", "    # Plot\n", "    fig, ax = plt.subplots(figsize=(4, 6))\n", "    ax.plot(spike_rates, depth_centers, color='black', linewidth=1.5)\n", "    ax.invert_yaxis()\n", "    ax.set_xlabel('Spike rate (Hz)', fontsize=12)\n", "    ax.set_ylabel('Depth from brain surface (µm)', fontsize=12)\n", "    ax.set_title('MUA spike rate vs. brain depth', fontsize=13)\n", "    ax.grid(True, axis='y', linestyle='--', linewidth=0.5)\n", "    \n", "    y_min = np.floor(depth_centers.min() / 100) * 100\n", "    y_max = np.ceil(depth_centers.max() / 100) * 100\n", "    ax.set_yticks(np.arange(y_min, y_max + 1, 100))\n", "\n", "    # Overlay regions\n", "    if trajectory_path is not None:\n", "        colors = plt.cm.tab20(np.linspace(0, 1, len(trajectory['structure'])))\n", "        for region, start, end, c in zip(trajectory['structure'], trajectory['start'], trajectory['end'], colors):\n", "            ax.axhspan(start, end, color=c, alpha=0.2, label=region)\n", "        # Only show unique labels\n", "        handles, labels = ax.get_legend_handles_labels()\n", "        unique = dict(zip(labels, handles))\n", "        ax.legend(unique.values(), unique.keys(), loc='lower right', fontsize=8, frameon=False)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 24, "id": "0fa43009", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "import pandas as pd\n", "\n", "def plot_mua_depth_profile(folder, fs=30000, bin_size_um=50, trajectory_path=None):\n", "    \"\"\"\n", "    Plot MUA spike rate vs. brain depth, optionally overlaying brain regions from a trajectory CSV.\n", "\n", "    Parameters:\n", "    - folder (str): Path to Kilosort output\n", "    - fs (int): Sampling rate in Hz (default: 30000)\n", "    - bin_size_um (int): Bin size for MUA profile (µm)\n", "    - trajectory_path (str or None): Path to CSV trajectory file with columns: 'structure', 'start', 'end'\n", "    \"\"\"\n", "\n", "    # Load spike + channel data\n", "    spike_times = np.load(os.path.join(folder, 'spike_times.npy'))\n", "    spike_templates = np.load(os.path.join(folder, 'spike_templates.npy'))\n", "    templates = np.load(os.path.join(folder, 'templates.npy'))\n", "    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))\n", "    channel_positions = np.load(os.path.join(folder, 'channel_positions.npy'))\n", "\n", "    # Determine main channel per template\n", "    template_amplitudes = templates.ptp(axis=1)\n", "    max_chan_indices = np.argmax(template_amplitudes, axis=1)\n", "    template_main_channels = channel_map[max_chan_indices]\n", "    channel_depths = channel_positions[:, 1]\n", "    spike_depths = channel_depths[template_main_channels[spike_templates]]\n", "\n", "    # Bin depths and compute firing rate\n", "    depth_bins = np.arange(channel_depths.min(), channel_depths.max() + bin_size_um, bin_size_um)\n", "    depth_centers = 0.5 * (depth_bins[:-1] + depth_bins[1:])\n", "    spike_counts, _ = np.histogram(spike_depths, bins=depth_bins)\n", "    duration_sec = spike_times.max() / fs\n", "    spike_rates = spike_counts / duration_sec\n", "\n", "    # Offset for aligning with brain surface\n", "    offset_from_brain_surface = 0\n", "    trajectory_df = None\n", "\n", "    if trajectory_path is not None:\n", "        trajectory_df = pd.read_csv(trajectory_path)\n", "        assert {'structure', 'start', 'end'}.issubset(trajectory_df.columns), \\\n", "            \"CSV must have 'structure', 'start', and 'end' columns.\"\n", "\n", "        # Align probe depth (from tip) to brain depth (from surface)\n", "        brain_depth_tip = trajectory_df['end'].max()\n", "        probe_depth_tip = depth_bins.max()\n", "        offset_from_brain_surface = brain_depth_tip - probe_depth_tip\n", "        depth_centers += offset_from_brain_surface\n", "\n", "    # Plot\n", "    fig, ax = plt.subplots(figsize=(4, 6))\n", "    ax.plot(spike_rates, depth_centers, color='black', linewidth=1.5)\n", "    ax.invert_yaxis()\n", "    ax.set_xlabel('Spike rate (a.u.)', fontsize=12)\n", "    ax.set_ylabel('Depth from probe tip (µm)', fontsize=12)\n", "    ax.set_title('MUA spike rate vs. brain depth', fontsize=13)\n", "    ax.grid(True, axis='y', linestyle='--', linewidth=0.5)\n", "\n", "    # Clean ticks\n", "    y_min = np.floor(depth_centers.min() / 100) * 100\n", "    y_max = np.ceil(depth_centers.max() / 100) * 100\n", "    ax.set_yticks(np.arange(y_min, y_max + 1, 100))\n", "    ax.tick_params(labelsize=10)\n", "\n", "    # Optional: draw brain regions\n", "    if trajectory_df is not None:\n", "        colors = plt.cm.tab20(np.linspace(0, 1, len(trajectory_df)))\n", "        for idx, row in trajectory_df.iterrows():\n", "            ax.axhspan(row['start'], row['end'], color=colors[idx], alpha=0.2, label=row['structure'])\n", "        \n", "        # Unique labels only\n", "        handles, labels = ax.get_legend_handles_labels()\n", "        unique = dict(zip(labels, handles))\n", "        ax.legend(unique.values(), unique.keys(), loc='lower right', fontsize=8, frameon=False)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 31, "id": "e5a2518e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_mua_depth_profile(\n", "    folder=r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b12\\b12_p2_r1_g0\\b12_p2_r1_g0_imec0\",\n", "    fs=30000,\n", "    bin_size_um=100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "06e0a541", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "region_name", "rawType": "object", "type": "string"}, {"name": "region_acronym", "rawType": "object", "type": "string"}, {"name": "depth_from", "rawType": "int64", "type": "integer"}, {"name": "depth_to", "rawType": "int64", "type": "integer"}], "ref": "444c0c5c-afdd-4fc1-adbd-1a88f4c7f112", "rows": [["0", "Primary somatosensory area trunk layer 1", "SSp-tr1", "0", "113"], ["1", "Primary somatosensory area trunk layer 2/3", "SSp-tr2/3", "113", "449"], ["2", "Primary somatosensory area trunk layer 4", "SSp-tr4", "449", "571"], ["3", "Primary somatosensory area trunk layer 5", "SSp-tr5", "571", "887"], ["4", "Primary somatosensory area trunk layer 6a", "SSp-tr6a", "887", "948"], ["5", "Primary somatosensory area lower limb layer 6a", "SSp-ll6a", "948", "1173"], ["6", "Primary somatosensory area lower limb layer 6b", "SSp-ll6b", "1173", "1213"], ["7", "supra-callosal cerebral white matter", "scwm", "1213", "1274"], ["8", "corpus callosum body", "ccb", "1274", "1346"], ["9", "alveus", "alv", "1346", "1397"], ["10", "Field CA1", "CA1", "1397", "1672"], ["11", "Field CA2", "CA2", "1672", "1754"], ["12", "Field CA3", "CA3", "1754", "2365"], ["13", "alveus", "alv", "2365", "2406"], ["14", "Dorsal part of the lateral geniculate complex core", "LGd-co", "2457", "2508"], ["15", "Lateral dorsal nucleus of thalamus", "LD", "2508", "2523"], ["16", "Dorsal part of the lateral geniculate complex core", "LGd-co", "2523", "2569"], ["17", "Lateral dorsal nucleus of thalamus", "LD", "2569", "3078"], ["18", "Thalamus", "TH", "3078", "3099"], ["19", "Ventral posterolateral nucleus of the thalamus", "VPL", "3099", "3108"], ["20", "Thalamus", "TH", "3108", "3109"], ["21", "Ventral posterolateral nucleus of the thalamus", "VPL", "3109", "3140"], ["22", "Ventral posteromedial nucleus of the thalamus", "VPM", "3140", "3894"], ["23", "Ventral posterolateral nucleus of the thalamus", "VPL", "3894", "4301"], ["24", "Zona incerta", "ZI", "4301", "4424"], ["25", "fiber tracts", "fiber tracts", "4424", "4566"], ["26", "internal capsule", "int", "4566", "5031"], ["27", "fiber tracts", "fiber tracts", "5031", "5086"], ["28", "optic tract", "opt", "5086", "5270"], ["29", "Medial amygdalar nucleus", "MEA", "5270", "5861"], ["30", "Posterior amygdalar nucleus", "PA", "5861", "6116"], ["31", "Cortical amygdalar area posterior part medial zone", "COApm", "6116", "6117"], ["32", "Posterior amygdalar nucleus", "PA", "6117", "6126"], ["33", "Cortical amygdalar area posterior part medial zone", "COApm", "6126", "6748"]], "shape": {"columns": 4, "rows": 34}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region_name</th>\n", "      <th>region_acronym</th>\n", "      <th>depth_from</th>\n", "      <th>depth_to</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Primary somatosensory area trunk layer 1</td>\n", "      <td>SSp-tr1</td>\n", "      <td>0</td>\n", "      <td>113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Primary somatosensory area trunk layer 2/3</td>\n", "      <td>SSp-tr2/3</td>\n", "      <td>113</td>\n", "      <td>449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Primary somatosensory area trunk layer 4</td>\n", "      <td>SSp-tr4</td>\n", "      <td>449</td>\n", "      <td>571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Primary somatosensory area trunk layer 5</td>\n", "      <td>SSp-tr5</td>\n", "      <td>571</td>\n", "      <td>887</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Primary somatosensory area trunk layer 6a</td>\n", "      <td>SSp-tr6a</td>\n", "      <td>887</td>\n", "      <td>948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Primary somatosensory area lower limb layer 6a</td>\n", "      <td>SSp-ll6a</td>\n", "      <td>948</td>\n", "      <td>1173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Primary somatosensory area lower limb layer 6b</td>\n", "      <td>SSp-ll6b</td>\n", "      <td>1173</td>\n", "      <td>1213</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>supra-callosal cerebral white matter</td>\n", "      <td>scwm</td>\n", "      <td>1213</td>\n", "      <td>1274</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>corpus callosum body</td>\n", "      <td>ccb</td>\n", "      <td>1274</td>\n", "      <td>1346</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>alveus</td>\n", "      <td>alv</td>\n", "      <td>1346</td>\n", "      <td>1397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Field CA1</td>\n", "      <td>CA1</td>\n", "      <td>1397</td>\n", "      <td>1672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Field CA2</td>\n", "      <td>CA2</td>\n", "      <td>1672</td>\n", "      <td>1754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Field CA3</td>\n", "      <td>CA3</td>\n", "      <td>1754</td>\n", "      <td>2365</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>alveus</td>\n", "      <td>alv</td>\n", "      <td>2365</td>\n", "      <td>2406</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Dorsal part of the lateral geniculate complex ...</td>\n", "      <td>LGd-co</td>\n", "      <td>2457</td>\n", "      <td>2508</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Lateral dorsal nucleus of thalamus</td>\n", "      <td>LD</td>\n", "      <td>2508</td>\n", "      <td>2523</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Dorsal part of the lateral geniculate complex ...</td>\n", "      <td>LGd-co</td>\n", "      <td>2523</td>\n", "      <td>2569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Lateral dorsal nucleus of thalamus</td>\n", "      <td>LD</td>\n", "      <td>2569</td>\n", "      <td>3078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Thalamus</td>\n", "      <td>TH</td>\n", "      <td>3078</td>\n", "      <td>3099</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Ventral posterolateral nucleus of the thalamus</td>\n", "      <td>VPL</td>\n", "      <td>3099</td>\n", "      <td>3108</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Thalamus</td>\n", "      <td>TH</td>\n", "      <td>3108</td>\n", "      <td>3109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Ventral posterolateral nucleus of the thalamus</td>\n", "      <td>VPL</td>\n", "      <td>3109</td>\n", "      <td>3140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Ventral posteromedial nucleus of the thalamus</td>\n", "      <td>VPM</td>\n", "      <td>3140</td>\n", "      <td>3894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Ventral posterolateral nucleus of the thalamus</td>\n", "      <td>VPL</td>\n", "      <td>3894</td>\n", "      <td>4301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Zona incerta</td>\n", "      <td>ZI</td>\n", "      <td>4301</td>\n", "      <td>4424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>fiber tracts</td>\n", "      <td>fiber tracts</td>\n", "      <td>4424</td>\n", "      <td>4566</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>internal capsule</td>\n", "      <td>int</td>\n", "      <td>4566</td>\n", "      <td>5031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>fiber tracts</td>\n", "      <td>fiber tracts</td>\n", "      <td>5031</td>\n", "      <td>5086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>optic tract</td>\n", "      <td>opt</td>\n", "      <td>5086</td>\n", "      <td>5270</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>Medial amygdalar nucleus</td>\n", "      <td>MEA</td>\n", "      <td>5270</td>\n", "      <td>5861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Posterior amygdalar nucleus</td>\n", "      <td>PA</td>\n", "      <td>5861</td>\n", "      <td>6116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>Cortical amygdalar area posterior part medial ...</td>\n", "      <td>COApm</td>\n", "      <td>6116</td>\n", "      <td>6117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>Posterior amygdalar nucleus</td>\n", "      <td>PA</td>\n", "      <td>6117</td>\n", "      <td>6126</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>Cortical amygdalar area posterior part medial ...</td>\n", "      <td>COApm</td>\n", "      <td>6126</td>\n", "      <td>6748</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          region_name region_acronym  \\\n", "0            Primary somatosensory area trunk layer 1        SSp-tr1   \n", "1          Primary somatosensory area trunk layer 2/3      SSp-tr2/3   \n", "2            Primary somatosensory area trunk layer 4        SSp-tr4   \n", "3            Primary somatosensory area trunk layer 5        SSp-tr5   \n", "4           Primary somatosensory area trunk layer 6a       SSp-tr6a   \n", "5      Primary somatosensory area lower limb layer 6a       SSp-ll6a   \n", "6      Primary somatosensory area lower limb layer 6b       SSp-ll6b   \n", "7                supra-callosal cerebral white matter           scwm   \n", "8                                corpus callosum body            ccb   \n", "9                                              alveus            alv   \n", "10                                          Field CA1            CA1   \n", "11                                          Field CA2            CA2   \n", "12                                          Field CA3            CA3   \n", "13                                             alveus            alv   \n", "14  Dorsal part of the lateral geniculate complex ...         LGd-co   \n", "15                 Lateral dorsal nucleus of thalamus             LD   \n", "16  Dorsal part of the lateral geniculate complex ...         LGd-co   \n", "17                 Lateral dorsal nucleus of thalamus             LD   \n", "18                                           Thalamus             TH   \n", "19     Ventral posterolateral nucleus of the thalamus            VPL   \n", "20                                           Thalamus             TH   \n", "21     Ventral posterolateral nucleus of the thalamus            VPL   \n", "22      Ventral posteromedial nucleus of the thalamus            VPM   \n", "23     Ventral posterolateral nucleus of the thalamus            VPL   \n", "24                                       Zona incerta             ZI   \n", "25                                       fiber tracts   fiber tracts   \n", "26                                   internal capsule            int   \n", "27                                       fiber tracts   fiber tracts   \n", "28                                        optic tract            opt   \n", "29                           Medial amygdalar nucleus            MEA   \n", "30                        Posterior amygdalar nucleus             PA   \n", "31  Cortical amygdalar area posterior part medial ...          COApm   \n", "32                        Posterior amygdalar nucleus             PA   \n", "33  Cortical amygdalar area posterior part medial ...          COApm   \n", "\n", "    depth_from  depth_to  \n", "0            0       113  \n", "1          113       449  \n", "2          449       571  \n", "3          571       887  \n", "4          887       948  \n", "5          948      1173  \n", "6         1173      1213  \n", "7         1213      1274  \n", "8         1274      1346  \n", "9         1346      1397  \n", "10        1397      1672  \n", "11        1672      1754  \n", "12        1754      2365  \n", "13        2365      2406  \n", "14        2457      2508  \n", "15        2508      2523  \n", "16        2523      2569  \n", "17        2569      3078  \n", "18        3078      3099  \n", "19        3099      3108  \n", "20        3108      3109  \n", "21        3109      3140  \n", "22        3140      3894  \n", "23        3894      4301  \n", "24        4301      4424  \n", "25        4424      4566  \n", "26        4566      5031  \n", "27        5031      5086  \n", "28        5086      5270  \n", "29        5270      5861  \n", "30        5861      6116  \n", "31        6116      6117  \n", "32        6117      6126  \n", "33        6126      6748  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 46, "id": "6c3c4423", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 600x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_brain_regions_by_depth(df, max_depth=3500):\n", "    \"\"\"\n", "    Plot brain region names as ticks aligned by their depth ranges,\n", "    only including regions within the max_depth.\n", "\n", "    Parameters:\n", "    - df (pd.DataFrame): DataFrame with columns ['region_name', 'depth_from', 'depth_to']\n", "    - max_depth (int or float): maximum depth (µm) to show on y-axis\n", "    \"\"\"\n", "    # Filter regions within max_depth (any part of region inside 0-max_depth)\n", "    df_filtered = df[(df['depth_from'] <= max_depth) & (df['depth_to'] >= 0)].copy()\n", "\n", "    # Clip depths at 0 and max_depth to avoid showing beyond limits\n", "    df_filtered['depth_from'] = df_filtered['depth_from'].clip(lower=0, upper=max_depth)\n", "    df_filtered['depth_to'] = df_filtered['depth_to'].clip(lower=0, upper=max_depth)\n", "\n", "    # Compute region centers for y-ticks\n", "    depth_centers = (df_filtered['depth_from'] + df_filtered['depth_to']) / 2\n", "\n", "    fig, ax = plt.subplots(figsize=(6, 10))\n", "\n", "    # Set y-ticks at region centers with region names\n", "    ax.set_yticks(depth_centers)\n", "    ax.set_yticklabels(df_filtered['region_name'], fontsize=9)\n", "\n", "    # Invert y-axis so surface (0) is on top\n", "    ax.invert_yaxis()\n", "\n", "    # Set y limits from 0 to max_depth\n", "    ax.set_ylim(max_depth, 0)\n", "\n", "    # Remove x-axis ticks and labels\n", "    ax.set_xticks([])\n", "    ax.set_xlabel('')  # remove xlabel if any\n", "\n", "    ax.set_ylabel('Depth from brain surface (µm)')\n", "    ax.set_title('Brain regions by depth')\n", "\n", "    # Get current x-axis limits for text positioning\n", "    x_min, x_max = ax.get_xlim()\n", "    x_text = x_max + 0.02 * (x_max - x_min)  # 2% margin to the right\n", "\n", "    # Add horizontal lines showing region boundaries with depth_to label on right side with margin\n", "    for _, row in df_filtered.iterrows():\n", "        # Draw horizontal lines\n", "        ax.axhline(row['depth_from'], color='gray', linestyle='--', linewidth=0.5)\n", "        ax.axhline(row['depth_to'], color='gray', linestyle='--', linewidth=0.5)\n", "\n", "        # Annotate depth_to on right side with offset\n", "        ax.text(\n", "            x=x_text,\n", "            y=row['depth_to'], \n", "            s=f\"{int(row['depth_to'])} µm\",\n", "            va='center',\n", "            ha='left',\n", "            fontsize=7,\n", "            color='gray'\n", "        )\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Usage example:\n", "trajectory_path=r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\trajectory_1.csv\"\n", "df_trajectory = pd.read_csv(trajectory_path)\n", "plot_brain_regions_by_depth(df_trajectory)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}