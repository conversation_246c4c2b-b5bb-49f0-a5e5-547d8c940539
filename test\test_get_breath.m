% --- Load channel 1 ---
basepath = 'Z:\users\izouridis\projects\bal_npx\data\b18\b18_p1_r1_g0\';
load(fullfile(basepath, 'channel_0.mat'));

% --- Create FieldTrip data structure ---
data = [];
data.label{1} = 'piezo';
data.trial{1} = channel_0;
data.time{1} = time_axis;
data.fsample = 30303;

cfg = [];
cfg.demean           = 'yes';
cfg.polyremoval      = 'yes';
cfg.polyorder        = 2;

% Bandpass filter settings
cfg.bpfilter         = 'yes';
cfg.bpfreq           = [1 6];
cfg.bpfilttype       = 'but';         % Butterworth filter
cfg.bpfiltord        = 2;             % 2th order
cfg.bpinstabilityfix = 'reduce';      % Fix filter instability

data_demeaned = ft_preprocessing(cfg, data);

% --- Resample ---
resamplefs = 1000;

cfg = [];
cfg.resamplefs = resamplefs;
data_resampled = ft_resampledata(cfg, data_demeaned);

% Extract signal and time
signal = data_resampled.trial{1};
time = data_resampled.time{1};

% Standardize the signal
signal_z = zscore(signal);

% --- Peak detection ---
min_breath_rate = 1;  % Hz
max_breath_rate = 5;  % Hz
resamplefs = data_resampled.fsample;  % should be 1000 Hz from your resampling

min_peak_distance = 1 / max_breath_rate;  % in seconds (0.2s)
max_peak_distance = 1 / min_breath_rate;  % in seconds (1s)

[pks, locs] = findpeaks(signal_z, time, ...
    'MinPeakDistance', min_peak_distance, ...
    'MinPeakProminence', 0.5);  % adjust based on signal amplitude

% --- Remove peaks that are too far apart (> 1s) ---
ibi = diff(locs);  % inter-breath intervals
valid_idx = ibi <= max_peak_distance;  % keep only those within range

locs_valid = locs([true valid_idx]);  % drop the last point if needed

% --- Compute instantaneous rate (Hz) ---
ibi_valid = diff(locs_valid);  % seconds
rate_inst = 1 ./ ibi_valid;
rate_time = locs_valid(1:end-1) + ibi_valid/2;  % assign to midpoints

% --- Interpolate to original time vector ---
breath_rate = interp1(rate_time, rate_inst, time, 'linear', 'extrap');

% Optional: ensure values outside valid times are set to NaN
breath_rate(time < rate_time(1) | time > rate_time(end)) = NaN;

% Plot (if needed)
plot(time, breath_rate);
xlabel('Time (s)');
ylabel('Breathing Rate (Hz)');
title('Instantaneous Breathing Rate (1–5 Hz)');

% --- Manual annotation ---
cfg = [];
cfg.artfctdef.bal_on.artifact = [];
cfg.artfctdef.bal_off.artifact = [];
cfg.viewmode = 'vertical';
cfg.blocksize = 100;  % in seconds
cfg = ft_databrowser(cfg, data_resampled);

% % --- Extract annotations in seconds ---
% bal_on_sec = cfg.artfctdef.bal_on.artifact / resamplefs;
% bal_off_sec = cfg.artfctdef.bal_off.artifact / resamplefs;

% --- Save annotation files ---
% writematrix(bal_on_sec, fullfile(basepath, 'bal_on_sec.txt'), 'Delimiter', '\t');
% writematrix(bal_off_sec, fullfile(basepath, 'bal_off_sec.txt'), 'Delimiter', '\t');