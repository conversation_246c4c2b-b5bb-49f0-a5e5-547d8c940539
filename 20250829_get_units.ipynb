{"cells": [{"cell_type": "markdown", "id": "1894c465", "metadata": {}, "source": ["# Units and Cortex Assignment (Ctx only)\n", "\n", "This simplified notebook:\n", "- Loads units from Kilosort outputs\n", "- Assigns all units to cortex (Ctx)\n", "- Loads balloon on/off events and applies per-animal time filtering\n", "- Saves updates back to each recording's unit_summary.pkl\n", "\n", "Removed: trajectory extraction/processing and region-based statistics."]}, {"cell_type": "markdown", "id": "f0e41593", "metadata": {}, "source": ["## Table of contents\n", "1. Configuration\n", "2. Helpers\n", "3. Step 1 — Extract units and write unit_summary.pkl\n", "4. Step 2 — Assign cortex region (Ctx) to all units\n", "5. Step 3 — Load and filter balloon events (per animal)\n", "6. Verification — Per-animal summary after filtering"]}, {"cell_type": "code", "execution_count": 17, "id": "81b2ecde", "metadata": {}, "outputs": [], "source": ["# Imports\n", "from pathlib import Path\n", "from typing import List, Dict, Optional\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# External utility for extracting units from a Kilosort folder\n", "from npx_utils import extract_unit_data\n"]}, {"cell_type": "markdown", "id": "112e1fbf", "metadata": {}, "source": ["## 1) Configuration\n", "Edit dataset paths and IDs for your environment. The time limits control balloon-event filtering per animal."]}, {"cell_type": "code", "execution_count": 18, "id": "ae52465e", "metadata": {}, "outputs": [], "source": ["from typing import Dict, List\n", "from pathlib import Path\n", "\n", "# Time limits (seconds) for filtering balloon events per animal\n", "tmax_dict: Dict[str, float] = {\"b24\": -1, \"b25\": -1, \"b27\":-1, \"b28\": -1, \"b30\": -1}\n", "\n", "# Kilosort dataset folders\n", "dataset_paths: List[Path] = [\n", "    #Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b27\\\\b27_p1_r1_g0_p\\\\catgt\"),\n", "    #Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b28\\\\b28_p2_r1_g0\\\\catgt\"),\n", "    Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b30\\\\b30_p1_r1_g0\\\\catgt\")\n", "]\n", "\n", "# Recording ID to animal ID mapping\n", "recording_to_animal_id: Dict[str, str] = {\n", "    \"b24_p1_r1_g0_p\": \"b24\",\n", "    \"b25_p1_r2_g0_p\": \"b25\",\n", "    \"b27_p1_r1_g0_p\": \"b27\",\n", "    \"b28_p2_r1_g0\": \"b28\",\n", "    \"b30_p1_r1_g0\": \"b30\"\n", "}\n", "\n", "# Firing-rate filter (Hz)\n", "MIN_FIRING_RATE: float = 0.05\n"]}, {"cell_type": "markdown", "id": "b5f8c923", "metadata": {}, "source": ["## 2) Step 1 — Extract units and write unit_summary.pkl\n", "This runs extract_unit_data per dataset, applies a minimum firing-rate filter, and saves unit_summary.pkl."]}, {"cell_type": "code", "execution_count": 19, "id": "846835b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Step 1] Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt (rid=b30_p1_r1_g0, animal=b30)\n", "[  5/15]  33.33% | Unit 292 | ETC: 00:33\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  9/15]  60.00% | Unit 383 | ETC: 00:27\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 10/15]  66.67% | Unit 389 | ETC: 00:27\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 12/15]  80.00% | Unit 34 | ETC: 00:174\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 13/15]  86.67% | Unit 74 | ETC: 00:12\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 14/15]  93.33% | Unit 258 | ETC: 00:06\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 15/15] 100.00% | Unit 324 | ETC: 00:00\n", "✅ Done.\n", "  ✔ Saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt\\unit_summary.pkl\n"]}], "source": ["from npx_utils import extract_unit_data\n", "\n", "for dataset_path in dataset_paths:\n", "    rid = dataset_path.parent.name\n", "    animal_id = recording_to_animal_id.get(rid, \"\")\n", "    print(f\"[Step 1] Processing units: {dataset_path} (rid={rid}, animal={animal_id})\")\n", "    try:\n", "        df = extract_unit_data(datapath=str(dataset_path), animal_id=animal_id, quality=['good', 'mua'])\n", "        df = df[df['mean_firing_rate'] > MIN_FIRING_RATE]\n", "        out_pkl = dataset_path / 'unit_summary.pkl'\n", "        df.to_pickle(out_pkl)\n", "        print(f\"  ✔ Saved: {out_pkl}\")\n", "    except Exception as exc:\n", "        print(f\"  ❌ Failed {dataset_path}: {exc}\")"]}, {"cell_type": "markdown", "id": "8aa0178e", "metadata": {}, "source": ["## 3) Step 2 — Assign cortex region (Ctx) to all units\n", "For each recording, set three columns on the unit DataFrame and persist back to unit_summary.pkl: region_name='Cortex', region_acronym='Ctx', brain_region='Ctx'."]}, {"cell_type": "code", "execution_count": 20, "id": "55101a0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  ✔ Updated and saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt\\unit_summary.pkl\n"]}], "source": ["import pandas as pd\n", "\n", "for dataset_path in dataset_paths:\n", "    out_pkl = dataset_path / 'unit_summary.pkl'\n", "    if not out_pkl.exists():\n", "        print(f\"[Step 2] ⚠ Missing {out_pkl} — run Step 1 first.\")\n", "        continue\n", "    try:\n", "        df = pd.read_pickle(out_pkl)\n", "        df['region_name'] = 'Cortex'\n", "        df['region_acronym'] = 'Ctx'\n", "        df['brain_region'] = 'Ctx'\n", "        df.to_pickle(out_pkl)\n", "        print(f\"  ✔ Updated and saved: {out_pkl}\")\n", "    except Exception as exc:\n", "        print(f\"[Step 2] ❌ Failed {dataset_path}: {exc}\")\n"]}, {"cell_type": "markdown", "id": "19b03f4f", "metadata": {}, "source": ["## 4) Step 3 — Load and filter balloon events (per animal)\n", "Reads bal_on_sec.txt and bal_off_sec.txt from the recording folder, filters by tmax_dict per animal, and stores filtered events in unit_summary.pkl."]}, {"cell_type": "code", "execution_count": 21, "id": "31d68f03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Step 3] b30_p1_r1_g0 (b30) ✔ Saved 5->5 events in unit_summary.pkl (tmax=-1.0)\n"]}], "source": ["for dataset_path in dataset_paths:\n", "    rid = dataset_path.parent.name\n", "    animal_id = recording_to_animal_id.get(rid, \"\")\n", "    basepath = dataset_path.parent\n", "    on_path = basepath / 'bal_on_sec.txt'\n", "    off_path = basepath / 'bal_off_sec.txt'\n", "    out_pkl = dataset_path / 'unit_summary.pkl'\n", "\n", "    status = f\"[Step 3] {rid} ({animal_id})\"\n", "\n", "    if not animal_id:\n", "        print(f\"{status} ❌ Animal ID not found. Aborting.\")\n", "        raise SystemExit(1)\n", "\n", "    if not on_path.exists() or not off_path.exists():\n", "        print(f\"{status} ⚠ Missing balloon files\")\n", "        continue\n", "    if not out_pkl.exists():\n", "        print(f\"{status} ⚠ Missing {out_pkl.name} — run Step 1 first.\")\n", "        continue\n", "\n", "    try:\n", "        bal_on = np.atleast_2d(np.loadtxt(on_path, delimiter='\\t'))\n", "        bal_off = np.atleast_2d(np.loadtxt(off_path, delimiter='\\t'))\n", "        before = len(bal_on)\n", "\n", "        tmax = float(tmax_dict.get(animal_id, -1))\n", "        if tmax > 0:\n", "            keep = (bal_on[:,0] < tmax) & (bal_on[:,1] < tmax) & \\\n", "                   (bal_off[:,0] < tmax) & (bal_off[:,1] < tmax)\n", "            bal_on, bal_off = bal_on[keep], bal_off[keep]\n", "\n", "        after = len(bal_on)\n", "\n", "        df = pd.read_pickle(out_pkl)\n", "        df['balloon_on_sec']  = [bal_on.tolist()] * len(df)\n", "        df['balloon_off_sec'] = [bal_off.tolist()] * len(df)\n", "        df.to_pickle(out_pkl)\n", "\n", "        print(f\"{status} ✔ Saved {before}->{after} events in {out_pkl.name} (tmax={tmax})\")\n", "\n", "    except Exception as exc:\n", "        print(f\"{status} ❌ Balloon processing failed: {exc}\")\n"]}, {"cell_type": "markdown", "id": "fdda0249", "metadata": {}, "source": ["## 5) Verification — Per-animal summary after filtering\n", "Displays per-recording counts of units and balloon trials to validate that filtering worked as expected."]}, {"cell_type": "code", "execution_count": 22, "id": "5c23ce4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Unit Summary ===\n", "\n", "|    | recording_id   | animal_id   |   units |   good_units |   mua_units |   balloon_on |   balloon_off |\n", "|----|----------------|-------------|---------|--------------|-------------|--------------|---------------|\n", "|  0 | b30_p1_r1_g0   | b30         |      15 |           11 |           4 |            5 |             5 |\n"]}], "source": ["summary_rows = []\n", "for dataset_path in dataset_paths:\n", "    rid = dataset_path.parent.name\n", "    animal_id = recording_to_animal_id.get(rid, \"\")\n", "    out_pkl = dataset_path / \"unit_summary.pkl\"\n", "    if not out_pkl.exists():\n", "        continue\n", "\n", "    try:\n", "        df = pd.read_pickle(out_pkl)\n", "\n", "        summary_rows.append({\n", "            \"recording_id\": rid,\n", "            \"animal_id\": animal_id,\n", "            \"units\": len(df),\n", "            \"good_units\": int((df.get(\"quality\") == \"good\").sum()) if \"quality\" in df else None,\n", "            \"mua_units\": int((df.get(\"quality\") == \"mua\").sum()) if \"quality\" in df else None,\n", "            \"balloon_on\": len(df.get(\"balloon_on_sec\", [[]])[0]) if \"balloon_on_sec\" in df else 0,\n", "            \"balloon_off\": len(df.get(\"balloon_off_sec\", [[]])[0]) if \"balloon_off_sec\" in df else 0,\n", "        })\n", "    except Exception as exc:\n", "        print(f\"  ⚠ Summary failed for {rid}: {exc}\")\n", "\n", "\n", "summary = pd.DataFrame(summary_rows)\n", "\n", "from tabulate import tabulate\n", "\n", "if not summary.empty:\n", "    print(\"\\n=== Unit Summary ===\\n\")\n", "    print(tabulate(summary, headers=\"keys\", tablefmt=\"github\", showindex=True))\n", "else:\n", "    print(\"No summaries available — check dataset_paths.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}