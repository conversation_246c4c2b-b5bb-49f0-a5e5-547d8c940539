{"cells": [{"cell_type": "code", "execution_count": 16, "id": "2219caea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Good Unit 14: 7762 spikes, main channel = 9\n", "Good Unit 19: 10706 spikes, main channel = 16\n", "Good Unit 35: 4017 spikes, main channel = 44\n", "Good Unit 36: 1940 spikes, main channel = 48\n", "Good Unit 38: 4940 spikes, main channel = 55\n", "Good Unit 41: 1035 spikes, main channel = 54\n", "Good Unit 42: 1715 spikes, main channel = 52\n", "Good Unit 48: 15185 spikes, main channel = 63\n", "Good Unit 49: 7755 spikes, main channel = 61\n", "Good Unit 53: 4743 spikes, main channel = 63\n", "Good Unit 61: 4859 spikes, main channel = 75\n", "Good Unit 68: 687 spikes, main channel = 78\n", "Good Unit 75: 4168 spikes, main channel = 95\n", "Good Unit 86: 12237 spikes, main channel = 107\n", "Good Unit 109: 8178 spikes, main channel = 146\n", "Good Unit 111: 2824 spikes, main channel = 147\n", "Good Unit 123: 41878 spikes, main channel = 148\n", "Good Unit 124: 675 spikes, main channel = 150\n", "Good Unit 125: 2411 spikes, main channel = 150\n", "Good Unit 129: 5906 spikes, main channel = 156\n", "Good Unit 130: 1362 spikes, main channel = 153\n", "Good Unit 135: 905 spikes, main channel = 155\n", "Good Unit 136: 2921 spikes, main channel = 152\n", "Good Unit 139: 7602 spikes, main channel = 152\n", "Good Unit 141: 1855 spikes, main channel = 157\n", "Good Unit 144: 17653 spikes, main channel = 156\n", "Good Unit 145: 961 spikes, main channel = 157\n", "Good Unit 147: 11906 spikes, main channel = 158\n", "Good Unit 148: 25641 spikes, main channel = 156\n", "Good Unit 151: 10187 spikes, main channel = 157\n", "Good Unit 152: 10671 spikes, main channel = 159\n", "Good Unit 167: 596 spikes, main channel = 183\n", "Good Unit 170: 19051 spikes, main channel = 196\n", "Good Unit 171: 15606 spikes, main channel = 201\n", "Good Unit 172: 12997 spikes, main channel = 201\n", "Good Unit 173: 20760 spikes, main channel = 200\n", "Good Unit 174: 1746 spikes, main channel = 202\n", "Good Unit 175: 5061 spikes, main channel = 205\n", "Good Unit 176: 10607 spikes, main channel = 204\n", "Good Unit 177: 553 spikes, main channel = 207\n", "Good Unit 178: 4336 spikes, main channel = 211\n", "Good Unit 179: 13665 spikes, main channel = 209\n", "Good Unit 180: 228 spikes, main channel = 213\n", "Good Unit 181: 274 spikes, main channel = 219\n", "Good Unit 212: 21653 spikes, main channel = None\n", "Good Unit 213: 910 spikes, main channel = None\n", "Good Unit 214: 1105 spikes, main channel = None\n", "Good Unit 215: 5792 spikes, main channel = None\n", "Good Unit 217: 4877 spikes, main channel = None\n"]}], "source": ["import os\n", "import numpy as np\n", "import spikeinterface.extractors as se\n", "\n", "# Path to Kilosort4 output\n", "folder = r\"Z:\\users\\izouridis\\test_sorting_npx2\\b11\\b11_p1_r1_g0\\b11_p1_r1_g0_imec0\\kilosort4\"\n", "\n", "# Load the sorting output\n", "sorting = se.read_kilosort(folder)\n", "\n", "# Get unit IDs and KS labels\n", "unit_ids = sorting.get_unit_ids()\n", "kslabels = sorting.get_property(\"KSLabel\")\n", "\n", "# Keep only 'good' units\n", "good_units = [unit for unit, label in zip(unit_ids, kslabels) if label == \"good\"]\n", "sorting = sorting.select_units(good_units)\n", "\n", "# Load Kilosort template info\n", "templates = np.load(os.path.join(folder, 'templates.npy'))  # shape: (n_templates, n_timepoints, n_channels)\n", "channel_map = np.load(os.path.join(folder, 'channel_map.npy'))  # maps local to global channel index\n", "\n", "# Determine main channel per template (max peak-to-peak amplitude)\n", "main_channels = []\n", "for template in templates:\n", "    ptp = template.ptp(axis=0)\n", "    max_ch_idx = np.argmax(ptp)\n", "    main_channels.append(channel_map[max_ch_idx])\n", "main_channels = np.array(main_channels)\n", "\n", "# Map good units to their main channel (assume unit_id == template_id)\n", "unit_main_channels = {}\n", "for unit_id in sorting.get_unit_ids():\n", "    if unit_id < len(main_channels):\n", "        unit_main_channels[unit_id] = main_channels[unit_id]\n", "    else:\n", "        unit_main_channels[unit_id] = None\n", "\n", "# Print result\n", "for unit_id in sorting.get_unit_ids():\n", "    spike_times = sorting.get_unit_spike_train(unit_id=unit_id)\n", "    main_ch = unit_main_channels.get(unit_id, \"N/A\")\n", "    print(f\"Good Unit {unit_id}: {len(spike_times)} spikes, main channel = {main_ch}\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "f6281879", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import spikeinterface.extractors as se\n", "\n", "def load_good_units_with_main_channel(folder, min_spikes=100):\n", "    \"\"\"\n", "    Load Kilosort4 sorting from folder,\n", "    keep only good units with spike count > min_spikes,\n", "    and return unit info with main channel for each.\n", "\n", "    Parameters\n", "    ----------\n", "    folder : str\n", "        Path to Kilosort4 output folder.\n", "    min_spikes : int, optional\n", "        Minimum number of spikes required to keep a unit (default 100).\n", "\n", "    Returns\n", "    -------\n", "    good_units_info : dict\n", "        Dict mapping unit_id -> dict with keys:\n", "            'spike_count': int,\n", "            'main_channel': int or None,\n", "            'spike_times': np.n<PERSON><PERSON>\n", "    \"\"\"\n", "\n", "    # Load sorting output\n", "    sorting = se.read_kilosort(folder)\n", "\n", "    # Get all unit IDs and their KS labels\n", "    unit_ids = sorting.get_unit_ids()\n", "    kslabels = sorting.get_property(\"KSLabel\")\n", "\n", "    # Filter units by <PERSON><PERSON><PERSON><PERSON> and spike count\n", "    good_units = []\n", "    for unit, label in zip(unit_ids, kslabels):\n", "        if label == \"good\":\n", "            spike_times = sorting.get_unit_spike_train(unit_id=unit)\n", "            if len(spike_times) > min_spikes:\n", "                good_units.append(unit)\n", "\n", "    # Select only good units\n", "    sorting = sorting.select_units(good_units)\n", "\n", "    # Load templates and channel map\n", "    templates = np.load(os.path.join(folder, 'templates.npy'))  # (n_templates, n_timepoints, n_channels)\n", "    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))  # maps local to global channel index\n", "\n", "    # Determine main channel per template (max peak-to-peak amplitude)\n", "    main_channels = []\n", "    for template in templates:\n", "        ptp = template.ptp(axis=0)\n", "        max_ch_idx = np.argmax(ptp)\n", "        main_channels.append(channel_map[max_ch_idx])\n", "    main_channels = np.array(main_channels)\n", "\n", "    # Map good units to main channel (assume unit_id == template_id)\n", "    good_units_info = {}\n", "    for unit_id in sorting.get_unit_ids():\n", "        spike_times = sorting.get_unit_spike_train(unit_id=unit_id)\n", "        main_ch = main_channels[unit_id] if unit_id < len(main_channels) else None\n", "        good_units_info[unit_id] = {\n", "            'spike_count': len(spike_times),\n", "            'main_channel': main_ch,\n", "            'spike_times': spike_times,\n", "        }\n", "\n", "    return good_units_info\n"]}, {"cell_type": "code", "execution_count": 18, "id": "99dcf31e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unit 14: 7762 spikes, main channel = 9\n", "Unit 19: 10706 spikes, main channel = 16\n", "Unit 35: 4017 spikes, main channel = 44\n", "Unit 36: 1940 spikes, main channel = 48\n", "Unit 38: 4940 spikes, main channel = 55\n", "Unit 41: 1035 spikes, main channel = 54\n", "Unit 42: 1715 spikes, main channel = 52\n", "Unit 48: 15185 spikes, main channel = 63\n", "Unit 49: 7755 spikes, main channel = 61\n", "Unit 53: 4743 spikes, main channel = 63\n", "Unit 61: 4859 spikes, main channel = 75\n", "Unit 68: 687 spikes, main channel = 78\n", "Unit 75: 4168 spikes, main channel = 95\n", "Unit 86: 12237 spikes, main channel = 107\n", "Unit 109: 8178 spikes, main channel = 146\n", "Unit 111: 2824 spikes, main channel = 147\n", "Unit 123: 41878 spikes, main channel = 148\n", "Unit 124: 675 spikes, main channel = 150\n", "Unit 125: 2411 spikes, main channel = 150\n", "Unit 129: 5906 spikes, main channel = 156\n", "Unit 130: 1362 spikes, main channel = 153\n", "Unit 135: 905 spikes, main channel = 155\n", "Unit 136: 2921 spikes, main channel = 152\n", "Unit 139: 7602 spikes, main channel = 152\n", "Unit 141: 1855 spikes, main channel = 157\n", "Unit 144: 17653 spikes, main channel = 156\n", "Unit 145: 961 spikes, main channel = 157\n", "Unit 147: 11906 spikes, main channel = 158\n", "Unit 148: 25641 spikes, main channel = 156\n", "Unit 151: 10187 spikes, main channel = 157\n", "Unit 152: 10671 spikes, main channel = 159\n", "Unit 167: 596 spikes, main channel = 183\n", "Unit 170: 19051 spikes, main channel = 196\n", "Unit 171: 15606 spikes, main channel = 201\n", "Unit 172: 12997 spikes, main channel = 201\n", "Unit 173: 20760 spikes, main channel = 200\n", "Unit 174: 1746 spikes, main channel = 202\n", "Unit 175: 5061 spikes, main channel = 205\n", "Unit 176: 10607 spikes, main channel = 204\n", "Unit 177: 553 spikes, main channel = 207\n", "Unit 178: 4336 spikes, main channel = 211\n", "Unit 179: 13665 spikes, main channel = 209\n", "Unit 180: 228 spikes, main channel = 213\n", "Unit 181: 274 spikes, main channel = 219\n", "Unit 212: 21653 spikes, main channel = None\n", "Unit 213: 910 spikes, main channel = None\n", "Unit 214: 1105 spikes, main channel = None\n", "Unit 215: 5792 spikes, main channel = None\n", "Unit 217: 4877 spikes, main channel = None\n"]}], "source": ["folder = r\"Z:\\users\\izouridis\\test_sorting_npx2\\b11\\b11_p1_r1_g0\\b11_p1_r1_g0_imec0\\kilosort4\"\n", "good_units_info = load_good_units_with_main_channel(folder, min_spikes=100)\n", "\n", "for unit_id, info in good_units_info.items():\n", "    print(f\"Unit {unit_id}: {info['spike_count']} spikes, main channel = {info['main_channel']}\")\n"]}, {"cell_type": "code", "execution_count": 19, "id": "de24cc43", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import spikeinterface.extractors as se\n", "import pandas as pd\n", "\n", "def load_good_units_with_main_channel_df(folder, min_spikes=100):\n", "    \"\"\"\n", "    Load Kilosort4 sorting from folder,\n", "    keep only good units with spike count > min_spikes,\n", "    and return a pandas DataFrame with unit info.\n", "\n", "    Parameters\n", "    ----------\n", "    folder : str\n", "        Path to Kilosort4 output folder.\n", "    min_spikes : int, optional\n", "        Minimum number of spikes required to keep a unit (default 100).\n", "\n", "    Returns\n", "    -------\n", "    df : pandas.DataFrame\n", "        DataFrame with columns:\n", "          - 'unit_id' (int)\n", "          - 'spike_count' (int)\n", "          - 'main_channel' (int or None)\n", "          - 'spike_times' (np.n<PERSON><PERSON>)\n", "    \"\"\"\n", "\n", "    sorting = se.read_kilosort(folder)\n", "    unit_ids = sorting.get_unit_ids()\n", "    kslabels = sorting.get_property(\"KSLabel\")\n", "\n", "    good_units = []\n", "    for unit, label in zip(unit_ids, kslabels):\n", "        if label == \"good\":\n", "            spike_times = sorting.get_unit_spike_train(unit_id=unit)\n", "            if len(spike_times) > min_spikes:\n", "                good_units.append(unit)\n", "\n", "    sorting = sorting.select_units(good_units)\n", "\n", "    templates = np.load(os.path.join(folder, 'templates.npy'))\n", "    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))\n", "\n", "    main_channels = []\n", "    for template in templates:\n", "        ptp = template.ptp(axis=0)\n", "        max_ch_idx = np.argmax(ptp)\n", "        main_channels.append(channel_map[max_ch_idx])\n", "    main_channels = np.array(main_channels)\n", "\n", "    data = []\n", "    for unit_id in sorting.get_unit_ids():\n", "        spike_times = sorting.get_unit_spike_train(unit_id=unit_id)\n", "        main_ch = main_channels[unit_id] if unit_id < len(main_channels) else None\n", "        data.append({\n", "            'unit_id': unit_id,\n", "            'spike_count': len(spike_times),\n", "            'main_channel': main_ch,\n", "            'spike_times': spike_times,\n", "        })\n", "\n", "    df = pd.DataFrame(data)\n", "    return df\n"]}, {"cell_type": "code", "execution_count": 20, "id": "34ed550d", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "unit_id", "rawType": "int64", "type": "integer"}, {"name": "spike_count", "rawType": "int64", "type": "integer"}, {"name": "main_channel", "rawType": "float64", "type": "float"}, {"name": "spike_times", "rawType": "object", "type": "string"}], "ref": "dbdb9a11-d830-4e49-8b84-640754de45c4", "rows": [["0", "14", "7762", "9.0", "[    4310     4419    13430 ... 74490596 74510051 74510167]"], ["1", "19", "10706", "16.0", "[    2587    13570    13658 ... 74805128 74805227 74805332]"], ["2", "35", "4017", "44.0", "[     684    39468    87918 ... 74752766 74767807 74782596]"], ["3", "36", "1940", "48.0", "[   87935   130281   148462 ... 71896688 72622929 72660326]"], ["4", "38", "4940", "55.0", "[    3133     4026    29529 ... 74976708 74983443 74995184]"], ["5", "41", "1035", "54.0", "[   98001   104292   104566 ... 74277259 74286396 74286611]"], ["6", "42", "1715", "52.0", "[    6293    13423    37878 ... 73198805 73252200 73610324]"], ["7", "48", "15185", "63.0", "[     204      524     1126 ... 74800460 74861383 74998366]"], ["8", "49", "7755", "61.0", "[    6058    34740    53950 ... 74991941 74993754 74994897]"], ["9", "53", "4743", "63.0", "[    4556     4770     5026 ... 74942856 74943315 74995724]"], ["10", "61", "4859", "75.0", "[   67675    86276   129911 ... 74784979 74830094 74831381]"], ["11", "68", "687", "78.0", "[  314242   443989   472234   562057   661743   670965   760116   882454\n   947411   967303   967469  1055152  1055304  1239346  1326596  1492943\n  1493097  1523447  1571956  1630810  1630950  1639408  1659965  1727324\n  1737191  1775549  1834524  1834669  1873405  1943357  1952084  1952217\n  1981307  2001909  2021024  2030464  2108186  2137909  2185541  2233586\n  2243256  2301803  2428756  2428963  2448454  2476554  2487308  2506460\n  2565330  2574036  2574184  2583889  2651047  2899783  2899930  2966482\n  2966652  2995474  3346157  3375137  3403516  3422357  3461118  3489893\n  3567123  3692348  3925195  4187748  4196005  4225408  4374299  4412205\n  4423373  4431572  4539442  4549271  4763810  5018550  5659645  5698169\n  5842948  5862099  5872793  5880537  5880714  5988666  6018673  6018901\n  6038433  6067339  6144761  6270622  6321131  6329926  6350268  6561191\n  6561465  6770345  6771050  6867260  6867467  7109755  7138618  7225608\n  7329719  7329855  7408032  7426281  7426440  7455434  7627470  7792931\n  7832804  7851007  7880416  7985833  7986095  7995072  7995299  8033770\n  8033924  8081891  8111065  8178613  8198121  8236027  8255326  8255522\n  8284353  8341714  8352884  8372770  8381299  8381424  8409206  8650638\n  8679471  8729230  8874376  8931022  8980156  9027977  9122144  9498381\n  9498611  9794590  9794759  9803622 10067631 10087996 10088183 10099735\n 10099870 10109138 10298187 10437782 10586878 10656180 11189776 11606546\n 11835442 12398000 12676723 12696569 12767632 13362395 14106505 14328947\n 14339455 14489542 14525443 14548268 14615963 14715940 14716096 14728080\n 14738009 14749000 14770226 15537562 15570117 15880543 15966153 15966283\n 15966418 16208623 16375136 16375310 16584104 16657224 16740187 16740740\n 16803543 16814246 16835281 16835436 16930313 16940562 16961526 17133430\n 17367469 17453890 17454044 17554026 17564692 17566289 17674988 17981601\n 18027049 18036693 18124288 18157393 18212387 18235348 18246673 18256226\n 18267613 18279003 18554336 18763924 19305587 19390518 19773227 19829206\n 19840511 19906830 19917836 19917967 19995596 20028627 20028801 20039063\n 20049823 20095238 20553135 20609528 20609770 21034618 21111421 21165932\n 21177390 21199019 21319679 21374444 21385053 21595377 21595649 21628565\n 21628816 21639154 21671692 21672197 21737463 21862156 21880221 22022484\n 22195399 22195694 22249702 22260625 22315086 22356240 22530566 22530705\n 22639082 22691632 22691792 22722993 22763726 22849822 22879364 22948453\n 23028234 23049864 23156473 23230675 23284654 23531287 23647976 23658878\n 23691685 23701690 23722563 23733617 23797445 23808501 23818768 23829270\n 23871427 23914317 24042331 24106120 24185225 24236376 24246954 24260077\n 24470141 24470304 24481084 24598726 24610387 24695742 25149997 25320824\n 25320964 25409402 25489211 25499423 25524505 25545907 25558864 25559007\n 25579628 25592623 25602764 25614154 25691470 25703467 25791062 25791298\n 25802927 25869219 25891310 25902471 25980282 26059098 26148462 26212230\n 26244643 26308688 27062467 27073337 27257910 27862431 27971373 28124140\n 28134799 28158319 28201291 28233304 28244257 28288176 28387523 28432958\n 28559314 28632651 28834714 28835043 28863682 28891992 28892148 28933495\n 28947455 28961071 28961637 28988523 29024124 29037121 29118759 29385649\n 29396693 29444068 29466159 29589580 29870052 30025711 30090359 30090702\n 30135317 30169316 30270302 30314971 30358204 30935281 30940899 30941206\n 30952204 31196263 31285975 31377561 31556988 31689167 31755273 31755440\n 32064259 32177014 32198056 32211445 32220656 32231997 32266415 32334443\n 32357940 32358250 32369869 32380050 32391571 32403261 32413876 32414026\n 32427293 32505768 32519511 32519656 32528921 32687097 32733299 32790176\n 32800595 32812061 32925517 32936856 33013781 33222424 33534036 33545464\n 33816131 33827856 34177417 34471338 34593546 34855646 35001157 35121786\n 35154590 35244041 35265396 35476208 35591076 35950434 35950626 36098070\n 36098220 36107156 36107585 36297021 36297247 36319725 36410990 36487313\n 36632268 36834498 37056741 37101903 37267076 37642617 37750567 37913925\n 38076260 38108022 38163168 38175153 38336088 38336241 39110032 39182802\n 39403888 41210281 41574609 41684010 41738832 41739454 41749191 41783910\n 41784106 41926347 42070100 42092536 42158899 42213358 42226110 42269042\n 43269900 43831753 43856399 44021148 44290037 44301062 44354825 44366094\n 44453885 44487796 44499133 44532315 44542886 45311949 45770846 45781962\n 45782454 45850140 45965300 45997971 46132565 46497345 46532244 46545622\n 46579648 46614652 46659928 46694363 46752588 46819889 46831250 46843292\n 46902221 46925565 46960526 46994224 46994366 47187599 48443571 48443721\n 48521725 48630104 48653300 48664101 48664236 49024105 49121920 49144594\n 49760172 49770020 49781598 49792068 49803658 49857399 51718113 51718911\n 51729466 51904324 52386243 52398317 52409881 52452826 52464035 52485398\n 52845895 53186913 53322231 53482341 53609650 53609793 53780545 53803755\n 53849088 53860971 53861168 53931100 53965295 54047715 54059437 54316052\n 54327075 54343007 55435971 55788213 55800060 55834609 55982281 56049118\n 56137126 56248193 56269997 56403435 56559443 56770388 56795063 56874292\n 56908641 56918947 56931174 56940606 56953687 56999774 57034690 57045885\n 57056206 57147204 57168905 57191307 57201220 57201344 57212689 57212854\n 57223680 57619597 58101432 58112469 58124977 58370049 58468293 59265363\n 59308773 59438712 59515154 59515288 59658449 59669251 59680883 59691682\n 59715236 59726291 60594702 60669288 60682959 61043464 61143222 61262923\n 61465055 61474790 61496221 61893806 61893964 62274277 62306056 62316564\n 62316724 62339563 62405823 62428253 62482905 62504328 62514063 62515557\n 62537955 62547912 62568911 63360568 63748242 63864404 63864590 63975291\n 64353500 64353800 64374880 64570640 65601291 65622273 65643115 65675863\n 65686427 65696160 65708595 65719891 65731279 65741521 65818429 65828166\n 65883412 65906537 66002575 66002704 66919460 66930813 67113340 67120041\n 67120159 67241690 67588274 67598873 67599035 67620640 67620941 68099904\n 68367863 68398427 68409286 68441848 68494589 68857648 69206213 69330446\n 69350330 69350456 69455065 69497831 69497971 70194989 70527640 70528127\n 70603904 72447978 72448147 72458410 72489570 72489676 72500122]"], ["12", "75", "4168", "95.0", "[   61740    66089    75660 ... 74819908 74898187 74970908]"], ["13", "86", "12237", "107.0", "[    1445     6692    18278 ... 74986783 74987067 74995429]"], ["14", "109", "8178", "146.0", "[   34141    34407    34809 ... 71680808 72158399 72158750]"], ["15", "111", "2824", "147.0", "[   26229    28325    28716 ... 69724524 69724721 70760245]"], ["16", "123", "41878", "148.0", "[    2392     3172     8478 ... 74780810 74784753 74786036]"], ["17", "124", "675", "150.0", "[   57957    90351   104529   105211   122698   321231   322176   329785\n   332553   332674   533617   755028   755185  1143904  1150612  1150879\n  1481814  1482164  1482315  1486388  1488365  1539817  1716337  1761264\n  1957734  2083385  2160974  2164761  2172127  2172927  2274161  2408367\n  2529886  2530045  2530180  2531787  2532382  2532718  2731364  2731546\n  2731913  2857120  2988751  3359391  3588062  3729656  3757115  3757345\n  3757777  3757934  3823072  3853298  3856596  3979451  4048277  4048438\n  4048585  4048724  4085729  4085919  4086066  4086235  4338496  4602142\n  4774461  4775171  5305953  5403341  5529252  5656017  5656240  5748093\n  5748402  5809370  5889723  6043571  6172000  6223502  6223639  6223770\n  6223898  6617991  6824700  6824870  6825014  7025415  7172037  7172636\n  7191938  7435018  7435150  7435339  7435534  7725212  7725374  7749335\n  7749479  7749661  7749826  7785355  8135232  8135880  8167943  8283027\n  8283177  8283307  8525522  8525752  8525895  8677920  8891459  8891664\n  8891790  8892012  8892183  9242590  9458690 10104158 10296689 10343729\n 10343911 10344049 10433593 10487439 10680069 10732110 10732453 10732611\n 10732812 10877117 10971204 11059134 11059441 11059600 11362315 11362564\n 11362723 11362894 11575429 11759443 11759597 11759848 11959363 11959577\n 11959739 12330906 12331719 12350712 12481443 12481638 12481784 12481928\n 12502298 12513527 12513839 12513990 12514222 12630235 13829428 13829771\n 14230567 14331458 14331670 14496248 14496406 14496541 14496713 14586083\n 14711452 14713779 14799006 14799198 14799376 14799585 14799762 14884906\n 15035692 15035851 15036149 15330728 15330900 15331043 15331207 15406367\n 15408926 15646194 15647577 15663114 15663431 15948036 15972429 16006064\n 16042593 16131392 16162921 16163377 16271994 16272301 16272538 16272744\n 16349789 16351520 16395022 16396083 16515440 16515732 16518484 16534450\n 16658515 16658802 17482137 17627786 17628020 17628161 17628309 17637258\n 17639645 17798769 17799041 17812849 17813009 17873276 17880283 17880451\n 17880596 17880774 18062409 18108055 18108197 18108474 18120104 18123169\n 18177446 18177587 18177750 18382674 18627963 18853855 19012612 19419824\n 19442296 19442414 19442545 19467076 19513699 19916201 19916893 20137728\n 20169023 20199298 20304233 20330770 20330921 20331066 20428145 20634808\n 20635104 20724869 20791504 20791687 20791842 20898745 20898927 20899218\n 21039983 21040101 21264176 21270863 21329334 21329622 21338986 21380388\n 21713893 21868407 21869256 21873883 21921161 21921653 21921834 21922090\n 22246439 22451220 22456812 22463691 22666369 22666551 22666702 22666888\n 22690162 23253494 23424003 23430645 23442750 23458592 24430963 24443423\n 24656087 25470956 25597637 25651832 25666620 25666848 25667127 25670874\n 25960563 25960824 25962716 25963136 25965825 25966145 26028620 26101866\n 26102120 26405828 26420371 26658831 26767509 26767660 26767801 26975721\n 27053634 27054910 27097647 27097765 27097907 27104409 27104641 27104988\n 27253342 27323579 27334345 27481672 27591699 27591887 27792345 27960900\n 28260906 28261067 28265123 28302696 28302893 28330547 28330728 28331090\n 28693089 28740586 28740716 28740847 28740991 28771797 28783644 29431233\n 29503275 29778816 29960949 29967418 29967633 29967957 30403467 30638311\n 30644009 30671027 30675536 30676809 30680121 31045566 31046095 31046292\n 31046475 31046619 31085219 31142471 31142711 31157716 31570789 31583466\n 31584056 31584258 31813670 32024889 32067510 32111499 32349550 32359309\n 32359646 32432034 32567047 32576579 32588977 32589441 32779907 32912515\n 32912944 33195451 33491328 33648267 33654557 34085238 34236551 34421017\n 34573796 34676517 34721373 35463226 35463427 36048846 36202575 36264786\n 36459190 36514533 36519463 36519763 36519928 36520234 36602733 36662523\n 36807371 36812351 36812524 37000317 37005485 37066822 37089953 37494738\n 37495304 37500140 37772302 38360374 38432003 38461033 38461216 38539086\n 38779494 38819332 38931864 38951427 38963231 39757108 40130251 40263485\n 40313963 40448867 40455299 40545992 40715120 40715766 40901677 41120916\n 41316846 41412710 41639497 41639652 41703942 41704231 41812962 41821140\n 41852718 41878597 41879053 41879254 41940705 41968665 41971267 42052210\n 42052333 42052502 42080252 42135365 42144021 42145292 42796032 42886378\n 43010929 43057953 43249342 43582499 43607390 43675553 43675997 43905703\n 43928197 44306403 44486148 45372607 45930222 46018613 46018797 46237894\n 46333934 46353485 46355588 46355745 46422003 46424149 46457163 46482434\n 46488711 46685955 46876039 46886917 46896389 47270152 47401101 47799137\n 48025626 48270538 48392752 48602961 48702292 48843420 48902317 48915438\n 48921068 49133448 49160018 49162396 49231084 49323814 49391531 49468581\n 49614223 49666081 49701285 49704616 49923466 49927830 49928095 49932979\n 50364038 50498187 51205376 51349516 51818966 51947333 52076958 52118197\n 52118607 52119063 52122521 52127417 52127660 52129069 52132530 52186984\n 52315792 52349997 52563222 52769188 52978447 52979038 53993362 54029395\n 54034101 54586762 54593921 54768041 54768274 55050685 55051101 55226551\n 55676330 55911983 55920415 55933456 56095678 56097165 56205495 56236791\n 56237025 56244242 56312100 56322327 56324302 56331514 56558057 56631272\n 56652070 56653280 56856726 57064043 57130015 57402331 57734614 57740836\n 57748156 57750029 58493076 58553639 58560286 58821658 58868382 58880220\n 59237466 59415621 59430708 59502327 59589959 59590236 59830512 59830862\n 60371769 60372142 60381835 60382074 60932533 61023049 61188066 61190414\n 61197522 61199930 61213680 61450981 61457578 61458367 61479440 61483903\n 61866848 61877804 62052488 62157254 62324555 62359228 62490438 62665969\n 62711700 62711935 62715531 62907019 63102252 63308207 63308516 63311218\n 63656346 63688352 63897562 63897743 63945925 63946440 64121387 64251790\n 64307722 64309034 64310046 64315440 64453374 64535319 64566761 64873648\n 65032597 65216488 65218167 65469674 65478791 65484271 65493396 65495735\n 65924714 65965811 65986832 66179178 66684624 66892742 67094416 67533157\n 67533463 67549403 67549555 67550145 67556667 68050529 68050794 68242683\n 68242851 68243578 68427887]"], ["18", "125", "2411", "150.0", "[   56647    64946    65063 ... 74320729 74320937 74321255]"], ["19", "129", "5906", "156.0", "[   18647   101155   111726 ... 74721559 74899099 74956480]"], ["20", "130", "1362", "153.0", "[   11621    65180   627940 ... 61189581 61190780 61464910]"], ["21", "135", "905", "155.0", "[   79183    79317    87339    87458   111958   140755   140876   141006\n   141128   141283   143335   143475   143702   303127   303301   303556\n   303840   337453   440730   440869   441298   460610   460726   460913\n   461060   703036   703166   703323   703490   703610   738997   739243\n   739443   739604   739742   764749   764885   765218   767435   788662\n   788940   789141   940535   961559   961699   961886   962041   962182\n   980820   981009   981465  1112474  1112784  1112933  1113098  1113216\n  1227834  1228255  1232885  1400437  1427269  1427399  1427541  1427695\n  1427924  1543553  1543784  1585093  1585270  1585425  1585577  1624619\n  1624837  1624998  1716283  1716476  1716636  1717073  1975717  1975840\n  1975969  1976104  1976229  1978896  1979188  2114643  2114771  2115014\n  2115292  2184633  2184762  2185161  2252084  2252225  2252487  2252631\n  2252830  2252958  2319645  2319797  2319977  2320190  2400787  2400974\n  2401132  2401279  2401409  2435165  2435293  2435452  2435619  2435771\n  2467452  2467763  2486806  2486955  2487165  2487384  2727373  2727610\n  2727733  2730010  2730174  2730374  2732272  2732830  2738082  2738219\n  2738520  2739572  2739929  2740681  2741136  2742597  2744966  2745118\n  2747297  2752693  2752923  2753251  2759506  2759818  2760159  2761828\n  2762100  2762716  3054279  3054420  3054551  3054759  3054866  3056708\n  3056873  3057116  3059463  3059687  3060816  3063071  3063624  3064213\n  3065652  3068049  3069460  3088598  3088749  3088908  3089595  3094544\n  3286176  3286363  3286517  3610192  3610350  3610555  3610719  3610842\n  3767914  3842469  3842618  3842762  3842898  3843059  3846809  4190293\n  4199767  4250500  4424225  4424366  4424610  4424737  4424855  4427187\n  4427315  4427469  5108064  5108229  5108384  5108530  5108660  5108823\n  5287459  5403307  5536190  5648097  5648220  5648413  5648575  5648741\n  5812142  5812342  5812490  5812782  6041569  6041722  6041909  6042062\n  6042184  6042479  6063837  6176995  6177153  6177292  6177530  6209858\n  6447217  6447356  6447633  6661033  6661234  6661391  6814606  6899685\n  6899856  6900059  6900170  7201981  7318316  7512505  7512632  7512877\n  7659102  7936481  7992827  7997385  7997543  7997729  7997964  7998089\n  8060279  8060451  8060587  8060745  8060872  8125038  8135819  8173612\n  8178059  8178376  8178737  8516021  8516403  8516614  8518743  8528060\n  8528332  8528497  8924878  8987682  9010035  9010177  9010361  9016117\n  9016325  9181264  9181439  9181651  9248739  9264489  9264778  9264929\n  9265079  9265187  9339581  9339969  9364426  9364641  9473538  9526607\n  9526720  9526914  9527246  9801243  9801367  9801548  9882406  9882572\n  9882785 10056382 10093116 10093329 10093576 10132434 10132559 10132748\n 10144005 10144968 10149968 10150086 10150245 10321965 10322193 10322353\n 10350869 10351066 10351322 10506581 10586108 10586248 10623020 10623474\n 11126459 11138599 11154805 11155173 11177611 11178042 11436259 11436516\n 11436652 11436805 11437051 11437178 11437329 11495624 11495759 11495932\n 11496154 11496311 11497786 11498215 11498431 11498979 11500742 11501665\n 11502179 11502458 11503983 11504404 11506111 11506409 11508333 11509791\n 11511130 11511457 11513758 11514076 11514854 11516475 11518259 11518417\n 11520971 11524765 11525131 11528519 11532102 11532412 11534406 11535257\n 11536436 11541992 11542159 11542401 11545503 11545775 11546273 11546409\n 11549997 11552645 11552803 11553280 11557548 11562831 11565954 11566144\n 11567337 11567785 11571792 11573786 11574523 11578158 11579240 11579453\n 11581946 11586565 11587139 11588023 11593702 11593904 11594332 11598840\n 11599142 11603477 11605294 11605448 11608870 11609019 11610733 11612885\n 11613256 11623418 11623663 11623849 11970782 11970921 11971069 11971239\n 11971369 11972763 11972976 11973596 11977035 11977189 11977543 11979265\n 11979420 11982261 11982378 11982897 11987943 11988077 11988463 11990859\n 11991130 11992993 11994283 11998461 11998777 12001950 12002364 12006257\n 12006847 12007204 12016100 12016520 12017619 12018423 12019197 12022545\n 12023009 12025655 12033078 12033576 12151367 12331496 12331648 12390395\n 12390576 12390730 12390902 12391017 12403239 12411641 12411757 12411913\n 12412086 12412427 12440764 12440947 12441135 12453113 12453241 12453423\n 12465553 12465681 12466062 12466249 12466522 12467897 12468690 12468861\n 12469413 12470980 12471921 12473077 12476059 12476624 12477452 12481444\n 12481612 12482332 12485854 12486788 12488022 12488649 12493549 12493783\n 12494136 12495994 12496651 12500205 12500390 12501728 12506284 12506472\n 12510106 12510659 12512893 12514354 12517150 12517307 12517815 12518660\n 12519628 12520752 12523076 12523415 12523718 12526626 12527117 12529505\n 12530449 12533155 12535409 12535571 12539092 12539390 12539622 12546452\n 12546977 12547212 12551003 12551249 12555014 12555379 12560050 12562095\n 12562833 12567033 12567498 12571457 12571587 12572808 12573429 12581720\n 12581854 12582140 12582749 12591260 12591408 12591801 13008384 13008551\n 13217698 13217817 13217959 13218087 13218230 13286164 13286347 13286504\n 13336236 13336386 13336527 13336684 13337166 13337471 13405283 13405469\n 13405706 13405972 13406261 13406474 13556333 13584962 13585333 13585478\n 13937744 13937890 13938086 13938247 13938401 13976609 13976782 14164127\n 14164288 14164495 14164674 14166770 14166924 14250427 14251461 14476036\n 14476198 14476337 14476500 14476620 14586410 14691696 14691838 14692101\n 14868294 14868463 14869218 14918603 14918710 14918960 15041161 15067759\n 15067896 15068039 15068380 15068522 15127011 15127288 15127619 15127767\n 15127906 15128079 15128233 15408317 15579111 15579245 15579425 15579577\n 15579695 15765999 15766426 15783142 15783297 15783678 15785328 15785458\n 15803060 15879307 15879458 15879618 15947458 15947473 15984840 15984980\n 15985130 15985268 15985405 15985582 15987409 15987552 15987787 15989218\n 15990481 15990993 15992375 15993130 15993638 15995592 15995728 15999287\n 16001290 16007230 16007604 16008131 16015485 16015631 16015872 16016105\n 16022155 16022461 16028717 16028993 16029393 16039083 16039263 16048749\n 16048881 16049149 16049282 16053924 16054157 16054413 16059672 16059951\n 16072072 16072212 16072385 16072564 16123076 16123290 16123604 16144150\n 16351737 16381569 16381708 16381853 16382008 16382122 16384049 16384203\n 16384569 16385669 16385901 16387630 16388539 16394426 16394569 16398278\n 16398397 16400344 16400507 16400743 16401109 16404558 16404815 16405083\n 16405364 16406719 16412248 16412605 16412822 16413100 16413421 16420645\n 16420810 16425994 16427512 16428628 16428756 16428999 16434232 16434411\n 16434926 16438082 16438214 16459215 16459331 16459533 16459692 16782758\n 16782880 16783022 16783156 16898903 16899117 16899261 16899489 16899651\n 16899797 16950169 16963618 16963750 16963870 16964003 16964161 16965631\n 16965833 16966212 16967337 16969756 16970040 16970353 16971244 16971717\n 16974046 16974329 16974587 16975399 16976285 16978052 16981679 16981824\n 16982322 16983364 16983542 16985296 16987742 16989165 16992346 16992488\n 16992713 16995609 17008173 17008431 17008658 17014095 17014286 17038186\n 17579870 17632789 17633046 18074545 18074675 18074835 18096259 18096532\n 18470543 18470669 18470807 18888226 18888407 19466542 19522708 19522963\n 19523208 20018174 20137099 20137407 20137698 20143770 20143966 20144414\n 20267558 20267979 20268910 20345430 20591063 20591354 20856830 20902379\n 20902599 20902822 20902983 20905388 20905573 20912219 20915499 20915847\n 20916035 20916356 20918247 20920244 20920482 20921689 20933752 21216055\n 21216181 21245133 21262005 21570241 21716802 21866181 21866436 21866717\n 22695522 22695654 22695785 22695937 22696051 22702329 22702503 22702801\n 22798252 22798373 22798529 22798720 22798886 23000423 23262278 23740850\n 24018291 25146966 25963176 27217784 27519572 27787735 27788074 30380704\n 30381052 30381192 30653683 30980871 31387213 31701838 31702252 32348782\n 33646090 34093816 34171178 34293914 34577343 34577753 35024801 35488882\n 36335322 36719354 39601836 41632103 43058747 43680449 43782045 44239647\n 44386271 45363116 45482753 45751591 45751814 48933506 48933620 48933734\n 48933892 49166371 49656121 54338402 54338715 54597052 54913074 55931109\n 65491217]"], ["22", "136", "2921", "152.0", "[  120732   122049   179495 ... 70974955 73264244 73264690]"], ["23", "139", "7602", "152.0", "[    5862     6271    25882 ... 71404027 73962224 73963334]"], ["24", "141", "1855", "157.0", "[   63348    63488    74805 ... 65038198 65038328 65038567]"], ["25", "144", "17653", "156.0", "[     337     2324     2688 ... 74274901 74755647 74908940]"], ["26", "145", "961", "157.0", "[   93570   101773   125701   271200   662580  1159076  1839780  1839977\n  1840201  1962012  2083319  2163881  2385542  2385739  2385866  2532065\n  2532228  2532737  2670480  2732164  2732339  2733104  2780980  2902260\n  3046158  3351974  3802520  3846204  3846345  4346410  4761365  5538074\n  5744408  5812629  6179611  7469576  7939601  7939797  8167141  8681970\n  9016291  9016472  9079717  9533945  9801713 10022928 10103315 10988504\n 10988915 11527212 12533919 12833869 12834167 13901596 14692280 14885236\n 14993351 15080083 15094127 15126275 15126571 15126741 15126994 15344296\n 15352176 15352490 15352636 15352800 15408294 15484080 15487439 15532121\n 15553121 15555368 15759209 15972500 16686408 16801278 16804697 16977400\n 16977612 16977819 17038955 17039250 17053895 17056208 17233182 17233360\n 17233524 17234075 17234226 17234469 17797870 19002768 19002933 19003117\n 19410970 19411124 19411460 19471170 19482632 19706181 19706332 19706474\n 19713168 19713322 19713516 19759611 19920698 19920832 19920995 19921170\n 19921334 19924039 19924162 19924585 19924803 19925193 20056722 20056869\n 20056991 20057159 20144639 20146411 20166765 20169129 20212355 20263053\n 20263468 20263658 20295272 20533932 20678993 20679152 20679330 20900196\n 20900425 20900631 20900815 20918389 20918566 20918717 20918934 20956846\n 20967691 20980476 20980645 20980787 20981102 20981428 20981654 20997539\n 20997679 20997858 21261560 21262059 21262257 21358313 21358449 21358600\n 21554901 21555039 21555196 21555349 21556557 21556754 21713771 21802267\n 21872110 21872341 21966245 21966785 21966941 22058997 22059132 22059259\n 22059433 22059631 22111291 22247985 22248128 22248335 22360647 22360779\n 22360917 22361056 22365731 22365904 22427124 22549651 22549844 22549996\n 22551821 22551948 22552143 22610854 22679666 22687624 22687820 22821277\n 22821416 22821543 22821692 22828353 22828476 22828665 23128993 23129146\n 23129580 23131978 23132109 23132365 23132755 23133586 23262423 23386018\n 23420497 23428065 23428240 23451203 23592354 23592555 23593207 23712758\n 23715541 23715706 23715863 23716013 23718143 23740829 23740962 23741105\n 23766145 23887171 23887374 23887542 23891454 23891587 23891815 23974820\n 23974983 23975153 23975318 23993010 24018975 24165694 24165849 24166052\n 24166682 24166914 24167234 24167513 24240204 24240683 24240840 24241686\n 24360463 24360655 24394072 24394276 24431712 24439781 24651782 24651920\n 24652078 24661394 24661567 24661721 24662014 24662286 24821609 24853911\n 24854420 24854560 25078861 25096301 25096532 25096675 25096886 25098208\n 25121621 25121885 25122040 25122374 25158073 25158371 25158511 25169107\n 25301989 25302111 25302227 25302358 25311005 25311227 25311473 25311755\n 25328007 25328287 25328441 25395401 25395567 25395719 25416181 25472020\n 25564445 25564612 25564750 25564937 25565151 25652322 25652479 25657289\n 25657456 25670446 25695182 25695338 25696572 25696678 25696828 25697017\n 25811610 25945372 25945502 25953920 25995594 25995723 25995867 25996471\n 25997363 25997574 25997878 25999691 26069174 26069440 26069658 26239985\n 26403342 26456694 26457064 26457435 26457608 26844425 26844940 26845104\n 26939653 26939796 26939956 26940107 26952156 26952296 26952448 26952770\n 27097555 27097732 27097890 27098907 27101570 27101857 27103835 27133184\n 27133478 27133615 27382088 27382275 27382430 27382564 27382717 27398766\n 27398957 27399124 27399446 27400256 27400490 27400960 27401968 27402547\n 27403013 27519739 27520757 27615882 27616088 27629889 27958468 27963529\n 27967226 27967491 28018334 28018455 28042278 28042420 28043878 28206442\n 28320614 28437846 28438001 28438158 28493005 28554341 28615659 28615801\n 28616050 28756037 28979843 29267486 29741710 29742428 29832007 29972095\n 29974320 30153765 30351881 30376332 30469225 30469391 30469542 30632601\n 30654141 30675486 30678658 30678808 30797327 30980415 30980701 31008051\n 31008497 31083574 31083932 31157716 31158135 31158306 31353462 31368135\n 31368281 31368431 31368605 31401063 31759540 31942968 31943442 31978892\n 31979199 31979357 31979590 31979883 31980078 32107194 32116121 32116792\n 32343768 32348716 32567016 32567223 32569570 32892926 32912663 32926609\n 32926863 32937251 33390968 33391304 33391433 33391588 33391711 33501610\n 33506528 33508662 33671313 33671530 33671725 33671975 33959362 34424479\n 34581866 34725986 34726117 34726270 34766396 34874123 35245910 35248125\n 35469037 35711559 35998962 36093337 36093484 36093631 36237533 36296052\n 36296586 36808703 36808913 36809279 37501998 37665688 37766776 37774653\n 38031155 38031313 38031473 38725710 38821053 38821423 38888688 38888854\n 38889061 38944393 38962925 38963061 38963259 38977545 39604541 39739602\n 40097805 40098083 40124987 40203594 40203746 40449489 40452116 40452254\n 40455633 40549922 40884187 40921183 40921417 40921546 40922176 40922988\n 40923417 41010904 41405567 41416143 41512701 41523350 41586231 41790997\n 41791254 41791424 42098304 42144626 42144772 42185449 42185608 42185802\n 42185963 43007541 43056876 43057042 43057242 43092446 43092962 43093095\n 43093428 43686806 43762155 43762552 43762710 43969761 43970247 43970709\n 43996103 44005209 44005355 44005650 44019637 44266101 44318446 44386161\n 44803584 45134772 45134928 45135077 45269578 45271901 45421398 45422623\n 45441982 45630148 45729891 45819408 45824063 45824222 45824648 45824769\n 45824950 45825107 45846296 45846482 45847071 45847875 45848022 45855358\n 45855495 45856429 45945751 45945904 46025008 46025399 46263472 46263619\n 46263775 46264187 46330016 46334340 46418276 46470648 46470802 46471233\n 46482331 46482634 46867327 46873588 46873949 46874096 47046761 47049743\n 47049922 47377570 47377695 47378576 47397053 47402345 47569894 47607050\n 47607217 47607376 47607548 47607987 47681406 47735379 47762342 47762525\n 47762659 47808508 47808622 47940291 47986974 47987117 47987238 47987375\n 48033511 48033652 48033867 48309973 48310115 48310283 48310444 48381799\n 48381964 48382168 48455563 48466855 48466975 48571999 48572182 48572317\n 48572496 48687827 48703854 48732133 48815195 48857272 48857468 48857620\n 48857788 48857924 49177146 49177398 49177557 49177742 49182409 49182678\n 49182828 49185601 49185723 49267526 49267682 49310368 49341843 49341971\n 49342237 49342409 49349663 49349804 49350288 49518465 49518580 49518730\n 49518878 49519005 49523867 49524005 49524267 49624518 49656015 49669522\n 49669653 49669843 49684373 49684516 49685122 49685252 49685555 49685963\n 49686339 49691719 49864221 49864376 49864551 49864968 49865222 49866165\n 49866425 49866899 50164845 50164996 50165150 50165360 50165511 50232979\n 50233128 50254099 50326772 50488750 50488950 50492961 50498089 50498234\n 50498374 50646829 50647003 50647175 50647342 50647502 50870221 50870456\n 50870601 50904162 50904319 50926042 50926186 50926422 50937175 50937328\n 50937638 50937860 51047068 51115088 51115821 51116085 51116541 51162182\n 51162357 51162516 51185468 51185624 51214256 51214390 51214523 51214677\n 51218215 51409951 51410154 51410281 51537653 51537825 51537980 51538205\n 51538357 51538548 51548688 51549481 51549827 51557686 51559552 51581876\n 51841434 51868241 51870400 51924296 51924427 51959231 51959375 51959700\n 51959941 52010558 52010707 52010875 52011024 52011178 52015510 52016916\n 52017030 52017806 52018047 52018316 52030746 52119268 52119414 52123375\n 52130076 52161173 52161301 52174946 52175387 52175710 52176682 52208778\n 52208910 52209101 52315327 52315684 52319235 52335433 52335560 52335697\n 52336086 52385444 52385610 52439097 52439240 52439505 52439806 52440101\n 52522959 52547894 52548054 52548214 53125197 53125473 53138355 53138484\n 53139208 53145420 53145573 53424390 53424550 53424700 53424841 53442260\n 53442412 53499657 53562945 53563129 53591683 53591827 53591972 53592339\n 53629195 53629326 53629462 53629713 53709264 53709489 53811178 53894867\n 53895142 53895313 54009601 54012166 54025130 54025551 54060539 54147039\n 54160514 54160652 54160815 54160995 54162192 54162340 54162698 54162947\n 54332758 54333029 54333321 54333688 54386653 54541990 54564975 54565150\n 54565332 54565531 54567775 54567890 54684433 54687772 54785136 54785276\n 54785416 54785844 54915422 55457570 55457844 55457976 55458309 55458578\n 55465051 55486161 55486423 55486837 55486991 55678634 55678842 55679003\n 55679298 55766272 55901448 55901640 55921316 56157769 56157967 56158107\n 56158404 56158621 56236074 56323746 56323863 56518588 56518784 56518977\n 56631340 56631498 56631640 56631798 56631948 56637959 56638062 56825245\n 56825411 56825547 56825725 57053291 57053430 57054062 57059707 57418487\n 57418654 57418795 57786949 57787106 57787283 57811239 57814767 57815161\n 58156250 58157594 58745570 59591069 59622505 59773979 59775018 59775054\n 59775806 60120705 60120983 60354628 60939163 61866906 63598812 63598960\n 65483590]"], ["27", "147", "11906", "158.0", "[    3106    11809    12037 ... 74774713 74774924 74775209]"], ["28", "148", "25641", "156.0", "[    3123     6775    10321 ... 72831224 72966946 73357659]"], ["29", "151", "10187", "157.0", "[    2426    11408    11792 ... 69525823 69715792 69721503]"], ["30", "152", "10671", "159.0", "[   15503    34032    78902 ... 74775335 74776204 74781197]"], ["31", "167", "596", "183.0", "[   47404    55920    72091   107361   136242   171343   185507   196057\n   315826   333359   376789   437652   485853   513911   535332   535740\n   566569   582989   656079   709628   776872   792170   808785   894884\n   914061   915113   932409   951173   951658   968188  1003549  1021748\n  1063808  1078824  1107167  1110764  1141957  1165675  1183224  1184634\n  1235449  1251983  1270528  1271319  1297545  1299828  1315812  1332993\n  1333958  1346610  1351087  1367449  1400296  1425364  1426165  1443214\n  1474237  1496840  1512290  1514339  1562516  1563751  1580496  1615429\n  1636725  1637154  1637725  1641000  1667540  1667960  1724330  1734856\n  1793155  1874122  1915883  1936133  1955708  1972710  1989462  2031751\n  2049629  2050466  2098336  2110635  2157673  2221936  2241421  2259799\n  2281133  2282131  2285652  2288209  2328999  2371414  2418397  2436698\n  2463820  2475878  2497747  2511734  2550986  2587931  2594920  2636618\n  2660870  2695047  2704239  2730002  2750953  2794649  2828969  2850645\n  2867940  2889207  2921615  2922940  2933972  2957703  2978189  3037938\n  3062019  3068302  3070238  3074960  3087057  3123436  3149379  3163710\n  3179164  3196969  3209435  3210962  3246559  3269898  3352930  3355330\n  3386230  3393463  3410705  3436634  3439228  3458377  3483453  3489115\n  3491045  3511832  3519190  3537270  3538652  3552125  3640078  3640984\n  3688256  3704655  3716820  3729296  3749999  3755076  3778416  3836091\n  3846981  3863562  3883553  3894477  3988628  4011652  4025067  4057544\n  4111774  4114766  4180670  4185301  4188075  4202896  4225927  4232904\n  4252984  4289011  4312350  4328898  4353740  4452346  4499511  4519689\n  4578126  4605092  4625515  4662474  4707200  4725332  4747402  4814280\n  4883403  4953947  4977542  5019006  5022215  5045464  5105493  5122370\n  5127681  5146067  5165765  5182994  5193036  5221789  5243698  5252214\n  5261197  5272590  5276504  5309029  5445803  5475105  5520754  5537884\n  5538746  5648491  5649681  5676545  5810640  5834856  5862090  5883272\n  5900918  5989063  6130511  6161474  6162747  6230227  6231148  6270926\n  6303011  6345243  6428648  6443672  6444499  6511317  6515497  6521226\n  6537499  6553717  6555284  6580658  6602405  6628026  6639033  6654032\n  6685842  6809768  6827790  6833177  6849648  6850310  6884118  6897856\n  6899070  6915242  6924617  6938017  6939180  6952530  6954520  7050767\n  7073498  7095903  7109311  7152860  7182945  7219624  7222095  7242870\n  7244446  7256052  7258407  7273019  7544882  7635006  7717067  7718061\n  7721420  7730814  7745728  7785809  7799772  7800446  7812434  7817452\n  7944392  8125856  8141147  8142196  8164362  8237248  8249461  8299648\n  8345789  8356505  8424853  8475518  8513645  8514269  8530766  8532720\n  8588041  8663296  8680466  8699961  8713536  8730700  8756396  8792621\n  8848307  8869142  8870264  8996634  8998634  9006912  9027183  9124099\n  9159431  9181957  9187417  9191141  9194089  9206541  9210367  9210958\n  9239069  9252351  9308354  9330243  9333109  9352913  9359067  9382054\n  9400239  9401916  9459681  9495556  9512475  9556629  9595512  9639536\n  9662789  9802568  9820518  9835378 10091075 10357742 10385701 10443416\n 10597290 10693470 10752346 10820770 10982551 10989424 11113396 11117657\n 11131063 11316952 11332944 11502534 11505336 11583111 11583527 11598642\n 11602288 11616518 11617027 11619934 11631386 11646126 11733428 11768507\n 11843090 11996718 12018585 12182481 12197316 12258907 12500117 12624299\n 12635366 13076835 13121037 13187584 13223209 13322151 13369515 13398286\n 13432682 13613974 13676592 13835293 13903836 13908708 14016395 14088218\n 14190592 14202395 14233644 14307472 14329319 14480364 14570579 14921303\n 14956907 14970354 15063202 15087696 15089514 15411234 15713826 15790577\n 15795119 15817806 15818188 15834182 15901782 16147431 16165225 16197199\n 16293205 16303885 16304363 16324102 16349386 16386738 16591153 16719897\n 16720964 16884126 16911021 16914711 16943013 16976681 16984407 17012473\n 17114394 17246258 17287561 17288591 17377867 17622658 17738579 17742305\n 17819530 17999361 18035519 18119705 18138364 18355638 18409159 18543164\n 18556795 18557735 18695223 18781488 18932752 19016413 19027853 19072439\n 19378377 19393966 19412114 19435534 19530556 19724031 20129824 20167802\n 20192836 20315160 20449449 20639775 20747065 20752371 20803944 20903170\n 20925281 20946667 21089654 21105481 21222959 21480517 21495273 21512439\n 21736250 21742163 21795308 21825402 22250143 22302655 22314252 22333047\n 22742275 22938196 23015122 23284313 23284989 23496969 23571333 23592967\n 24318304 24628029 24844436 24987389 25031241 25158972 25193578 25298466\n 25562544 26481483 26823106 27194673 27266667 28169501 28435181 29519495\n 30012302 30076891 30975094 31236194 31989237 32107480 32759775 32968716\n 33108855 33192568 33593917 33663837 33677212 33779382 34163902 34361186\n 34414862 34655576 35166153 35664314 35792010 36009594 36227852 36236746\n 36290692 36577205 36875258 37246957 37417073 37770151 38240161 38682547\n 39389804 39717478 40033593 40353167 40531523 40666151 40811837 40879752\n 41475614 41709330 41743300 42165790 42377546 42433208 42850545 42907385\n 42977391 43188934 44677532 44804616 45074234 45363351 45996957 46118045\n 46245404 46428700 47315060 47981269 48232632 48475832 48998873 49780417\n 50005534 51624026 51644314 52606831 53068808 53515528 53594030 54689855\n 56796653 59728443 60115243 62289309]"], ["32", "170", "19051", "196.0", "[    3769     3839     3902 ... 74956996 74970443 74983680]"], ["33", "171", "15606", "201.0", "[    7731     7801     7930 ... 74876327 74941618 74977387]"], ["34", "172", "12997", "201.0", "[   11931    12030    13271 ... 73545044 74309143 74796665]"], ["35", "173", "20760", "200.0", "[    2592     2670     2745 ... 74965104 74988351 74999323]"], ["36", "174", "1746", "202.0", "[   12386    29649    44449 ... 73270272 73340579 73505774]"], ["37", "175", "5061", "205.0", "[   16507    16667    32765 ... 71792676 73217665 74415196]"], ["38", "176", "10607", "204.0", "[   36064    36139    36215 ... 74778950 74779012 74779092]"], ["39", "177", "553", "207.0", "[   61786    70924   348209   368714   428065   446955   454626   629266\n   671664   981112  1063615  1228677  1445182  1752734  1832369  1891267\n  1976423  2087057  2207364  2665743  2684022  2723294  3035275  3170461\n  3174341  3733690  3801070  3985509  4036636  4123568  4249784  4252243\n  4381776  4444978  4530320  4590025  4686393  4831495  4835428  4901832\n  4951182  4979979  5001861  5174999  5240220  5246795  5287325  5322880\n  5354651  5515674  5560986  5669337  5690617  5692098  5847195  5878529\n  5882494  5886093  5916369  5921879  5935848  5941512  6002129  6045233\n  6047570  6088574  6110704  6279463  6292529  6317355  6344148  6371070\n  6495520  6499373  6637695  6645211  6649371  6651999  6694848  6737662\n  6779907  6848101  6852234  6903692  6911709  6931073  6957713  6973613\n  6997866  7001326  7087101  7254339  7256963  7261116  7261852  7308039\n  7333868  7363592  7373687  7400119  7408866  7547400  7557524  7563037\n  7585344  7620143  7628735  7629228  7635173  7693449  7781367  7920382\n  8014793  8053124  8066242  8116354  8174339  8205798  8304682  8387459\n  8389944  8445501  8446624  8462237  8612662  8637779  8645086  8721004\n  8723924  8776236  8784903  8850329  8919015  8994655  9000754  9011886\n  9039769  9051878  9069358  9166004  9314790  9357872  9378644  9379524\n  9417316  9418899  9421817  9426856  9432288  9437735  9542363  9561126\n  9655529  9664463  9675099  9706743  9733791  9741701  9753068  9800061\n  9839639  9851166  9878504  9891671  9896885  9900144  9907486  9914762\n  9948527  9959411  9997946 10000147 10070401 10138408 10175718 10179024\n 10192618 10195480 10205083 10302297 10356080 10388369 10423403 10440857\n 10442488 10451947 10453353 10456535 10457258 10469612 10471065 10476000\n 10479618 10542502 10676293 10753684 10755087 10760268 10783069 10784145\n 10835722 10849290 10879922 10881753 10896932 10898030 10920380 10925232\n 10926484 10982924 11013155 11055126 11062020 11076197 11078854 11147449\n 11179155 11189421 11241166 11260644 11302174 11317722 11320822 11349790\n 11362136 11378538 11395535 11398363 11443818 11489342 11494824 11503776\n 11575792 11604423 11649019 11655974 11723275 11744269 11752122 11852034\n 11879016 11912384 11941502 11948191 11958914 11974380 11978910 11989885\n 11995939 12063154 12088696 12115147 12132942 12134082 12135632 12175064\n 12201355 12212941 12230683 12275339 12348701 12460407 12514631 12592551\n 12672949 12707748 12709694 12726164 12753997 12851134 12863594 12891076\n 12932647 13013918 13026701 13061882 13222638 13239902 13257317 13326663\n 13347694 13348956 13387199 13399232 13596127 13630352 13653425 13674827\n 13690037 13725201 13733384 13770412 13801690 13846734 14021139 14043303\n 14085868 14126045 14193694 14244699 14357466 14387518 14402855 14403689\n 14607578 14676342 14680617 14735628 14748380 14758966 14762251 14764346\n 14775871 14869641 14892572 14892795 14903747 15046753 15087391 15124000\n 15130647 15158550 15159732 15165179 15296874 15305163 15352147 15399689\n 15465431 15767362 15908661 16066611 16194999 16794804 17182896 17377911\n 17379228 17422480 17434874 17452455 17458913 17477246 17548716 17649331\n 17655749 17701310 17741053 17966369 17968077 17973557 18215442 18344213\n 18541440 18721144 18735301 19222477 19329032 20296064 20437580 20914682\n 20925270 21155998 21173039 22164960 22492535 22772080 23412633 24911326\n 25386280 25867787 26090140 26463835 26511794 26527766 26665700 26771463\n 26895297 27255802 27402909 27680334 28031222 28126903 28746268 28780149\n 28938142 29181249 29411628 29430093 29524916 30221958 30321292 30403578\n 30485415 30521868 30616003 31083231 31330998 31393549 32112538 33019709\n 33314982 33842290 33935255 34197806 34274053 34295671 34408798 34455381\n 34472675 34794493 34998750 35150317 35256579 35314636 35324019 35354229\n 35361729 36145111 36367558 36583644 36886911 36896306 37036687 37304979\n 37328836 37397143 37444785 37592590 37662640 37717309 37839498 37948503\n 37968339 38200776 38255328 38677835 39010294 39281778 40101621 40306554\n 40379496 40603626 40964593 41308980 41564589 41882915 41893358 41893961\n 41971318 42325048 42376450 42609078 42650284 42684963 43711657 43757584\n 43806393 44524057 44721622 44737826 45616455 45720614 45739553 46197039\n 46246028 46357477 47041730 47268988 47281695 47405326 48299529 48658728\n 48877874 49888710 49975610 50246084 50285993 50406176 50460558 50557598\n 50598729 51174474 51547435 51609097 52205618 52254940 52525718 52749977\n 52991038 53213013 53304967 53592315 54136933 54350709 54403049 54455183\n 54566411 54948967 56857031 56878977 57140893 57173986 57560869 57838137\n 58181548 58332159 58632135 58700288 58720869 58741776 59125649 59233177\n 59291930 59562238 59662213 59933120 59944506 60054481 60114232 60744263\n 60931152 61021827 61056997 61237453 61811026 61871834 62034631 62070987\n 62288467 62308569 62356804 62357054 62387044 62423946 62600232 62717608\n 62759796 62946002 63194134 64124917 64410832 64463665 64568834 65177857\n 65381749 65610518 66516809 67017382 67352650 68321874 68959499 69232852\n 69688469]"], ["40", "178", "4336", "211.0", "[  298564   347854   447687 ... 55161818 58492678 58622547]"], ["41", "179", "13665", "209.0", "[    7009     7083     7152 ... 72117941 72333649 72860530]"], ["42", "180", "228", "213.0", "[  102791   746941  1020332  1392972  6894413  7453647  7992867  8711586\n  9022358 10510405 10628990 10647669 10678415 11055122 11173178 12683279\n 13086753 13384330 13429405 13595193 13902723 14657189 15146558 15270005\n 15299751 15522539 15625240 16112513 19812865 26694514 28315144 28493869\n 29807855 30456828 33212952 37183817 38822719 39672269 42313066 43437981\n 46245861 71852059 72437634 72496871 72697434 72837271 72961029 73002892\n 73022888 73061832 73080772 73099794 73161167 73200866 73219983 73242712\n 73276527 73305213 73326105 73335560 73338904 73343778 73348495 73353941\n 73357480 73362695 73368418 73373205 73377575 73382926 73385638 73388730\n 73392710 73396155 73398695 73401816 73405191 73408723 73411417 73414509\n 73417734 73421019 73423573 73426896 73430000 73432884 73435962 73439169\n 73442065 73444679 73447885 73451256 73454082 73457085 73460368 73462859\n 73465957 73469351 73472121 73474847 73477870 73480727 73483437 73486238\n 73488637 73491637 73494906 73497855 73500673 73503438 73506318 73509307\n 73512582 73515559 73518675 73521722 73524736 73527757 73530769 73533652\n 73536425 73539337 73542290 73545346 73548187 73550933 73555319 73558910\n 73562496 73565795 73568903 73572307 73575573 73579099 73582794 73586729\n 73590525 73594012 73597542 73601006 73604893 73608522 73611576 73614718\n 73617708 73621121 73624755 73628233 73631730 73635362 73638876 73642549\n 73647013 73651282 73655239 73660775 73665112 73676689 73680680 73684511\n 73689022 73693551 73698379 73704109 73708199 73712837 73717346 73720856\n 73725148 73728816 73733771 73749225 73753866 73760681 73766449 73773243\n 73778890 73785892 73790894 73802411 73809381 73837519 73850881 73858344\n 73867766 73879064 73897021 73903871 73912238 73949839 73963154 73970681\n 73984402 73991087 74001405 74043826 74050424 74065444 74096389 74102718\n 74130002 74140853 74148007 74172513 74179453 74199062 74211474 74240614\n 74260403 74299199 74306338 74331055 74341216 74371707 74386075 74412157\n 74435210 74468814 74491228 74509051 74571487 74576044 74591135 74611768\n 74615385 74624111 74785496 74803541]"], ["43", "181", "274", "219.0", "[  250792   331578   397465   398353   578535   681898   927502   992661\n  1106165  1173757  1310330  1329919  1348307  1456214  1471265  1492967\n  1529650  1771618  1804118  1853618  1921568  2075368  2156466  2238582\n  2309691  2376468  2393434  2414087  2811949  2813083  2828326  2846155\n  2859062  3002192  3078701  3107136  3146622  3171737  3231103  3246995\n  3309410  3350797  3533322  3594225  3943525  4182889  4407221  4449751\n  4476578  4545099  4560346  4578925  4602782  4864671  4879871  4925080\n  5117781  5129076  5164741  5275373  5341447  5561258  5655884  5720398\n  5759248  5776504  5818737  5839284  5941705  6190841  6292939  6410303\n  6582062  6719937  7010296  7223285  7239488  7268819  7349395  7389132\n  7488878  7563271  7632474  7702105  7844548  7894995  7909610  7920028\n  8041813  8154249  8354344  8375656  8419424  8699392  8755138  9023326\n  9107526  9119256  9238841  9284022  9388794  9563097  9636287  9675133\n  9723907  9824949  9931002 10033531 10177462 10225070 10356288 10416486\n 10468571 10713157 10838004 10871170 10884000 11121793 11174080 11238554\n 11346892 11504758 11576192 11619010 11737137 11810133 12020560 12099203\n 12209907 12223368 12291351 12335449 12380399 12612709 12717164 12742338\n 12743441 12814787 12829311 12894550 12947919 12984298 12997617 13044705\n 13059231 13085913 13151794 13184916 13277196 13325351 13399576 13476405\n 13547516 13610770 13765859 13901502 13956310 14023418 14170626 14202227\n 14219793 14285871 14314125 14342349 14511965 14792547 14856404 14984283\n 15107363 15181796 15207221 15298137 15385530 15460291 15605826 15685646\n 15861759 15946258 16041255 16070293 16095223 16417460 16552321 16580842\n 16600941 16729746 16816321 16981449 17166017 17488368 17652055 18116949\n 18139488 18334638 18462412 18521912 18786997 18797716 18993940 19464151\n 20442855 21042522 21670648 21775718 22419845 22555548 22556637 22643816\n 22901253 23086830 23119975 23154010 23187348 23249951 23547343 23898365\n 24078918 24137357 24198233 24254218 24306636 24331764 24466538 24517818\n 24607224 24748139 24842771 24855837 25003416 25328099 25524737 25704668\n 26063521 26309151 26325507 26462894 27641505 27680228 27732786 27822361\n 28166537 28312533 28362383 28938212 29290872 29322282 29938670 29981753\n 30012559 30486748 30512692 30698410 31153245 31926954 32114479 33793255\n 34045520 34440805 34930502 34948292 38429906 39056007 39123746 39986413\n 40276926 40855691 41376618 42483005 43438463 44402602 45073991 45505970\n 56527992 64072518]"], ["44", "212", "21653", null, "[    3776     9627    38858 ... 74997249 74998114 74999816]"], ["45", "213", "910", null, "[  580586   775820  1072957  1158522  1713782  1714697  1715205  1775993\n  1881953  2204620  2204776  2532311  2569822  2735470  2736020  2736632\n  2736770  3000647  3001320  3639419  3805375  3806734  3832130  4339726\n  4349523  4349658  5532990  5957614  6131420  6210661  6248152  6558174\n  6840998  7298913  7403997  7423117  7603676  7720215  7937721  8013901\n  8170204  8785886  9483666  9485167  9491932  9492064 10579625 10579700\n 10863216 10874346 10897971 10967451 12818682 14405108 14585847 15662534\n 15817011 15928775 15930706 15931086 15947642 15948040 15952229 16121336\n 16222791 16223264 16255460 16255575 16352829 16664345 16664549 16817307\n 17793467 17830434 18142518 18174321 18834886 19115789 20003383 20169985\n 20170264 20341932 20453154 20462658 20463320 21021893 21022028 21252967\n 21371206 22782452 23141312 23270103 23980075 24018383 24431242 25258761\n 25470403 25470564 25698707 25800425 25945300 26401884 27598126 27686999\n 27687343 28970323 29449551 29504689 29511194 29918869 29919278 29963537\n 29964674 30276075 30276235 30276366 30421432 30649984 30818783 31357866\n 31357997 31393063 31403084 31403247 31403377 31540792 31550353 31550548\n 31550640 31601240 31622846 31623153 31750832 31750974 31751119 31765946\n 31768707 31784035 31865605 31904774 31960573 32067575 32079177 32108200\n 32116322 32126838 32291531 32313998 32355123 32590777 32752965 32753906\n 32767085 32767223 32773350 32918347 32918555 32918703 33205385 33211131\n 33324159 33651240 34273010 34572345 34575344 34580688 34580808 35211155\n 35469678 35469938 35821364 36191857 36193131 36248384 36584542 37043239\n 37494296 38148356 38148796 38379333 38379463 38390191 38395516 38396120\n 38396338 38397770 38419822 38512108 38538555 38836232 38923739 38924009\n 38924131 39078776 39548156 39548296 40130563 40131512 40131724 40139367\n 40306152 40437495 40458490 40679897 40690910 40691025 40705105 40723685\n 40723890 40939356 40987787 40987925 40997485 40997823 41028855 41239646\n 41239920 41410297 41411693 41411967 41433969 41965774 41966213 41966482\n 42141694 42143797 42282364 42541465 42541618 43680953 43793289 43793386\n 43793491 43793814 44084820 44121054 44124642 44240426 44273223 44832141\n 44855988 44918031 45056668 45057166 45057356 45351716 45351856 45377343\n 45377462 45377600 45590659 46093637 46094335 46094570 46245974 46342958\n 46356623 46356791 46356894 46409560 46481486 46488862 46491608 46514624\n 46618993 46667032 46667830 46688607 46789351 46812925 46864781 46873101\n 46873208 46886767 46919294 47104957 47105309 47105407 47113292 47116389\n 47403996 47586697 47694863 47755937 47799793 47837963 47871571 47992655\n 48025917 48039344 48049341 48050244 48143599 48143860 48143967 48144143\n 48144235 48144328 48190353 48273274 48273397 48273502 48350933 48393395\n 48393604 48394094 48580901 48589489 48589589 48593904 48634186 48635078\n 48636187 48710233 48724865 48724999 48725089 48726743 48727350 48736198\n 48846048 48852525 48856312 48905601 48905782 48921501 48925497 49097692\n 49097853 49232901 49244684 49245193 49335352 49394772 49437602 49438001\n 49438282 49621627 49621773 49621983 49622079 49700311 49702225 50012045\n 50026120 50513179 50707569 50707701 50707801 51867186 52055264 52062767\n 52074927 52132095 52145054 52296774 52303057 52322290 52336454 52336963\n 52517973 52567132 52928256 52945575 52976729 53040582 53151338 53163172\n 53163823 53164155 53344936 53450486 53501771 53618259 53708814 53709742\n 53751828 53806652 53881152 53889653 53951408 54021166 54021450 54226755\n 54227801 54241629 54268037 54337500 54337844 54444091 54494437 54494596\n 54495374 54495631 54495822 54589707 54596538 54596676 54717839 54718368\n 54718938 54804927 54825294 54884599 54923691 54999220 55069253 55189317\n 55224610 55251331 55373892 55374016 55386508 55387721 55388845 55500662\n 55716313 55922065 55922192 55922316 55922689 55922910 55930432 55933489\n 56166242 56171221 56171831 56210157 56210391 56210512 56211016 56234651\n 56258830 56318430 56327736 56327880 56327969 56389321 56467615 56565118\n 56666974 56667218 56667414 56668535 56680719 56701679 56716400 56722480\n 56744373 56860671 56862789 56868430 56868710 56868991 56870530 56879768\n 56882557 57040517 57041945 57060101 57060422 57063541 57303995 57315397\n 57349287 57354256 57354452 57402700 57412909 57417220 57419674 57425443\n 57450242 57545669 57574250 57595759 57600217 57618216 57656358 57719284\n 57719989 57746426 57746560 57746691 57788495 57791010 57812597 57854599\n 57865098 57927084 57927291 57927585 58019884 58020466 58025878 58156675\n 58235334 58237599 58238953 58255770 58261887 58307982 58309089 58312795\n 58506844 58560168 58560419 58578201 58579624 58581413 58861624 58861833\n 58870626 58879921 58890931 58892242 59007018 59323250 59345551 59345717\n 59378063 59500726 59598373 59605510 59609757 59665037 59665156 59989586\n 60111954 60157118 60171709 60320170 60408115 60429947 60430121 60430293\n 60430603 60492944 60493597 60603140 60835779 60883081 60883357 60942757\n 60942901 61070190 61070900 61071510 61072212 61156533 61158331 61182209\n 61190064 61193350 61193562 61242323 61293274 61294123 61356369 61381527\n 61454449 61454590 61454708 61456911 61457296 61466083 61481843 61593128\n 61598299 61709720 61711825 61838787 61843144 61847164 61847305 61847409\n 61874215 61883230 61883482 61883719 61883903 61883993 61884084 61884171\n 62012097 62012509 62139213 62147663 62166699 62166831 62167253 62218243\n 62259304 62322854 62335782 62345302 62447836 62457658 62458176 62468417\n 62468572 62534596 62535059 62704045 62732905 63098015 63098464 63098919\n 63108003 63108139 63297191 63297451 63320939 63350652 63474276 63531005\n 63541268 63541790 63608448 63610161 63630180 63692915 63694498 63703578\n 63830103 63852587 63870509 63870954 63948673 63967251 63968738 63969671\n 63981987 64133728 64235902 64248319 64253837 64261667 64270119 64350995\n 64447955 64450862 64451494 64451782 64523993 64524277 64533697 64538990\n 64543540 64634979 64647288 64662470 64663147 64683454 64683778 64684121\n 64888921 64936237 64936401 64936545 64938233 64938368 64950474 64964411\n 65017521 65034966 65036275 65044901 65045006 65084207 65084318 65108274\n 65108494 65112669 65112802 65147175 65167967 65168094 65168210 65240553\n 65242290 65262506 65266893 65267267 65309368 65309846 65374016 65374270\n 65375662 65379504 65412884 65413079 65417053 65450230 65454571 65469875\n 65473648 65490855 65492938 65493058 65493181 65493286 65498602 65502301\n 65544717 65544854 65551454 65551668 65551787 65761709 65868735 65879038\n 66174784 66296003 66405375 66405698 66406145 66521723 66541664 66547164\n 66560072 66681578 66685940 66695667 66700690 66712744 66713156 66713560\n 66744888 66745267 66823362 66836417 66839325 66937555 66944069 66947831\n 66954501 66954764 66955221 66974508 66974683 67034597 67124514 67125399\n 67210149 67212155 67223581 67427786 67442403 67443003 67499070 67529546\n 67529786 67549244 67561033 67561416 67745560 67745685 67746096 67746209\n 67767838 67768051 68103540 68114680 68114806 68122227 68122375 68167035\n 68167216 68200142 68200360 68200640 68427364 68428373 68639926 68695872\n 68703383 68703549 68703670 68708854 68726893 68851701 68916761 68916899\n 69071348 69071565 69090414 69091074 69171104 69186885 69254589 69254727\n 69255211 69900646 69908317 70039160 70039987 70199303 70199662 70223506\n 70360696 70360924 70446085 70446331 70470544 70545606 70556147 70556522\n 70901096 70906573 71001189 71001306 71001441 71126389 71231098 71231209\n 71344889 71345014 71394081 71547315 71572806 71680061 71800189 71813493\n 71825958 71831387 71831514 71847563 71849847 72147828 72191931 72192342\n 72196628 72280001 72285558 72300343 72337721 72338031 72342528 72342760\n 72342881 72452188 72596144 73126918 73127029 73147264 73147389 73329420\n 73329535 73329665 73578705 73578816 73578939 73652452 73652562 73652752\n 73798541 73798651 73798766 73800047 74013143 74013291 74062155 74062282\n 74062407 74235213 74235320 74241684 74241787 74305494 74305601 74305704\n 74376788 74376898 74376994 74406197 74406297 74406541 74438061 74438162\n 74438290 74457944 74473200 74473551 74547987 74548106 74575239 74575347\n 74580606 74676065 74676175 74676296 74713593 74713691 74759028 74782940\n 74783075 74792783 74792896 74820307 74820419 74836960 74837063 74837180\n 74885890 74885994 74886119 74909762 74909867 74909968]"], ["46", "214", "1105", null, "[   94606    94736   537798 ... 62836585 65492132 65492481]"], ["47", "215", "5792", null, "[   31737    31877    63539 ... 74754286 74784805 74784894]"], ["48", "217", "4877", null, "[   90424    92895    95008 ... 72270545 72271053 72272137]"]], "shape": {"columns": 4, "rows": 49}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>unit_id</th>\n", "      <th>spike_count</th>\n", "      <th>main_channel</th>\n", "      <th>spike_times</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>14</td>\n", "      <td>7762</td>\n", "      <td>9.0</td>\n", "      <td>[4310, 4419, 13430, 13516, 13591, 31918, 32002...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>19</td>\n", "      <td>10706</td>\n", "      <td>16.0</td>\n", "      <td>[2587, 13570, 13658, 13759, 32803, 32900, 3299...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35</td>\n", "      <td>4017</td>\n", "      <td>44.0</td>\n", "      <td>[684, 39468, 87918, 97175, 99364, 106121, 1064...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>36</td>\n", "      <td>1940</td>\n", "      <td>48.0</td>\n", "      <td>[87935, 130281, 148462, 157593, 256338, 258453...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>38</td>\n", "      <td>4940</td>\n", "      <td>55.0</td>\n", "      <td>[3133, 4026, 29529, 30257, 42976, 52344, 61841...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>41</td>\n", "      <td>1035</td>\n", "      <td>54.0</td>\n", "      <td>[98001, 104292, 104566, 137744, 364486, 601768...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>42</td>\n", "      <td>1715</td>\n", "      <td>52.0</td>\n", "      <td>[6293, 13423, 37878, 42646, 44096, 53027, 5409...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>48</td>\n", "      <td>15185</td>\n", "      <td>63.0</td>\n", "      <td>[204, 524, 1126, 16679, 19897, 27268, 27776, 3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>49</td>\n", "      <td>7755</td>\n", "      <td>61.0</td>\n", "      <td>[6058, 34740, 53950, 102793, 107564, 109503, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>53</td>\n", "      <td>4743</td>\n", "      <td>63.0</td>\n", "      <td>[4556, 4770, 5026, 14750, 18402, 22018, 22310,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>61</td>\n", "      <td>4859</td>\n", "      <td>75.0</td>\n", "      <td>[67675, 86276, 129911, 146333, 185822, 216340,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>68</td>\n", "      <td>687</td>\n", "      <td>78.0</td>\n", "      <td>[314242, 443989, 472234, 562057, 661743, 67096...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>75</td>\n", "      <td>4168</td>\n", "      <td>95.0</td>\n", "      <td>[61740, 66089, 75660, 80952, 89855, 124699, 13...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>86</td>\n", "      <td>12237</td>\n", "      <td>107.0</td>\n", "      <td>[1445, 6692, 18278, 19343, 22473, 28329, 33855...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>109</td>\n", "      <td>8178</td>\n", "      <td>146.0</td>\n", "      <td>[34141, 34407, 34809, 45255, 45935, 46613, 576...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>111</td>\n", "      <td>2824</td>\n", "      <td>147.0</td>\n", "      <td>[26229, 28325, 28716, 79151, 86691, 90704, 911...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>123</td>\n", "      <td>41878</td>\n", "      <td>148.0</td>\n", "      <td>[2392, 3172, 8478, 8704, 9784, 11273, 11460, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>124</td>\n", "      <td>675</td>\n", "      <td>150.0</td>\n", "      <td>[57957, 90351, 104529, 105211, 122698, 321231,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>125</td>\n", "      <td>2411</td>\n", "      <td>150.0</td>\n", "      <td>[56647, 64946, 65063, 65878, 66063, 66593, 669...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>129</td>\n", "      <td>5906</td>\n", "      <td>156.0</td>\n", "      <td>[18647, 101155, 111726, 125867, 167909, 194718...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>130</td>\n", "      <td>1362</td>\n", "      <td>153.0</td>\n", "      <td>[11621, 65180, 627940, 1585703, 1828570, 18288...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>135</td>\n", "      <td>905</td>\n", "      <td>155.0</td>\n", "      <td>[79183, 79317, 87339, 87458, 111958, 140755, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>136</td>\n", "      <td>2921</td>\n", "      <td>152.0</td>\n", "      <td>[120732, 122049, 179495, 333746, 613366, 61392...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>139</td>\n", "      <td>7602</td>\n", "      <td>152.0</td>\n", "      <td>[5862, 6271, 25882, 26195, 37211, 38497, 39251...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>141</td>\n", "      <td>1855</td>\n", "      <td>157.0</td>\n", "      <td>[63348, 63488, 74805, 74951, 75126, 77288, 779...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>144</td>\n", "      <td>17653</td>\n", "      <td>156.0</td>\n", "      <td>[337, 2324, 2688, 2983, 12238, 13289, 18727, 2...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>145</td>\n", "      <td>961</td>\n", "      <td>157.0</td>\n", "      <td>[93570, 101773, 125701, 271200, 662580, 115907...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>147</td>\n", "      <td>11906</td>\n", "      <td>158.0</td>\n", "      <td>[3106, 11809, 12037, 13463, 13589, 13815, 5929...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>148</td>\n", "      <td>25641</td>\n", "      <td>156.0</td>\n", "      <td>[3123, 6775, 10321, 11734, 13564, 15950, 18717...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>151</td>\n", "      <td>10187</td>\n", "      <td>157.0</td>\n", "      <td>[2426, 11408, 11792, 56812, 65013, 67363, 6946...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>152</td>\n", "      <td>10671</td>\n", "      <td>159.0</td>\n", "      <td>[15503, 34032, 78902, 80946, 88678, 89718, 965...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>167</td>\n", "      <td>596</td>\n", "      <td>183.0</td>\n", "      <td>[47404, 55920, 72091, 107361, 136242, 171343, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>170</td>\n", "      <td>19051</td>\n", "      <td>196.0</td>\n", "      <td>[3769, 3839, 3902, 3982, 12733, 12805, 12877, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>171</td>\n", "      <td>15606</td>\n", "      <td>201.0</td>\n", "      <td>[7731, 7801, 7930, 18729, 18813, 32021, 32091,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>172</td>\n", "      <td>12997</td>\n", "      <td>201.0</td>\n", "      <td>[11931, 12030, 13271, 31009, 31186, 56769, 568...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>173</td>\n", "      <td>20760</td>\n", "      <td>200.0</td>\n", "      <td>[2592, 2670, 2745, 12662, 12739, 12812, 23010,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>174</td>\n", "      <td>1746</td>\n", "      <td>202.0</td>\n", "      <td>[12386, 29649, 44449, 101144, 148987, 149087, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>175</td>\n", "      <td>5061</td>\n", "      <td>205.0</td>\n", "      <td>[16507, 16667, 32765, 32856, 44410, 63595, 735...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>176</td>\n", "      <td>10607</td>\n", "      <td>204.0</td>\n", "      <td>[36064, 36139, 36215, 36360, 61777, 61868, 771...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>177</td>\n", "      <td>553</td>\n", "      <td>207.0</td>\n", "      <td>[61786, 70924, 348209, 368714, 428065, 446955,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>178</td>\n", "      <td>4336</td>\n", "      <td>211.0</td>\n", "      <td>[298564, 347854, 447687, 579768, 613320, 80444...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>179</td>\n", "      <td>13665</td>\n", "      <td>209.0</td>\n", "      <td>[7009, 7083, 7152, 18069, 18144, 18222, 32278,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>180</td>\n", "      <td>228</td>\n", "      <td>213.0</td>\n", "      <td>[102791, 746941, 1020332, 1392972, 6894413, 74...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>181</td>\n", "      <td>274</td>\n", "      <td>219.0</td>\n", "      <td>[250792, 331578, 397465, 398353, 578535, 68189...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>212</td>\n", "      <td>21653</td>\n", "      <td>NaN</td>\n", "      <td>[3776, 9627, 38858, 39675, 40357, 46756, 47668...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>213</td>\n", "      <td>910</td>\n", "      <td>NaN</td>\n", "      <td>[580586, 775820, 1072957, 1158522, 1713782, 17...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>214</td>\n", "      <td>1105</td>\n", "      <td>NaN</td>\n", "      <td>[94606, 94736, 537798, 537975, 538091, 538231,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>215</td>\n", "      <td>5792</td>\n", "      <td>NaN</td>\n", "      <td>[31737, 31877, 63539, 63633, 74919, 203117, 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>217</td>\n", "      <td>4877</td>\n", "      <td>NaN</td>\n", "      <td>[90424, 92895, 95008, 109640, 122926, 189808, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    unit_id  spike_count  main_channel  \\\n", "0        14         7762           9.0   \n", "1        19        10706          16.0   \n", "2        35         4017          44.0   \n", "3        36         1940          48.0   \n", "4        38         4940          55.0   \n", "5        41         1035          54.0   \n", "6        42         1715          52.0   \n", "7        48        15185          63.0   \n", "8        49         7755          61.0   \n", "9        53         4743          63.0   \n", "10       61         4859          75.0   \n", "11       68          687          78.0   \n", "12       75         4168          95.0   \n", "13       86        12237         107.0   \n", "14      109         8178         146.0   \n", "15      111         2824         147.0   \n", "16      123        41878         148.0   \n", "17      124          675         150.0   \n", "18      125         2411         150.0   \n", "19      129         5906         156.0   \n", "20      130         1362         153.0   \n", "21      135          905         155.0   \n", "22      136         2921         152.0   \n", "23      139         7602         152.0   \n", "24      141         1855         157.0   \n", "25      144        17653         156.0   \n", "26      145          961         157.0   \n", "27      147        11906         158.0   \n", "28      148        25641         156.0   \n", "29      151        10187         157.0   \n", "30      152        10671         159.0   \n", "31      167          596         183.0   \n", "32      170        19051         196.0   \n", "33      171        15606         201.0   \n", "34      172        12997         201.0   \n", "35      173        20760         200.0   \n", "36      174         1746         202.0   \n", "37      175         5061         205.0   \n", "38      176        10607         204.0   \n", "39      177          553         207.0   \n", "40      178         4336         211.0   \n", "41      179        13665         209.0   \n", "42      180          228         213.0   \n", "43      181          274         219.0   \n", "44      212        21653           NaN   \n", "45      213          910           NaN   \n", "46      214         1105           NaN   \n", "47      215         5792           NaN   \n", "48      217         4877           NaN   \n", "\n", "                                          spike_times  \n", "0   [4310, 4419, 13430, 13516, 13591, 31918, 32002...  \n", "1   [2587, 13570, 13658, 13759, 32803, 32900, 3299...  \n", "2   [684, 39468, 87918, 97175, 99364, 106121, 1064...  \n", "3   [87935, 130281, 148462, 157593, 256338, 258453...  \n", "4   [3133, 4026, 29529, 30257, 42976, 52344, 61841...  \n", "5   [98001, 104292, 104566, 137744, 364486, 601768...  \n", "6   [6293, 13423, 37878, 42646, 44096, 53027, 5409...  \n", "7   [204, 524, 1126, 16679, 19897, 27268, 27776, 3...  \n", "8   [6058, 34740, 53950, 102793, 107564, 109503, 1...  \n", "9   [4556, 4770, 5026, 14750, 18402, 22018, 22310,...  \n", "10  [67675, 86276, 129911, 146333, 185822, 216340,...  \n", "11  [314242, 443989, 472234, 562057, 661743, 67096...  \n", "12  [61740, 66089, 75660, 80952, 89855, 124699, 13...  \n", "13  [1445, 6692, 18278, 19343, 22473, 28329, 33855...  \n", "14  [34141, 34407, 34809, 45255, 45935, 46613, 576...  \n", "15  [26229, 28325, 28716, 79151, 86691, 90704, 911...  \n", "16  [2392, 3172, 8478, 8704, 9784, 11273, 11460, 1...  \n", "17  [57957, 90351, 104529, 105211, 122698, 321231,...  \n", "18  [56647, 64946, 65063, 65878, 66063, 66593, 669...  \n", "19  [18647, 101155, 111726, 125867, 167909, 194718...  \n", "20  [11621, 65180, 627940, 1585703, 1828570, 18288...  \n", "21  [79183, 79317, 87339, 87458, 111958, 140755, 1...  \n", "22  [120732, 122049, 179495, 333746, 613366, 61392...  \n", "23  [5862, 6271, 25882, 26195, 37211, 38497, 39251...  \n", "24  [63348, 63488, 74805, 74951, 75126, 77288, 779...  \n", "25  [337, 2324, 2688, 2983, 12238, 13289, 18727, 2...  \n", "26  [93570, 101773, 125701, 271200, 662580, 115907...  \n", "27  [3106, 11809, 12037, 13463, 13589, 13815, 5929...  \n", "28  [3123, 6775, 10321, 11734, 13564, 15950, 18717...  \n", "29  [2426, 11408, 11792, 56812, 65013, 67363, 6946...  \n", "30  [15503, 34032, 78902, 80946, 88678, 89718, 965...  \n", "31  [47404, 55920, 72091, 107361, 136242, 171343, ...  \n", "32  [3769, 3839, 3902, 3982, 12733, 12805, 12877, ...  \n", "33  [7731, 7801, 7930, 18729, 18813, 32021, 32091,...  \n", "34  [11931, 12030, 13271, 31009, 31186, 56769, 568...  \n", "35  [2592, 2670, 2745, 12662, 12739, 12812, 23010,...  \n", "36  [12386, 29649, 44449, 101144, 148987, 149087, ...  \n", "37  [16507, 16667, 32765, 32856, 44410, 63595, 735...  \n", "38  [36064, 36139, 36215, 36360, 61777, 61868, 771...  \n", "39  [61786, 70924, 348209, 368714, 428065, 446955,...  \n", "40  [298564, 347854, 447687, 579768, 613320, 80444...  \n", "41  [7009, 7083, 7152, 18069, 18144, 18222, 32278,...  \n", "42  [102791, 746941, 1020332, 1392972, 6894413, 74...  \n", "43  [250792, 331578, 397465, 398353, 578535, 68189...  \n", "44  [3776, 9627, 38858, 39675, 40357, 46756, 47668...  \n", "45  [580586, 775820, 1072957, 1158522, 1713782, 17...  \n", "46  [94606, 94736, 537798, 537975, 538091, 538231,...  \n", "47  [31737, 31877, 63539, 63633, 74919, 203117, 20...  \n", "48  [90424, 92895, 95008, 109640, 122926, 189808, ...  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["folder = r\"Z:\\users\\izouridis\\test_sorting_npx2\\b11\\b11_p1_r1_g0\\b11_p1_r1_g0_imec0\\kilosort4\"\n", "df_good_units = load_good_units_with_main_channel_df(folder, min_spikes=100)\n", "df_good_units\n"]}, {"cell_type": "code", "execution_count": 21, "id": "4f4f54d8", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "object", "type": "string"}], "ref": "4fe97717-363d-4d8d-945a-f74b29260ed2", "rows": [["unit_id", "int64"], ["spike_count", "int64"], ["main_channel", "float64"], ["spike_times", "object"]], "shape": {"columns": 1, "rows": 4}}, "text/plain": ["unit_id           int64\n", "spike_count       int64\n", "main_channel    float64\n", "spike_times      object\n", "dtype: object"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_good_units.dtypes"]}, {"cell_type": "code", "execution_count": 22, "id": "4f5897b1", "metadata": {}, "outputs": [], "source": ["df_good_units['spike_times_sec'] = df_good_units.spike_times / 30000"]}, {"cell_type": "code", "execution_count": 23, "id": "f57fa0a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BinaryRecordingExtractor: 14 channels - 30.3kHz - 1 segments - 84,192,900 samples \n", "                          2,778.37s (46.31 minutes) - int16 dtype - 2.20 GiB\n", "  file_paths: ['Z:\\\\users\\\\izouridis\\\\test_sorting_npx2\\\\b11\\\\b11_p1_r1_g0\\\\b11_p1_r1_g0_t0.obx0.obx.bin']\n", "Channels: 14\n", "Sampling frequency: 30303.0 Hz\n", "Duration: 2778.37 seconds\n"]}], "source": ["import spikeinterface.extractors as se\n", "\n", "binary_file = r\"Z:\\users\\izouridis\\test_sorting_npx2\\b11\\b11_p1_r1_g0\\b11_p1_r1_g0_t0.obx0.obx.bin\"\n", "\n", "recording = se.BinaryRecordingExtractor(\n", "    file_paths=[binary_file],   # note: this expects a list of files\n", "    sampling_frequency=30303,\n", "    num_channels=14,\n", "    dtype='int16',\n", "    time_axis=0\n", ")\n", "\n", "print(recording)\n", "print(f\"Channels: {recording.get_num_channels()}\")\n", "print(f\"Sampling frequency: {recording.get_sampling_frequency()} Hz\")\n", "print(f\"Duration: {recording.get_total_duration():.2f} seconds\")"]}, {"cell_type": "code", "execution_count": 24, "id": "d2094166", "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import spikeinterface.preprocessing as spre\n", "import numpy as np\n", "\n", "# Slice channel 1\n", "recording_ch1 = recording.channel_slice(channel_ids=[1])\n", "\n", "# Filter channel 1 (0.01 Hz high-pass)\n", "filtered_ch1 = spre.highpass_filter(recording_ch1, freq_min=0.002)\n", "\n", "# Get traces\n", "raw_trace = recording_ch1.get_traces()[:, 0]\n", "filt_trace = filtered_ch1.get_traces()[:, 0]\n", "\n", "# Time axis\n", "fs = recording.get_sampling_frequency()\n", "num_frames = recording.get_num_frames()\n", "time_axis = np.arange(num_frames) / fs\n", "\n", "# Plot raw vs. filtered\n", "plt.figure(figsize=(15, 5))\n", "plt.plot(time_axis, raw_trace, label='Raw Channel 1', color='gray', linewidth=0.5)\n", "plt.plot(time_axis, filt_trace, label='Filtered Channel 1 (0.01 Hz HP)', color='green', linewidth=0.5)\n", "\n", "plt.xlabel(\"Time (s)\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"Raw vs. Filtered (0.01 Hz HP) Channel 1\")\n", "plt.legend(loc=\"upper right\")\n", "plt.tight_layout()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}